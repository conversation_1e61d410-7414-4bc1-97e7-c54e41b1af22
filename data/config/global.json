{"vfs": {"mount": [{"path": "data/core", "mountPoint": ""}]}, "renderer": {"shaders": [{"name": "default", "vertex": "shaders/default.vs", "fragment": "shaders/default.fs"}, {"name": "text", "vertex": "shaders/text.vs", "fragment": "shaders/text.fs"}, {"name": "gui", "vertex": "shaders/gui.vs", "fragment": "shaders/gui.fs"}]}, "scripts": {"application": {"init": ["scripts/init.lua"], "perFrame": ["scripts/update.lua"]}}}