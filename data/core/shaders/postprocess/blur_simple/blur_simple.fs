#version 330 core
out vec4 FragColor;
  
in vec2 TexCoords;

uniform sampler2D screenTexture;
uniform vec2 windowSize;

void main()
{ 
    vec2 texOffset = 1.0 / windowSize;
    vec3 color = texture(screenTexture, TexCoords).rgb * 0.2;

    color += texture(screenTexture, TexCoords + vec2(texOffset.x, 0.0)).rgb * 0.15;
    color += texture(screenTexture, TexCoords - vec2(texOffset.x, 0.0)).rgb * 0.15;
    color += texture(screenTexture, TexCoords + vec2(0.0, texOffset.y)).rgb * 0.15;
    color += texture(screenTexture, TexCoords - vec2(0.0, texOffset.y)).rgb * 0.15;

    color += texture(screenTexture, TexCoords + texOffset).rgb * 0.1;
    color += texture(screenTexture, TexCoords - texOffset).rgb * 0.1;
    color += texture(screenTexture, TexCoords + vec2(texOffset.x, -texOffset.y)).rgb * 0.1;
    color += texture(screenTexture, TexCoords - vec2(texOffset.x, -texOffset.y)).rgb * 0.1;

    FragColor = vec4(color, 1.0);
}
