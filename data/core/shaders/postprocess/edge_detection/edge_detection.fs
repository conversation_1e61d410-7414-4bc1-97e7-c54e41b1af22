#version 330 core
out vec4 FragColor;
  
in vec2 TexCoords;

uniform sampler2D screenTexture;
uniform vec2 windowSize; 

void main()
{ 
    vec2 texOffset = 1.0 / windowSize;

    mat3 sobelX = mat3(
        -1.0, 0.0, 1.0,
        -2.0, 0.0, 2.0,
        -1.0, 0.0, 1.0
    );
    mat3 sobelY = mat3(
        -1.0, -2.0, -1.0,
        0.0, 0.0, 0.0,
        1.0, 2.0, 1.0
    );

    vec3 color = vec3(0.0);
    for (int x = -1; x <= 1; x++) {
        for (int y = -1; y <= 1; y++) {
            color += texture(screenTexture, TexCoords + vec2(x, y) * texOffset).rgb * sobelX[x+1][y+1];
        }
    }

    vec3 edgeX = vec3(0.0);
    vec3 edgeY = vec3(0.0);
    for (int x = -1; x <= 1; x++) {
        for (int y = -1; y <= 1; y++) {
            edgeX += texture(screenTexture, TexCoords + vec2(x, y) * texOffset).rgb * sobelX[x+1][y+1];
            edgeY += texture(screenTexture, TexCoords + vec2(x, y) * texOffset).rgb * sobelY[x+1][y+1];
        }
    }

    vec3 edges = sqrt(edgeX * edgeX + edgeY * edgeY);
    float intensity = length(edges);

    float threshold = 0.1;
    if (intensity > threshold) {
        FragColor = vec4(vec3(0.0), 1.0);
    } else {
        FragColor = vec4(vec3(1.0), 1.0);
    }
}
