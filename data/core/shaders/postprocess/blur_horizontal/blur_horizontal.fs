#version 330 core
out vec4 FragColor;
  
in vec2 TexCoords;

uniform sampler2D screenTexture;
uniform vec2 windowSize;

const float weight[5] = float[](0.227027, 0.1945946, 0.1216216, 0.054054, 0.016216); 

void main()
{ 
    vec2 texOffset = vec2(1.0 / windowSize.x, 0.0);
    vec3 result = texture(screenTexture, TexCoords).rgb * weight[0];

    for (int i = 1; i < 5; ++i) {
        result += texture(screenTexture, TexCoords + texOffset * i).rgb * weight[i];
        result += texture(screenTexture, TexCoords - texOffset * i).rgb * weight[i];
    }

    FragColor = vec4(result, 1.0);
}
