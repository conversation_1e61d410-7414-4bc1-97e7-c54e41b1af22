#version 330 core
out vec4 FragColor;
  
in vec2 TexCoords;

uniform sampler2D screenTexture;
uniform vec2 windowSize; 
uniform float blockSize;

void main()
{ 
    vec2 blockSize = blockSize / windowSize;

    vec2 pixelatedUV = vec2(
        floor(TexCoords.x / blockSize.x) * blockSize.x,
        floor(TexCoords.y / blockSize.y) * blockSize.y
    );

    vec3 color = texture(screenTexture, pixelatedUV).rgb;
    
    FragColor = vec4(color, 1.0);
}
