#version 330 core

in vec2 TexCoords;
out vec4 FragColor;

uniform sampler2D screenTexture;
uniform vec2 windowSize;
uniform float aberrationAmount;

void main() {
    vec2 uv = TexCoords;

    vec3 color;
    color.r = texture(screenTexture, uv + aberrationAmount * vec2(0.002, 0.0)).r;
    color.g = texture(screenTexture, uv + aberrationAmount * vec2(0.0, 0.002)).g;
    color.b = texture(screenTexture, uv - aberrationAmount * vec2(0.002, 0.002)).b;
    
    FragColor = vec4(color, 1.0);
}
