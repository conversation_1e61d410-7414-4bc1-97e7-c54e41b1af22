#version 330 core
layout (location = 0) in vec3 aPos;
layout (location = 1) in vec2 aTexCoord;
layout (location = 2) in vec3 aNormal;
layout (location = 3) in vec3 aTangent;
layout (location = 4) in vec3 aBitangent;

out vec3 Tangent;

uniform mat4 model;
uniform mat4 viewProjection;
uniform mat3 normalMatrix;

void main()
{
    vec4 worldPos = model * vec4(aPos, 1.0);

    Tangent = normalize(normalMatrix * aTangent);
    
    gl_Position = viewProjection * worldPos;
} 
