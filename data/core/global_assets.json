{"meshes": [{"name": "mesh::triangle", "type": "primitive", "primitive": "triangle"}, {"name": "mesh::quad", "type": "primitive", "primitive": "quad"}, {"name": "mesh::cube", "type": "primitive", "primitive": "cube"}, {"name": "mesh::cone", "type": "primitive", "primitive": "cone"}, {"name": "mesh::cylinder", "type": "primitive", "primitive": "cylinder", "segments": 16}, {"name": "mesh::sphere", "type": "primitive", "primitive": "sphere", "lat-bands": 16, "long-bands": 16}], "materials": [{"name": "material::pbr::tiles", "type": "pbr", "baseColor": "materials/tiles/Tiles012_1K-PNG_Color.png", "normal": "materials/tiles/Tiles012_1K-PNG_NormalGL.png", "roughness": "materials/tiles/Tiles012_1K-PNG_Roughness.png"}], "shaders": [{"name": "shader::debug", "path": "shaders/debug/debug"}, {"name": "shader::normals", "path": "shaders/normals/normals"}, {"name": "shader::tangents", "path": "shaders/tangents/tangents"}, {"name": "shader::bitangents", "path": "shaders/bitangents/bitangents"}, {"name": "shader::blinn-phong", "path": "shaders/blinn-phong/blinn-phong"}, {"name": "shader::pbr", "path": "shaders/pbr/pbr"}, {"name": "shader::postprocess::black-and-white", "path": "shaders/postprocess/black_and_white/black_and_white"}, {"name": "shader::postprocess::chromatic-aberration", "path": "shaders/postprocess/chromatic_aberration/chromatic_aberration"}, {"name": "shader::postprocess::blur-simple", "path": "shaders/postprocess/blur_simple/blur_simple"}, {"name": "shader::postprocess::blur-vertical", "path": "shaders/postprocess/blur_vertical/blur_vertical"}, {"name": "shader::postprocess::blur-horizontal", "path": "shaders/postprocess/blur_horizontal/blur_horizontal"}, {"name": "shader::postprocess::default", "path": "shaders/postprocess/default/postprocess_default"}, {"name": "shader::postprocess::edge-detection", "path": "shaders/postprocess/edge_detection/edge_detection"}, {"name": "shader::postprocess::invert", "path": "shaders/postprocess/invert/invert"}, {"name": "shader::postprocess::pixelation", "path": "shaders/postprocess/pixelation/pixelation"}, {"name": "shader::postprocess::sepia", "path": "shaders/postprocess/sepia/sepia"}], "postprocess": [{"name": "postprocess::black-and-white", "shaders": ["shader::postprocess::black-and-white"]}, {"name": "postprocess::blur-simple", "shaders": ["shader::postprocess::blur-simple"]}, {"name": "postprocess::blur-gaussian", "shaders": ["shader::postprocess::blur-horizontal", "shader::postprocess::blur-vertical"]}, {"name": "postprocess::chromatic-aberration", "shaders": ["shader::postprocess::chromatic-aberration"]}, {"name": "postprocess::default", "shaders": ["shader::postprocess::default"]}, {"name": "postprocess::edge-detection", "shaders": ["shader::postprocess::edge-detection"]}, {"name": "postprocess::invert", "shaders": ["shader::postprocess::invert"]}, {"name": "postprocess::pixelation", "shaders": ["shader::postprocess::pixelation"]}, {"name": "postprocess::sepia", "shaders": ["shader::postprocess::sepia"]}, {"name": "postprocess::test", "shaders": ["shader::postprocess::pixelation", "shader::postprocess::black-and-white"]}], "fonts": [{"name": "font::console-72", "path": "fonts/RedHatMono.ttf", "size": 72}, {"name": "font::console-36", "path": "fonts/RedHatMono.ttf", "size": 36}], "audio": [{"name": "audio::music", "path": "audio/harp.ogg"}]}