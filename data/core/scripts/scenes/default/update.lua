-- Default scene per-frame update script
-- This script runs every frame while the default scene is active
-- Modules are loaded and initialized in init.lua

-- Update all modules (modules are global variables set in init.lua)
if scene and window and CameraControls and GUIInteractions and DebugOutput then
  CameraControls.update(scene, deltaTime, window)
  GUIInteractions.update(scene)
  DebugOutput.update(scene, deltaTime)
end

update()
