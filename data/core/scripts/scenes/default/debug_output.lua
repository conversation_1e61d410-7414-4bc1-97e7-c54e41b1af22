-- Debug Output Module
-- Handles frame counting and debug logging

local DebugOutput = {}

-- Initialize debug output state
function DebugOutput.init()
  DebugOutput.frameCount = 0
  DebugOutput.totalTime = 0
end

-- Update debug output
function DebugOutput.update(scene, deltaTime)
  DebugOutput.frameCount = DebugOutput.frameCount + 1
  DebugOutput.totalTime = DebugOutput.totalTime + (deltaTime or 0)

  -- Debug output every 300 frames (roughly every 5 seconds at 60 FPS)
  if DebugOutput.frameCount % 300 == 0 then
    DebugOutput.logSceneInfo(scene)
  end
end

-- Log scene information
function DebugOutput.logSceneInfo(scene)
  print("Scene '" .. (sceneName or "unknown") .. "' frame " .. DebugOutput.frameCount ..
        " - objects: " .. (sceneObjectCount or 0) ..
        " - deltaTime: " .. string.format("%.3f", deltaTime or 0) ..
        " - totalTime: " .. string.format("%.1f", DebugOutput.totalTime) .. "s")

  if scene then
    print("Scene object is available (this scene's object)")
    print("Scene name: " .. scene:getSceneName():toString())
  else
    print("Warning: Scene object not available")
  end
end

-- Get current frame count (for other modules that might need it)
function DebugOutput.getFrameCount()
  return DebugOutput.frameCount
end

-- Get total time (for other modules that might need it)
function DebugOutput.getTotalTime()
  return DebugOutput.totalTime
end

return DebugOutput
