-- Default scene initialization script
-- This script runs once when the default scene is loaded

print("Default scene init script loaded!")

-- Load modules using VFS-based require (only once per scene)
-- Shared modules (reusable across scenes)
CameraControls = require("scripts/shared/camera_controls")
DebugControls = require("scripts/shared/debug_controls")
GUIInteractions = require("scripts/shared/gui_interactions")

-- Scene-specific modules
DebugOutput = require("scripts/scenes/default/debug_output")

-- Initialize modules
CameraControls.init()
GUIInteractions.init()
DebugOutput.init()

-- Scene-specific variables (isolated from other scenes)
sceneStartTime = 0
sceneObjectCount = 5

if window then
  DebugControls.bindKeys(window:getKeyboard())
end

print("Default scene initialized with " .. sceneObjectCount .. " objects")

local angle = 0.0
local radius = 3.0
local speed = 1.0  -- radians per second

if scene then
  local sceneGraph = scene:getSceneGraph()

  local success, node = pcall(function()
    return sceneGraph:get(StringID.new("cube"))
  end)

  if success and node then
    function update()
      angle = angle + speed * deltaTime

      local x = math.cos(angle) * radius
      local z = math.sin(angle) * radius
      local y = node:getPosition().y  -- keep original height

      node:setPosition(vec3.new(x, y, z))
    end
  end
end

print("Playing music with looping...")
Audio.playMusic("audio::music", true)
Audio.setMusicVolume(0.5)
print("Music started! Volume set to 0.5")
