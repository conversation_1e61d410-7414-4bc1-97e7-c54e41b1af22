cmake_minimum_required(VERSION 3.29.0)

if (POLICY CMP0025)
  cmake_policy(SET CMP0025 NEW)
endif ()

set(CMAKE_CXX_STANDARD 20)
set(CMAKE_CXX_STANDARD_REQUIRED ON)
set(CMAKE_CXX_EXTENSIONS OFF)

project(IronFrost VERSION 0.1.0 LANGUAGES C CXX)

include(CTest)
enable_testing()

# Platform-specific compiler settings
if (MSVC)
  add_compile_options(/bigobj)
  add_compile_options(/W4)  # High warning level
  add_compile_definitions(_CRT_SECURE_NO_WARNINGS)
elseif (CMAKE_CXX_COMPILER_ID STREQUAL "GNU" OR CMAKE_CXX_COMPILER_ID STREQUAL "Clang")
  add_compile_options(-Wall -Wextra -Wpedantic)
  if (CMAKE_BUILD_TYPE STREQUAL "Debug")
    add_compile_options(-g -O0)
  endif()
endif()

# Platform-specific definitions
if (WIN32)
  add_compile_definitions(IF_PLATFORM_WINDOWS)
elseif (APPLE)
  add_compile_definitions(IF_PLATFORM_MACOS)
elseif (UNIX)
  add_compile_definitions(IF_PLATFORM_LINUX)
endif()

# Static Analysis
option(ENABLE_CLANG_TIDY "Enable clang-tidy static analysis" OFF)
if(ENABLE_CLANG_TIDY)
  find_program(CLANG_TIDY_EXE NAMES "clang-tidy")
  if(CLANG_TIDY_EXE)
    # Use .clang-tidy configuration file from project root
    set(CMAKE_CXX_CLANG_TIDY "${CLANG_TIDY_EXE};--config-file=${CMAKE_SOURCE_DIR}/.clang-tidy")
    message(STATUS "clang-tidy found: ${CLANG_TIDY_EXE}")
    message(STATUS "Using .clang-tidy config: ${CMAKE_SOURCE_DIR}/.clang-tidy")
  else()
    message(WARNING "clang-tidy not found!")
  endif()
endif()

# Window Library

add_library(Window STATIC
  src/window/glfw/glfw_mouse.cpp
  src/window/glfw/glfw_keyboard.cpp
  src/window/glfw/glfw_window.cpp
  src/window/window.cpp
  src/window/console/console.cpp)

find_package(glfw3 CONFIG REQUIRED)
target_link_libraries(Window PRIVATE glfw)

# Platform-specific system libraries for Window
if(WIN32)
  target_link_libraries(Window PRIVATE opengl32)
elseif(APPLE)
  find_library(COCOA_LIBRARY Cocoa REQUIRED)
  find_library(IOKIT_LIBRARY IOKit REQUIRED)
  find_library(COREVIDEO_LIBRARY CoreVideo REQUIRED)
  find_library(OPENGL_LIBRARY OpenGL REQUIRED)
  target_link_libraries(Window PRIVATE
    ${COCOA_LIBRARY}
    ${IOKIT_LIBRARY}
    ${COREVIDEO_LIBRARY}
    ${OPENGL_LIBRARY})
elseif(UNIX)
  find_package(PkgConfig REQUIRED)
  pkg_check_modules(X11 REQUIRED x11)
  pkg_check_modules(XRANDR REQUIRED xrandr)
  pkg_check_modules(XINERAMA REQUIRED xinerama)
  pkg_check_modules(XCURSOR REQUIRED xcursor)
  pkg_check_modules(XI REQUIRED xi)
  find_package(OpenGL REQUIRED)
  target_link_libraries(Window PRIVATE
    ${X11_LIBRARIES}
    ${XRANDR_LIBRARIES}
    ${XINERAMA_LIBRARIES}
    ${XCURSOR_LIBRARIES}
    ${XI_LIBRARIES}
    ${OPENGL_LIBRARIES}
    ${CMAKE_DL_LIBS})
endif()

# Add to export set
set_target_properties(Window PROPERTIES EXPORT_NAME Window)
target_include_directories(Window PUBLIC
    $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}/src>
    $<INSTALL_INTERFACE:${CMAKE_INSTALL_INCLUDEDIR}/IronFrost>)

# VFS Library

add_library(VFS STATIC src/vfs/physfs/physfs_vfs.cpp src/vfs/vfs.cpp)

# Cross-platform PhysFS finding
find_package(PhysFS QUIET)
if(NOT PhysFS_FOUND)
  find_package(PkgConfig QUIET)
  if(PkgConfig_FOUND)
    pkg_check_modules(PHYSFS QUIET physfs)
  endif()
endif()

# Handle PhysFS linking
if(PHYSFS_FOUND)
  # Use pkg-config results
  target_include_directories(VFS PRIVATE ${PHYSFS_INCLUDE_DIRS})
  target_link_libraries(VFS PRIVATE ${PHYSFS_LIBRARIES})
elseif(PhysFS_FOUND)
  # Use find_package results
  target_include_directories(VFS PRIVATE ${PHYSFS_INCLUDE_DIRS})
  target_link_libraries(VFS PRIVATE ${PHYSFS_LIBRARIES})
else()
  # Fallback for manual installation
  find_path(PHYSFS_INCLUDE_DIR physfs.h)
  find_library(PHYSFS_LIBRARY NAMES physfs)
  if(PHYSFS_INCLUDE_DIR AND PHYSFS_LIBRARY)
    target_include_directories(VFS PRIVATE ${PHYSFS_INCLUDE_DIR})
    target_link_libraries(VFS PRIVATE ${PHYSFS_LIBRARY})
  else()
    message(FATAL_ERROR "PhysFS not found. Please install PhysFS or set PHYSFS_INCLUDE_DIR and PHYSFS_LIBRARY")
  endif()
endif()

find_package(assimp CONFIG REQUIRED)
target_link_libraries(VFS PRIVATE assimp::assimp)

# Utils Library

add_library(Utils STATIC
  src/utils/delta_time_calculator.cpp)

# Audio Library

add_library(Audio STATIC
  src/audio/openal/openal_audio_engine.cpp
  src/audio/audio_engine.cpp)

# Find OpenAL
find_package(OpenAL REQUIRED)
target_include_directories(Audio PRIVATE ${OPENAL_INCLUDE_DIR})
target_link_libraries(Audio PRIVATE ${OPENAL_LIBRARY})

# Link required system frameworks on macOS
if(APPLE)
    find_library(COREAUDIO_FRAMEWORK CoreAudio)
    find_library(AUDIOTOOLBOX_FRAMEWORK AudioToolbox)
    find_library(AUDIOUNIT_FRAMEWORK AudioUnit)
    target_link_libraries(Audio PRIVATE
        ${COREAUDIO_FRAMEWORK}
        ${AUDIOTOOLBOX_FRAMEWORK}
        ${AUDIOUNIT_FRAMEWORK})
endif()

# Find and link fmt library (required by OpenAL)
find_package(fmt CONFIG REQUIRED)
target_link_libraries(Audio PRIVATE fmt::fmt)

# Link VFS and Assets for audio file loading
target_link_libraries(Audio PRIVATE VFS)
target_link_libraries(Audio PRIVATE Assets)

# Link GLM for 3D audio positioning
find_package(glm CONFIG REQUIRED)
target_link_libraries(Audio PRIVATE glm::glm-header-only)

# Assets Library

add_library(Assets STATIC
  src/assets/loaders/stb_impl.cpp
  src/assets/loaders/image_loader.cpp
  src/assets/loaders/shader_loader.cpp
  src/assets/loaders/font_loader.cpp
  src/assets/loaders/mesh_loader.cpp
  src/assets/loaders/audio_loader.cpp
  src/assets/loaders/model_loader.cpp
  src/assets/loaders/heightmap_loader.cpp
  src/assets/writers/stb_write_impl.cpp
  src/assets/writers/image_writer.cpp
  src/assets/generators/default_texture_generator.cpp
  src/assets/generators/terrain_generator.cpp
  src/assets/assets_loader.cpp
  src/assets/assets_manager.cpp)

set_target_properties(Assets PROPERTIES LINKER_LANGUAGE CXX)

target_link_libraries(Assets PRIVATE VFS)
target_link_libraries(Assets PRIVATE Utils)

find_package(glm CONFIG REQUIRED)
target_link_libraries(Assets PRIVATE glm::glm-header-only)

find_package(Stb REQUIRED)
target_include_directories(Assets PRIVATE ${Stb_INCLUDE_DIR})

find_package(Freetype REQUIRED)
target_link_libraries(Assets PRIVATE Freetype::Freetype)

find_package(assimp CONFIG REQUIRED)
target_link_libraries(Assets PRIVATE assimp::assimp)

find_package(SndFile CONFIG REQUIRED)
target_link_libraries(Assets PRIVATE SndFile::sndfile)

# Renderer Library

add_library(Renderer STATIC
  src/renderer/opengl/opengl_renderer.cpp
  src/renderer/renderer.cpp
  src/renderer/gui/gui_renderer.cpp)

find_package(glad CONFIG REQUIRED)
target_link_libraries(Renderer PRIVATE glad::glad)

find_package(glm CONFIG REQUIRED)
target_link_libraries(Renderer PRIVATE glm::glm-header-only)

target_link_libraries(Renderer PRIVATE Window)
target_link_libraries(Renderer PRIVATE Assets)
target_link_libraries(Renderer PRIVATE Utils)

# GUI Library

add_library(GUI STATIC src/gui/widgets/widget.cpp src/gui/gui.cpp)

find_package(glm CONFIG REQUIRED)
target_link_libraries(GUI PRIVATE glm::glm-header-only)

target_link_libraries(GUI PRIVATE Renderer)

# Scripts Library

add_library(Scripts STATIC
  src/scripts/lua/lua_api_binder.cpp
  src/scripts/lua/lua_script_engine.cpp
  src/scripts/lua/lua_script_context.cpp
  src/scripts/lua/lua_global_script_context.cpp
  src/scripts/lua/lua_scene_script_context.cpp
  src/scripts/script_engine.cpp)

find_package(Lua REQUIRED)
target_include_directories(Scripts PRIVATE ${LUA_INCLUDE_DIR})
target_link_libraries(Scripts PRIVATE ${LUA_LIBRARIES})

find_package(sol2 CONFIG REQUIRED)
target_link_libraries(Scripts PRIVATE sol2::sol2)

target_link_libraries(Scripts PRIVATE VFS)

# Scene Library

add_library(Scene STATIC 
  src/scene/collisions/collision_shapes/collision_aabb.cpp 
  src/scene/collisions/collision_shapes/collision_sphere.cpp
  src/scene/collisions/collision_shapes/collision_plane.cpp
  src/scene/collisions/collision_shapes/collision_terrain.cpp
  src/scene/camera/camera.cpp 
  src/scene/scene_graph/scene_graph.cpp
  src/scene/scene_graph/scene_list.cpp 
  src/scene/spatial_partitioning/spatial_partitioning.cpp
  src/scene/loaders/scene_loader.cpp 
  src/scene/scene.cpp 
  src/scene/scene_manager.cpp)

find_package(glm CONFIG REQUIRED)
target_link_libraries(Scene PRIVATE glm::glm-header-only)

find_package(nlohmann_json CONFIG REQUIRED)
target_link_libraries(Scene PRIVATE nlohmann_json::nlohmann_json)

target_link_libraries(Scene PRIVATE Assets)
target_link_libraries(Scene PRIVATE Renderer)
target_link_libraries(Scene PRIVATE GUI)
target_link_libraries(Scene PRIVATE Scripts)

# Executable

add_executable(IronFrost 
  src/app/config/config.cpp 
  src/app/console_manager.cpp 
  src/app/system_initializer.cpp 
  src/app/application_builder.cpp 
  src/app/application.cpp 
  src/main.cpp)

target_link_libraries(IronFrost PRIVATE Window)
target_link_libraries(IronFrost PRIVATE Renderer)
target_link_libraries(IronFrost PRIVATE VFS)
target_link_libraries(IronFrost PRIVATE Utils)
target_link_libraries(IronFrost PUBLIC Assets)
target_link_libraries(IronFrost PRIVATE Audio)
target_link_libraries(IronFrost PRIVATE Scripts)
target_link_libraries(IronFrost PRIVATE Scene)

find_package(glm CONFIG REQUIRED)
target_link_libraries(IronFrost PRIVATE glm::glm-header-only)

find_package(nlohmann_json CONFIG REQUIRED)
target_link_libraries(IronFrost PRIVATE nlohmann_json::nlohmann_json)

add_custom_command(
        TARGET IronFrost POST_BUILD
        COMMAND ${CMAKE_COMMAND} -E copy_directory
                ${CMAKE_SOURCE_DIR}/data
                $<TARGET_FILE_DIR:IronFrost>/data)

# Testing
option(BUILD_TESTS "Build tests" ON)
if(BUILD_TESTS)
    find_package(Catch2 3 REQUIRED)

    # Test executable
    add_executable(IronFrostTests
        tests/main.cpp
        tests/utils/test_delta_time_calculator.cpp
        tests/utils/test_assimp_glm_utils.cpp
        tests/utils/test_json_serializers.cpp
        tests/utils/test_string_id.cpp
        tests/utils/test_storage.cpp
        tests/utils/test_random.cpp
        tests/utils/test_collision_math.cpp
        tests/utils/test_container.cpp
        tests/utils/test_containers_set.cpp
        tests/utils/test_hash.cpp
        tests/services/test_service_locator.cpp
        tests/events/test_event_queue.cpp
        tests/events/test_event_dispatcher.cpp
        tests/assets/test_asset_data_types.cpp
        tests/assets/data_types/test_heightmap_data.cpp
        tests/assets/loaders/test_image_loader.cpp
        tests/assets/loaders/test_font_loader.cpp
        tests/assets/loaders/test_mesh_loader.cpp
        tests/assets/loaders/test_shader_loader.cpp
        tests/assets/loaders/test_audio_loader.cpp
        tests/assets/loaders/meshes/test_plane_mesh.cpp
        tests/assets/loaders/meshes/test_sphere_mesh.cpp
        tests/assets/loaders/meshes/test_cube_mesh.cpp
        tests/assets/loaders/meshes/test_triangle_mesh.cpp
        tests/assets/loaders/meshes/test_quad_mesh.cpp
        tests/assets/writers/test_image_writer.cpp
        tests/assets/processors/test_image_atlas_processor.cpp
        tests/assets/test_asset_primitive_types.cpp
        tests/mocks/test_mock_mouse.cpp
        tests/gui/widgets/mixins/test_callbackable.cpp
        tests/gui/widgets/mixins/test_colorable.cpp
        tests/gui/widgets/mixins/test_transformable.cpp
        tests/gui/widgets/test_widget_clickable.cpp
        tests/gui/widgets/test_image_widget.cpp
        tests/gui/widgets/test_label_widget.cpp
        tests/gui/widgets/test_panel_widget.cpp
        tests/gui/test_gui.cpp
        tests/scene/test_scene_lighting.cpp
        tests/scene/collisions/test_collision_shapes.cpp
        tests/scene/collisions/test_collision_cache.cpp
        tests/scene/collisions/test_collision_handler.cpp
        tests/vfs/assimp/test_io_stream.cpp
        tests/vfs/assimp/test_io_system.cpp
        tests/vfs/physfs/test_physfs_vfs.cpp
        tests/scene/scene_graph/test_scene_node.cpp
        tests/scene/test_camera.cpp
        tests/scene/camera/test_frustum.cpp
        tests/scene/scene_graph/test_scene_graph.cpp
        tests/scripts/test_scheduled_task_manager.cpp
        tests/window/console/test_console.cpp
        tests/window/glfw/test_glfw_keyboard.cpp
        tests/window/glfw/test_glfw_mouse.cpp
        tests/window/glfw/test_glfw_window.cpp
    )

    target_link_libraries(IronFrostTests PRIVATE
        Catch2::Catch2WithMain
        Utils
        Window
        VFS
        Assets
        Audio
        Renderer
        GUI
        Scripts
        Scene
    )

    # Include test directories
    target_include_directories(IronFrostTests PRIVATE src/ tests/)

    # Register tests with CTest
    include(Catch)
    catch_discover_tests(IronFrostTests)
endif()

# Documentation
find_package(Doxygen)
if(DOXYGEN_FOUND)
    add_custom_target(docs
        COMMAND ${DOXYGEN_EXECUTABLE} ${CMAKE_SOURCE_DIR}/Doxyfile
        WORKING_DIRECTORY ${CMAKE_SOURCE_DIR}
        COMMENT "Generating API documentation with Doxygen"
        VERBATIM)
endif()

# Install targets
include(GNUInstallDirs)

# Install the main executable
install(TARGETS IronFrost
    EXPORT IronFrostTargets
    RUNTIME DESTINATION ${CMAKE_INSTALL_BINDIR}
    COMPONENT Runtime)

# Install libraries
install(TARGETS Window VFS Utils Assets Renderer GUI Scripts Scene
    EXPORT IronFrostTargets
    LIBRARY DESTINATION ${CMAKE_INSTALL_LIBDIR}
    ARCHIVE DESTINATION ${CMAKE_INSTALL_LIBDIR}
    RUNTIME DESTINATION ${CMAKE_INSTALL_BINDIR}
    COMPONENT Development)

# Install headers
install(DIRECTORY src/
    DESTINATION ${CMAKE_INSTALL_INCLUDEDIR}/IronFrost
    COMPONENT Development
    FILES_MATCHING PATTERN "*.hpp"
    PATTERN "*.cpp" EXCLUDE)

# Install data files
install(DIRECTORY data/
    DESTINATION ${CMAKE_INSTALL_DATADIR}/IronFrost
    COMPONENT Runtime)

# Install documentation
if(DOXYGEN_FOUND)
    install(DIRECTORY ${CMAKE_CURRENT_BINARY_DIR}/docs/
        DESTINATION ${CMAKE_INSTALL_DOCDIR}
        COMPONENT Documentation
        OPTIONAL)
endif()

# Install configuration files
install(FILES
    vcpkg.json
    vcpkg-configuration.json
    DESTINATION ${CMAKE_INSTALL_DATADIR}/IronFrost
    COMPONENT Development)

# Create and install package config files
include(CMakePackageConfigHelpers)

# Generate the config file
configure_package_config_file(
    "${CMAKE_CURRENT_SOURCE_DIR}/cmake/IronFrostConfig.cmake.in"
    "${CMAKE_CURRENT_BINARY_DIR}/IronFrostConfig.cmake"
    INSTALL_DESTINATION ${CMAKE_INSTALL_LIBDIR}/cmake/IronFrost
)

# Generate the version file
write_basic_package_version_file(
    "${CMAKE_CURRENT_BINARY_DIR}/IronFrostConfigVersion.cmake"
    VERSION ${PROJECT_VERSION}
    COMPATIBILITY SameMajorVersion
)

# Install the config files
install(FILES
    "${CMAKE_CURRENT_BINARY_DIR}/IronFrostConfig.cmake"
    "${CMAKE_CURRENT_BINARY_DIR}/IronFrostConfigVersion.cmake"
    DESTINATION ${CMAKE_INSTALL_LIBDIR}/cmake/IronFrost
    COMPONENT Development)

# Export targets
install(EXPORT IronFrostTargets
    FILE IronFrostTargets.cmake
    NAMESPACE IronFrost::
    DESTINATION ${CMAKE_INSTALL_LIBDIR}/cmake/IronFrost
    COMPONENT Development)

# Add export to build tree
export(EXPORT IronFrostTargets
    FILE "${CMAKE_CURRENT_BINARY_DIR}/IronFrostTargets.cmake"
    NAMESPACE IronFrost::)

# CPack configuration for packaging
set(CPACK_PROJECT_NAME ${PROJECT_NAME})
set(CPACK_PROJECT_VERSION ${PROJECT_VERSION})
set(CPACK_PACKAGE_NAME "IronFrost")
set(CPACK_PACKAGE_VENDOR "IronFrost Game Engine Contributors")
set(CPACK_PACKAGE_DESCRIPTION_SUMMARY "A modern C++20 game engine with modular architecture")
set(CPACK_PACKAGE_DESCRIPTION "IronFrost is a modern C++20 game engine featuring modular architecture, OpenGL rendering, Lua scripting, and comprehensive asset management.")
set(CPACK_PACKAGE_HOMEPAGE_URL "https://github.com/yourusername/IronFrost")
set(CPACK_PACKAGE_CONTACT "<EMAIL>")

# Version information
set(CPACK_PACKAGE_VERSION_MAJOR ${PROJECT_VERSION_MAJOR})
set(CPACK_PACKAGE_VERSION_MINOR ${PROJECT_VERSION_MINOR})
set(CPACK_PACKAGE_VERSION_PATCH ${PROJECT_VERSION_PATCH})

# License and readme
set(CPACK_PACKAGE_FILE_NAME "${CPACK_PACKAGE_NAME}-${CPACK_PACKAGE_VERSION}-${CMAKE_SYSTEM_NAME}-${CMAKE_SYSTEM_PROCESSOR}")
set(CPACK_RESOURCE_FILE_LICENSE "${CMAKE_CURRENT_SOURCE_DIR}/LICENSE")
set(CPACK_RESOURCE_FILE_README "${CMAKE_CURRENT_SOURCE_DIR}/README.md")

# Component configuration
set(CPACK_COMPONENTS_ALL Runtime Development Documentation)
set(CPACK_COMPONENT_RUNTIME_DISPLAY_NAME "IronFrost Runtime")
set(CPACK_COMPONENT_RUNTIME_DESCRIPTION "IronFrost game engine executable and runtime data")
set(CPACK_COMPONENT_RUNTIME_REQUIRED TRUE)

set(CPACK_COMPONENT_DEVELOPMENT_DISPLAY_NAME "Development Files")
set(CPACK_COMPONENT_DEVELOPMENT_DESCRIPTION "Headers, libraries, and CMake files for developing with IronFrost")
set(CPACK_COMPONENT_DEVELOPMENT_DEPENDS Runtime)

set(CPACK_COMPONENT_DOCUMENTATION_DISPLAY_NAME "Documentation")
set(CPACK_COMPONENT_DOCUMENTATION_DESCRIPTION "API documentation and user guides")

# Platform-specific packaging
if(WIN32)
  # Windows-specific settings
  set(CPACK_GENERATOR "ZIP;NSIS")
  set(CPACK_NSIS_DISPLAY_NAME "IronFrost Game Engine")
  set(CPACK_NSIS_PACKAGE_NAME "IronFrost")
  set(CPACK_NSIS_CONTACT "${CPACK_PACKAGE_CONTACT}")
  set(CPACK_NSIS_HELP_LINK "${CPACK_PACKAGE_HOMEPAGE_URL}")
  set(CPACK_NSIS_URL_INFO_ABOUT "${CPACK_PACKAGE_HOMEPAGE_URL}")
  set(CPACK_NSIS_MODIFY_PATH ON)
  set(CPACK_NSIS_ENABLE_UNINSTALL_BEFORE_INSTALL ON)

  # Add to PATH
  set(CPACK_NSIS_EXTRA_INSTALL_COMMANDS "
    WriteRegExpandStr HKLM 'SYSTEM\\\\CurrentControlSet\\\\Control\\\\Session Manager\\\\Environment' 'PATH' '$INSTDIR\\\\bin;$%PATH%'
    SendMessage \\\${HWND_BROADCAST} \\\${WM_WININICHANGE} 0 'STR:Environment' /TIMEOUT=5000
  ")
  set(CPACK_NSIS_EXTRA_UNINSTALL_COMMANDS "
    Push '$INSTDIR\\\\bin'
    Call un.RemoveFromPath
  ")

elseif(APPLE)
  # macOS-specific settings
  set(CPACK_GENERATOR "TGZ;DragNDrop")
  set(CPACK_DMG_VOLUME_NAME "IronFrost")
  set(CPACK_DMG_FORMAT "UDZO")
  set(CPACK_DMG_BACKGROUND_IMAGE "${CMAKE_CURRENT_SOURCE_DIR}/packaging/dmg_background.png")

elseif(UNIX)
  # Linux-specific settings
  set(CPACK_GENERATOR "TGZ;DEB;RPM")

  # Debian package settings
  set(CPACK_DEBIAN_PACKAGE_MAINTAINER "${CPACK_PACKAGE_CONTACT}")
  set(CPACK_DEBIAN_PACKAGE_SECTION "games")
  set(CPACK_DEBIAN_PACKAGE_PRIORITY "optional")
  set(CPACK_DEBIAN_PACKAGE_DEPENDS "libglfw3, libphysfs1, liblua5.4-0, libfreetype6, libassimp5")
  set(CPACK_DEBIAN_PACKAGE_HOMEPAGE "${CPACK_PACKAGE_HOMEPAGE_URL}")

  # RPM package settings
  set(CPACK_RPM_PACKAGE_LICENSE "MIT")
  set(CPACK_RPM_PACKAGE_GROUP "Amusements/Games")
  set(CPACK_RPM_PACKAGE_URL "${CPACK_PACKAGE_HOMEPAGE_URL}")
  set(CPACK_RPM_PACKAGE_REQUIRES "glfw, physfs, lua, freetype, assimp")

endif()

# Source package configuration
set(CPACK_SOURCE_GENERATOR "TGZ;ZIP")
set(CPACK_SOURCE_IGNORE_FILES
  "/\\\\.git/"
  "/\\\\.github/"
  "/build/"
  "/\\\\.vscode/"
  "/\\\\.vs/"
  "\\\\.gitignore"
  "\\\\.DS_Store"
  "Thumbs\\\\.db"
)

include(CPack)
