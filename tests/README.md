# IronFrost Testing Framework

This directory contains the test suite for the IronFrost game engine using the Catch2 testing framework.

## Quick Start

### Running All Tests
```bash
# From the project root directory
./scripts/run_tests.sh
```

### Running Tests Manually
```bash
# Configure and build with tests enabled
export VCPKG_ROOT=/Users/<USER>/vcpkg
cmake --preset Debug -DBUILD_TESTS=ON
cmake --build build/Debug

# Run tests directly
./build/Debug/IronFrostTests

# Or run with CTest
cd build/Debug && ctest
```

## Test Runner Script Options

The `scripts/run_tests.sh` script provides several convenient options:

```bash
./scripts/run_tests.sh                    # Build and run all tests in Debug mode
./scripts/run_tests.sh --clean            # Clean build and run all tests
./scripts/run_tests.sh --release          # Build and run tests in Release mode
./scripts/run_tests.sh --verbose          # Show verbose build and test output
./scripts/run_tests.sh -t "test name"     # Run a specific test by name
./scripts/run_tests.sh --help             # Show all available options
```

## Test Structure

Tests are organized by component:

- `tests/main.cpp` - Main entry point for Catch2 tests
- `tests/utils/` - Tests for utility classes
  - `test_delta_time_calculator.cpp` - Tests for DeltaTimeCalculator class

## Writing New Tests

### Basic Test Structure

```cpp
#include <catch2/catch_test_macros.hpp>
#include <catch2/catch_approx.hpp>  // For floating point comparisons

#include "your/header/file.hpp"

using namespace IronFrost;

TEST_CASE("Test description", "[tag1][tag2]") {
    // Your test code here
    REQUIRE(condition);
    
    SECTION("Sub-test description") {
        // Section-specific test code
        CHECK(another_condition);
    }
}
```

### Common Catch2 Assertions

- `REQUIRE(condition)` - Test fails immediately if condition is false
- `CHECK(condition)` - Test continues even if condition is false
- `REQUIRE_THROWS(expression)` - Expects expression to throw an exception
- `REQUIRE_NOTHROW(expression)` - Expects expression not to throw
- `REQUIRE(value == Catch::Approx(expected))` - For floating point comparisons

### Adding New Test Files

1. Create your test file in the appropriate subdirectory under `tests/`
2. Add the file to the `IronFrostTests` target in `CMakeLists.txt`:

```cmake
add_executable(IronFrostTests
    tests/main.cpp
    tests/utils/test_delta_time_calculator.cpp
    tests/your_new_directory/test_your_new_file.cpp  # Add this line
)
```

3. Make sure to link any additional libraries your tests need

## Test Tags

Use descriptive tags to categorize your tests:

- `[utils]` - Utility class tests
- `[renderer]` - Rendering system tests
- `[vfs]` - Virtual file system tests
- `[assets]` - Asset loading/management tests
- `[scene]` - Scene management tests
- `[integration]` - Integration tests

## Running Specific Tests

You can run tests by tag or name:

```bash
# Run all utility tests
./build/Debug/IronFrostTests "[utils]"

# Run a specific test case
./build/Debug/IronFrostTests "DeltaTimeCalculator basic functionality"

# Run tests matching a pattern
./build/Debug/IronFrostTests "*delta*"
```

## Continuous Integration

Tests are automatically discovered by CTest and can be run in CI environments:

```bash
ctest --output-on-failure
```

## Best Practices

1. **Test one thing at a time** - Each test case should focus on a single aspect
2. **Use descriptive names** - Test names should clearly describe what is being tested
3. **Use sections** - Group related assertions within a test case using `SECTION`
4. **Test edge cases** - Don't just test the happy path
5. **Keep tests fast** - Avoid long-running operations in unit tests
6. **Use appropriate assertions** - Choose `REQUIRE` vs `CHECK` based on whether the test should continue
7. **Clean up resources** - Ensure tests don't leave behind state that affects other tests
