#include <catch2/catch_test_macros.hpp>

#include "vfs/physfs/physfs_vfs.hpp"

#include <filesystem>
#include <fstream>
#include <memory>
#include <string>
#include <vector>
#include <cstdio>

using namespace IronFrost;

// Test fixture class to manage test environment
class PhysFSTestFixture {
private:
    std::string testDir_;
    std::string writeDir_;
    std::unique_ptr<PhysFS_VFS> vfs_;

public:
    PhysFSTestFixture() {
        // Create unique test directories
        testDir_ = "test_physfs_" + std::to_string(std::time(nullptr));
        writeDir_ = testDir_ + "/write";
        
        // Create test directories
        std::filesystem::create_directories(testDir_);
        std::filesystem::create_directories(writeDir_);
        
        // Create VFS instance
        vfs_ = PhysFS_VFS::createPhysFS();
        
        // Set write directory
        vfs_->setWriteDirectory(writeDir_);
    }
    
    ~PhysFSTestFixture() {
        // Clean up - reset VFS first to deinitialize PhysFS
        vfs_.reset();
        
        // Remove test directories
        std::error_code ec;
        std::filesystem::remove_all(testDir_, ec);
        // Ignore errors during cleanup
    }
    
    PhysFS_VFS& getVFS() { return *vfs_; }
    const std::string& getTestDir() const { return testDir_; }
    const std::string& getWriteDir() const { return writeDir_; }
    
    // Helper to create a test file
    void createTestFile(const std::string& relativePath, const std::string& content) {
        std::string fullPath = testDir_ + "/" + relativePath;
        
        // Create parent directories if needed
        std::filesystem::path filePath(fullPath);
        std::filesystem::create_directories(filePath.parent_path());
        
        std::ofstream file(fullPath);
        file << content;
        file.close();
    }
    
    // Helper to create a binary test file
    void createBinaryTestFile(const std::string& relativePath, const std::vector<unsigned char>& content) {
        std::string fullPath = testDir_ + "/" + relativePath;
        
        // Create parent directories if needed
        std::filesystem::path filePath(fullPath);
        std::filesystem::create_directories(filePath.parent_path());
        
        std::ofstream file(fullPath, std::ios::binary);
        file.write(reinterpret_cast<const char*>(content.data()), content.size());
        file.close();
    }
    
    // Helper to check if file exists on disk
    bool fileExistsOnDisk(const std::string& relativePath) const {
        return std::filesystem::exists(testDir_ + "/" + relativePath);
    }
};

TEST_CASE("PhysFS_VFS creation and destruction", "[vfs][physfs][creation]") {
    SECTION("createPhysFS returns valid instance") {
        auto vfs = PhysFS_VFS::createPhysFS();
        REQUIRE(vfs != nullptr);
    }
    
    SECTION("Multiple instances can be created sequentially") {
        {
            auto vfs1 = PhysFS_VFS::createPhysFS();
            REQUIRE(vfs1 != nullptr);
        } // vfs1 destroyed here
        
        {
            auto vfs2 = PhysFS_VFS::createPhysFS();
            REQUIRE(vfs2 != nullptr);
        } // vfs2 destroyed here
    }
}

TEST_CASE("PhysFS_VFS mount and unmount", "[vfs][physfs][mount]") {
    PhysFSTestFixture fixture;
    auto& vfs = fixture.getVFS();
    
    SECTION("Mount existing directory") {
        bool result = vfs.mount(fixture.getTestDir(), "", true);
        REQUIRE(result == true);
    }
    
    SECTION("Mount non-existent directory fails") {
        bool result = vfs.mount("/non/existent/path", "", true);
        REQUIRE(result == false);
    }
    
    SECTION("Mount and unmount cycle") {
        // Mount
        bool mountResult = vfs.mount(fixture.getTestDir(), "", true);
        REQUIRE(mountResult == true);
        
        // Unmount
        bool unmountResult = vfs.unmount(fixture.getTestDir());
        REQUIRE(unmountResult == true);
    }
    
    SECTION("Mount with specific mount point") {
        bool result = vfs.mount(fixture.getTestDir(), "testmount", true);
        REQUIRE(result == true);
        
        // Clean up
        vfs.unmount(fixture.getTestDir());
    }
    
    SECTION("Mount with append false") {
        bool result = vfs.mount(fixture.getTestDir(), "", false);
        REQUIRE(result == true);
        
        // Clean up
        vfs.unmount(fixture.getTestDir());
    }
    
    SECTION("Unmount non-mounted directory") {
        bool result = vfs.unmount("/non/mounted/path");
        REQUIRE(result == false);
    }
}

TEST_CASE("PhysFS_VFS file existence", "[vfs][physfs][exists]") {
    PhysFSTestFixture fixture;
    auto& vfs = fixture.getVFS();
    
    // Create test files
    fixture.createTestFile("test.txt", "Hello World");
    fixture.createTestFile("subdir/nested.txt", "Nested content");
    
    // Mount the test directory
    vfs.mount(fixture.getTestDir(), "", true);
    
    SECTION("Existing file returns true") {
        REQUIRE(vfs.exists("test.txt") == true);
    }
    
    SECTION("Existing nested file returns true") {
        REQUIRE(vfs.exists("subdir/nested.txt") == true);
    }
    
    SECTION("Non-existent file returns false") {
        REQUIRE(vfs.exists("nonexistent.txt") == false);
    }
    
    SECTION("Non-existent nested file returns false") {
        REQUIRE(vfs.exists("nonexistent/file.txt") == false);
    }
    
    SECTION("Directory existence") {
        REQUIRE(vfs.exists("subdir") == true);
    }
    
    SECTION("Non-existent directory returns false") {
        REQUIRE(vfs.exists("nonexistent_dir") == false);
    }
}

TEST_CASE("PhysFS_VFS read file operations", "[vfs][physfs][read]") {
    PhysFSTestFixture fixture;
    auto& vfs = fixture.getVFS();
    
    // Create test files
    std::string testContent = "Hello, PhysFS World!";
    fixture.createTestFile("test.txt", testContent);
    fixture.createTestFile("empty.txt", "");
    
    std::vector<unsigned char> binaryContent = {0x00, 0x01, 0x02, 0xFF, 0xFE, 0xFD};
    fixture.createBinaryTestFile("binary.dat", binaryContent);
    
    // Mount the test directory
    vfs.mount(fixture.getTestDir(), "", true);
    
    SECTION("Read text file") {
        std::string content = vfs.readFile("test.txt");
        REQUIRE(content == testContent);
    }
    
    SECTION("Read empty file") {
        std::string content = vfs.readFile("empty.txt");
        REQUIRE(content.empty());
    }
    
    SECTION("Read binary file as text") {
        std::string content = vfs.readFile("binary.dat");
        REQUIRE(content.size() == binaryContent.size());
        
        for (size_t i = 0; i < binaryContent.size(); ++i) {
            REQUIRE(static_cast<unsigned char>(content[i]) == binaryContent[i]);
        }
    }
    
    SECTION("Read binary file") {
        std::vector<unsigned char> content = vfs.readFileBinary("binary.dat");
        REQUIRE(content == binaryContent);
    }
    
    SECTION("Read text file as binary") {
        std::vector<unsigned char> content = vfs.readFileBinary("test.txt");
        REQUIRE(content.size() == testContent.size());
        
        for (size_t i = 0; i < testContent.size(); ++i) {
            REQUIRE(content[i] == static_cast<unsigned char>(testContent[i]));
        }
    }
    
    SECTION("Read non-existent file throws exception") {
        REQUIRE_THROWS_AS(vfs.readFile("nonexistent.txt"), std::runtime_error);
        REQUIRE_THROWS_AS(vfs.readFileBinary("nonexistent.dat"), std::runtime_error);
    }
}

TEST_CASE("PhysFS_VFS write operations", "[vfs][physfs][write]") {
    PhysFSTestFixture fixture;
    auto& vfs = fixture.getVFS();
    
    SECTION("Write text file") {
        std::string content = "Hello, write test!";
        bool result = vfs.writeFile("output.txt", content);
        REQUIRE(result == true);
        
        // Verify file was written
        REQUIRE(fixture.fileExistsOnDisk("write/output.txt"));
        
        // Mount write directory and read back
        vfs.mount(fixture.getWriteDir(), "write", true);
        std::string readContent = vfs.readFile("write/output.txt");
        REQUIRE(readContent == content);
    }
    
    SECTION("Write empty file") {
        std::string content = "";
        bool result = vfs.writeFile("empty_output.txt", content);
        REQUIRE(result == true);
        
        // Verify file was written
        REQUIRE(fixture.fileExistsOnDisk("write/empty_output.txt"));
    }
    
    SECTION("Write file with special characters") {
        std::string content = "Special chars: àáâãäåæçèéêë 中文 🎮";
        bool result = vfs.writeFile("special.txt", content);
        REQUIRE(result == true);
        
        // Mount write directory and read back
        vfs.mount(fixture.getWriteDir(), "write", true);
        std::string readContent = vfs.readFile("write/special.txt");
        REQUIRE(readContent == content);
    }
    
    SECTION("Overwrite existing file") {
        std::string content1 = "First content";
        std::string content2 = "Second content";
        
        bool result1 = vfs.writeFile("overwrite.txt", content1);
        REQUIRE(result1 == true);
        
        bool result2 = vfs.writeFile("overwrite.txt", content2);
        REQUIRE(result2 == true);
        
        // Mount write directory and read back
        vfs.mount(fixture.getWriteDir(), "write", true);
        std::string readContent = vfs.readFile("write/overwrite.txt");
        REQUIRE(readContent == content2);
    }
}

TEST_CASE("PhysFS_VFS binary write operations", "[vfs][physfs][write_binary]") {
    PhysFSTestFixture fixture;
    auto& vfs = fixture.getVFS();

    SECTION("Write binary file") {
        std::vector<unsigned char> content = {0x00, 0x01, 0x02, 0x03, 0xFF, 0xFE, 0xFD, 0xFC};
        bool result = vfs.writeFileBinary("binary_output.dat", content);
        REQUIRE(result == true);

        // Verify file was written
        REQUIRE(fixture.fileExistsOnDisk("write/binary_output.dat"));

        // Mount write directory and read back
        vfs.mount(fixture.getWriteDir(), "write", true);
        std::vector<unsigned char> readContent = vfs.readFileBinary("write/binary_output.dat");
        REQUIRE(readContent == content);
    }

    SECTION("Write empty binary file") {
        std::vector<unsigned char> content = {};
        bool result = vfs.writeFileBinary("empty_binary.dat", content);
        REQUIRE(result == true);

        // Verify file was written
        REQUIRE(fixture.fileExistsOnDisk("write/empty_binary.dat"));

        // Mount write directory and read back
        vfs.mount(fixture.getWriteDir(), "write", true);
        std::vector<unsigned char> readContent = vfs.readFileBinary("write/empty_binary.dat");
        REQUIRE(readContent.empty());
    }

    SECTION("Write large binary file") {
        // Create a large binary file (1MB)
        std::vector<unsigned char> content(1024 * 1024);
        for (size_t i = 0; i < content.size(); ++i) {
            content[i] = static_cast<unsigned char>(i % 256);
        }

        bool result = vfs.writeFileBinary("large_binary.dat", content);
        REQUIRE(result == true);

        // Verify file was written
        REQUIRE(fixture.fileExistsOnDisk("write/large_binary.dat"));

        // Mount write directory and read back
        vfs.mount(fixture.getWriteDir(), "write", true);
        std::vector<unsigned char> readContent = vfs.readFileBinary("write/large_binary.dat");
        REQUIRE(readContent == content);
        REQUIRE(readContent.size() == 1024 * 1024);
    }

    SECTION("Write binary file with all byte values") {
        // Test all possible byte values (0-255)
        std::vector<unsigned char> content(256);
        for (int i = 0; i < 256; ++i) {
            content[i] = static_cast<unsigned char>(i);
        }

        bool result = vfs.writeFileBinary("all_bytes.dat", content);
        REQUIRE(result == true);

        // Mount write directory and read back
        vfs.mount(fixture.getWriteDir(), "write", true);
        std::vector<unsigned char> readContent = vfs.readFileBinary("write/all_bytes.dat");
        REQUIRE(readContent == content);
        REQUIRE(readContent.size() == 256);

        // Verify each byte value
        for (int i = 0; i < 256; ++i) {
            REQUIRE(readContent[i] == static_cast<unsigned char>(i));
        }
    }

    SECTION("Overwrite existing binary file") {
        std::vector<unsigned char> content1 = {0x01, 0x02, 0x03};
        std::vector<unsigned char> content2 = {0xFD, 0xFE, 0xFF, 0x00, 0x01};

        bool result1 = vfs.writeFileBinary("overwrite_binary.dat", content1);
        REQUIRE(result1 == true);

        bool result2 = vfs.writeFileBinary("overwrite_binary.dat", content2);
        REQUIRE(result2 == true);

        // Mount write directory and read back
        vfs.mount(fixture.getWriteDir(), "write", true);
        std::vector<unsigned char> readContent = vfs.readFileBinary("write/overwrite_binary.dat");
        REQUIRE(readContent == content2);
        REQUIRE(readContent.size() == 5);
    }

    SECTION("Write PNG-like binary data") {
        // Simulate PNG file header and some data
        std::vector<unsigned char> pngData = {
            0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A,  // PNG signature
            0x00, 0x00, 0x00, 0x0D,                          // IHDR chunk length
            0x49, 0x48, 0x44, 0x52,                          // IHDR
            0x00, 0x00, 0x00, 0x01,                          // Width: 1
            0x00, 0x00, 0x00, 0x01,                          // Height: 1
            0x08, 0x02, 0x00, 0x00, 0x00                     // Bit depth, color type, etc.
        };

        bool result = vfs.writeFileBinary("test_image.png", pngData);
        REQUIRE(result == true);

        // Mount write directory and read back
        vfs.mount(fixture.getWriteDir(), "write", true);
        std::vector<unsigned char> readContent = vfs.readFileBinary("write/test_image.png");
        REQUIRE(readContent == pngData);

        // Verify PNG signature
        REQUIRE(readContent.size() >= 8);
        REQUIRE(readContent[0] == 0x89);
        REQUIRE(readContent[1] == 0x50);
        REQUIRE(readContent[2] == 0x4E);
        REQUIRE(readContent[3] == 0x47);
    }

    SECTION("Write binary file with null bytes") {
        std::vector<unsigned char> content = {
            0x00, 0x00, 0x00, 0x00,  // Null bytes
            0x48, 0x65, 0x6C, 0x6C, 0x6F,  // "Hello"
            0x00, 0x00,              // More null bytes
            0x57, 0x6F, 0x72, 0x6C, 0x64   // "World"
        };

        bool result = vfs.writeFileBinary("null_bytes.dat", content);
        REQUIRE(result == true);

        // Mount write directory and read back
        vfs.mount(fixture.getWriteDir(), "write", true);
        std::vector<unsigned char> readContent = vfs.readFileBinary("write/null_bytes.dat");
        REQUIRE(readContent == content);
        REQUIRE(readContent.size() == 16);
    }
}

TEST_CASE("PhysFS_VFS directory operations", "[vfs][physfs][directory]") {
    PhysFSTestFixture fixture;
    auto& vfs = fixture.getVFS();

    // Create test directory structure
    fixture.createTestFile("file1.txt", "Content 1");
    fixture.createTestFile("file2.txt", "Content 2");
    fixture.createTestFile("subdir/file3.txt", "Content 3");
    fixture.createTestFile("subdir/file4.txt", "Content 4");
    fixture.createTestFile("subdir/nested/file5.txt", "Content 5");
    fixture.createTestFile("another/file6.txt", "Content 6");

    // Mount the test directory
    vfs.mount(fixture.getTestDir(), "", true);

    SECTION("List files in root directory") {
        std::vector<std::string> files = vfs.listFiles("");

        // Should contain files and directories at root level
        REQUIRE(std::find(files.begin(), files.end(), "file1.txt") != files.end());
        REQUIRE(std::find(files.begin(), files.end(), "file2.txt") != files.end());
        REQUIRE(std::find(files.begin(), files.end(), "subdir") != files.end());
        REQUIRE(std::find(files.begin(), files.end(), "another") != files.end());
    }

    SECTION("List files in subdirectory") {
        std::vector<std::string> files = vfs.listFiles("subdir");

        REQUIRE(std::find(files.begin(), files.end(), "file3.txt") != files.end());
        REQUIRE(std::find(files.begin(), files.end(), "file4.txt") != files.end());
        REQUIRE(std::find(files.begin(), files.end(), "nested") != files.end());
    }

    SECTION("List files in nested directory") {
        std::vector<std::string> files = vfs.listFiles("subdir/nested");

        REQUIRE(std::find(files.begin(), files.end(), "file5.txt") != files.end());
    }

    SECTION("List files in non-existent directory") {
        std::vector<std::string> files = vfs.listFiles("nonexistent");
        REQUIRE(files.empty());
    }

    SECTION("List files with trailing slash") {
        std::vector<std::string> files = vfs.listFiles("subdir/");

        REQUIRE(std::find(files.begin(), files.end(), "file3.txt") != files.end());
        REQUIRE(std::find(files.begin(), files.end(), "file4.txt") != files.end());
    }
}

TEST_CASE("PhysFS_VFS write directory operations", "[vfs][physfs][write_dir]") {
    PhysFSTestFixture fixture;
    auto& vfs = fixture.getVFS();

    SECTION("Set valid write directory") {
        bool result = vfs.setWriteDirectory(fixture.getWriteDir());
        REQUIRE(result == true);
    }

    SECTION("Set non-existent write directory fails") {
        bool result = vfs.setWriteDirectory("/non/existent/write/path");
        REQUIRE(result == false);
    }

    SECTION("Change write directory") {
        // Create another write directory
        std::string altWriteDir = fixture.getTestDir() + "/alt_write";
        std::filesystem::create_directories(altWriteDir);

        // Set first write directory
        bool result1 = vfs.setWriteDirectory(fixture.getWriteDir());
        REQUIRE(result1 == true);

        // Write a file
        vfs.writeFile("test1.txt", "First location");

        // Change write directory
        bool result2 = vfs.setWriteDirectory(altWriteDir);
        REQUIRE(result2 == true);

        // Write another file
        vfs.writeFile("test2.txt", "Second location");

        // Verify files are in correct locations
        REQUIRE(std::filesystem::exists(fixture.getWriteDir() + "/test1.txt"));
        REQUIRE(std::filesystem::exists(altWriteDir + "/test2.txt"));
        REQUIRE_FALSE(std::filesystem::exists(fixture.getWriteDir() + "/test2.txt"));
        REQUIRE_FALSE(std::filesystem::exists(altWriteDir + "/test1.txt"));
    }
}

TEST_CASE("PhysFS_VFS error handling", "[vfs][physfs][errors]") {
    PhysFSTestFixture fixture;
    auto& vfs = fixture.getVFS();

    SECTION("Read from unmounted location throws") {
        // Don't mount anything, try to read
        REQUIRE_THROWS_AS(vfs.readFile("test.txt"), std::runtime_error);
        REQUIRE_THROWS_AS(vfs.readFileBinary("test.dat"), std::runtime_error);
    }

    SECTION("Write without write directory set throws") {
        // Reset write directory to invalid path
        vfs.setWriteDirectory("/invalid/path");

        // This should throw an exception (consistent with read behavior)
        REQUIRE_THROWS_AS(vfs.writeFile("test.txt", "content"), std::runtime_error);

        // Binary write should also throw
        std::vector<unsigned char> binaryContent = {0x01, 0x02, 0x03};
        REQUIRE_THROWS_AS(vfs.writeFileBinary("test.dat", binaryContent), std::runtime_error);
    }

    SECTION("Read file exception contains error message") {
        try {
            vfs.readFile("nonexistent.txt");
            FAIL("Expected exception was not thrown");
        } catch (const std::runtime_error& e) {
            std::string message = e.what();
            REQUIRE(message.find("Failed to open file for reading") != std::string::npos);
        }
    }

    SECTION("Read binary file exception contains error message") {
        try {
            vfs.readFileBinary("nonexistent.dat");
            FAIL("Expected exception was not thrown");
        } catch (const std::runtime_error& e) {
            std::string message = e.what();
            REQUIRE(message.find("Failed to open file for reading") != std::string::npos);
        }
    }

    SECTION("Write binary file exception contains error message") {
        // Reset write directory to invalid path
        vfs.setWriteDirectory("/invalid/path");

        try {
            std::vector<unsigned char> content = {0x01, 0x02, 0x03};
            vfs.writeFileBinary("test.dat", content);
            FAIL("Expected exception was not thrown");
        } catch (const std::runtime_error& e) {
            std::string message = e.what();
            REQUIRE(message.find("Failed to open file for writing") != std::string::npos);
        }
    }
}

TEST_CASE("PhysFS_VFS edge cases", "[vfs][physfs][edge_cases]") {
    PhysFSTestFixture fixture;
    auto& vfs = fixture.getVFS();

    SECTION("Empty file paths") {
        vfs.mount(fixture.getTestDir(), "", true);

        // Empty path should be treated as root directory
        std::vector<std::string> files = vfs.listFiles("");
        // Should not crash and return some result
        REQUIRE(files.size() >= 0);
    }

    SECTION("File paths with special characters") {
        // Create files with special characters in names
        fixture.createTestFile("file with spaces.txt", "Spaces content");
        fixture.createTestFile("file-with-dashes.txt", "Dashes content");
        fixture.createTestFile("file_with_underscores.txt", "Underscores content");
        fixture.createTestFile("file.with.dots.txt", "Dots content");

        vfs.mount(fixture.getTestDir(), "", true);

        REQUIRE(vfs.exists("file with spaces.txt"));
        REQUIRE(vfs.exists("file-with-dashes.txt"));
        REQUIRE(vfs.exists("file_with_underscores.txt"));
        REQUIRE(vfs.exists("file.with.dots.txt"));

        REQUIRE(vfs.readFile("file with spaces.txt") == "Spaces content");
        REQUIRE(vfs.readFile("file-with-dashes.txt") == "Dashes content");
        REQUIRE(vfs.readFile("file_with_underscores.txt") == "Underscores content");
        REQUIRE(vfs.readFile("file.with.dots.txt") == "Dots content");
    }

    SECTION("Large file operations") {
        // Create a large file (1MB)
        std::string largeContent(1024 * 1024, 'A');
        fixture.createTestFile("large.txt", largeContent);

        vfs.mount(fixture.getTestDir(), "", true);

        std::string readContent = vfs.readFile("large.txt");
        REQUIRE(readContent.size() == largeContent.size());
        REQUIRE(readContent == largeContent);

        std::vector<unsigned char> binaryContent = vfs.readFileBinary("large.txt");
        REQUIRE(binaryContent.size() == largeContent.size());
    }

    SECTION("Binary file with all byte values") {
        // Create binary file with all possible byte values
        std::vector<unsigned char> allBytes;
        for (int i = 0; i < 256; ++i) {
            allBytes.push_back(static_cast<unsigned char>(i));
        }

        fixture.createBinaryTestFile("allbytes.dat", allBytes);
        vfs.mount(fixture.getTestDir(), "", true);

        std::vector<unsigned char> readBytes = vfs.readFileBinary("allbytes.dat");
        REQUIRE(readBytes == allBytes);
    }
}

TEST_CASE("PhysFS_VFS multiple mount points", "[vfs][physfs][multi_mount]") {
    PhysFSTestFixture fixture;
    auto& vfs = fixture.getVFS();

    // Create separate directory structures
    std::string dir1 = fixture.getTestDir() + "/mount1";
    std::string dir2 = fixture.getTestDir() + "/mount2";
    std::filesystem::create_directories(dir1);
    std::filesystem::create_directories(dir2);

    // Create files in each directory
    std::ofstream(dir1 + "/file1.txt") << "Content from mount1";
    std::ofstream(dir1 + "/common.txt") << "Common file from mount1";
    std::ofstream(dir2 + "/file2.txt") << "Content from mount2";
    std::ofstream(dir2 + "/common.txt") << "Common file from mount2";

    SECTION("Mount multiple directories to different mount points") {
        vfs.mount(dir1, "mount1", true);
        vfs.mount(dir2, "mount2", true);

        REQUIRE(vfs.exists("mount1/file1.txt"));
        REQUIRE(vfs.exists("mount2/file2.txt"));
        REQUIRE(vfs.exists("mount1/common.txt"));
        REQUIRE(vfs.exists("mount2/common.txt"));

        REQUIRE(vfs.readFile("mount1/file1.txt") == "Content from mount1");
        REQUIRE(vfs.readFile("mount2/file2.txt") == "Content from mount2");
        REQUIRE(vfs.readFile("mount1/common.txt") == "Common file from mount1");
        REQUIRE(vfs.readFile("mount2/common.txt") == "Common file from mount2");
    }

    SECTION("Mount multiple directories to root with append") {
        // Mount first directory
        vfs.mount(dir1, "", true);
        REQUIRE(vfs.exists("file1.txt"));
        REQUIRE(vfs.readFile("common.txt") == "Common file from mount1");

        // Mount second directory with append=true (should add to search path)
        vfs.mount(dir2, "", true);
        REQUIRE(vfs.exists("file1.txt")); // Still from mount1
        REQUIRE(vfs.exists("file2.txt")); // Now from mount2
        // common.txt should still be from mount1 (first in search path)
        REQUIRE(vfs.readFile("common.txt") == "Common file from mount1");
    }

    SECTION("Mount multiple directories to root without append") {
        // Mount first directory
        vfs.mount(dir1, "", true);
        REQUIRE(vfs.exists("file1.txt"));

        // Mount second directory with append=false (should replace search path)
        vfs.mount(dir2, "", false);
        // Note: PhysFS behavior may vary - file1.txt might still be accessible
        // depending on the implementation. Let's test what we can guarantee:
        REQUIRE(vfs.exists("file2.txt")); // Now from mount2
        REQUIRE(vfs.readFile("common.txt") == "Common file from mount2");
    }
}

TEST_CASE("PhysFS_VFS integration scenarios", "[vfs][physfs][integration]") {
    PhysFSTestFixture fixture;
    auto& vfs = fixture.getVFS();

    SECTION("Complete workflow: mount, read, write, list") {
        // Create initial files
        fixture.createTestFile("config.txt", "initial config");
        fixture.createTestFile("data/level1.dat", "level 1 data");
        fixture.createTestFile("data/level2.dat", "level 2 data");

        // Mount for reading
        vfs.mount(fixture.getTestDir(), "", true);

        // Read existing files
        REQUIRE(vfs.exists("config.txt"));
        std::string config = vfs.readFile("config.txt");
        REQUIRE(config == "initial config");

        // List data directory
        std::vector<std::string> dataFiles = vfs.listFiles("data");
        REQUIRE(dataFiles.size() == 2);
        REQUIRE(std::find(dataFiles.begin(), dataFiles.end(), "level1.dat") != dataFiles.end());
        REQUIRE(std::find(dataFiles.begin(), dataFiles.end(), "level2.dat") != dataFiles.end());

        // Ensure write directory is set
        vfs.setWriteDirectory(fixture.getWriteDir());

        // Create necessary directories for writing
        std::filesystem::create_directories(fixture.getWriteDir() + "/saves");

        // Write new files
        vfs.writeFile("output.log", "Application started");
        vfs.writeFile("saves/save1.dat", "Save game data");

        // Verify written files exist
        vfs.mount(fixture.getWriteDir(), "write", true);
        REQUIRE(vfs.exists("write/output.log"));
        REQUIRE(vfs.exists("write/saves/save1.dat"));

        std::string logContent = vfs.readFile("write/output.log");
        REQUIRE(logContent == "Application started");
    }

    SECTION("Archive-like behavior simulation") {
        // Create a directory structure that simulates an archive
        fixture.createTestFile("assets/textures/player.png", "fake PNG data");
        fixture.createTestFile("assets/models/player.obj", "fake OBJ data");
        fixture.createTestFile("assets/sounds/jump.wav", "fake WAV data");
        fixture.createTestFile("scripts/main.lua", "print('Hello from Lua')");

        vfs.mount(fixture.getTestDir(), "", true);

        // Test asset loading simulation
        REQUIRE(vfs.exists("assets/textures/player.png"));
        REQUIRE(vfs.exists("assets/models/player.obj"));
        REQUIRE(vfs.exists("assets/sounds/jump.wav"));
        REQUIRE(vfs.exists("scripts/main.lua"));

        // Read assets
        std::string texture = vfs.readFile("assets/textures/player.png");
        std::string model = vfs.readFile("assets/models/player.obj");
        std::string sound = vfs.readFile("assets/sounds/jump.wav");
        std::string script = vfs.readFile("scripts/main.lua");

        REQUIRE(texture == "fake PNG data");
        REQUIRE(model == "fake OBJ data");
        REQUIRE(sound == "fake WAV data");
        REQUIRE(script == "print('Hello from Lua')");

        // List assets by category
        std::vector<std::string> textures = vfs.listFiles("assets/textures");
        std::vector<std::string> models = vfs.listFiles("assets/models");
        std::vector<std::string> sounds = vfs.listFiles("assets/sounds");

        REQUIRE(std::find(textures.begin(), textures.end(), "player.png") != textures.end());
        REQUIRE(std::find(models.begin(), models.end(), "player.obj") != models.end());
        REQUIRE(std::find(sounds.begin(), sounds.end(), "jump.wav") != sounds.end());
    }

    SECTION("Configuration and save data separation") {
        // Create read-only config
        fixture.createTestFile("config/settings.ini", "[Graphics]\nResolution=1920x1080");
        fixture.createTestFile("config/keybinds.cfg", "JUMP=SPACE\nMOVE_LEFT=A");

        // Mount config as read-only
        vfs.mount(fixture.getTestDir(), "", true);

        // Read configuration
        std::string settings = vfs.readFile("config/settings.ini");
        std::string keybinds = vfs.readFile("config/keybinds.cfg");

        REQUIRE(settings.find("Resolution=1920x1080") != std::string::npos);
        REQUIRE(keybinds.find("JUMP=SPACE") != std::string::npos);

        // Ensure write directory is set
        vfs.setWriteDirectory(fixture.getWriteDir());

        // Create necessary directories for writing
        std::filesystem::create_directories(fixture.getWriteDir() + "/user");

        // Write user data to separate location
        vfs.writeFile("user/profile.dat", "PlayerName=TestUser\nLevel=42");
        vfs.writeFile("user/achievements.dat", "FirstLevel=true\nSpeedRun=false");

        // Verify separation
        vfs.mount(fixture.getWriteDir(), "userdata", true);
        REQUIRE(vfs.exists("userdata/user/profile.dat"));
        REQUIRE(vfs.exists("userdata/user/achievements.dat"));

        std::string profile = vfs.readFile("userdata/user/profile.dat");
        REQUIRE(profile.find("PlayerName=TestUser") != std::string::npos);
    }
}

TEST_CASE("PhysFS_VFS text and binary integration", "[vfs][physfs][integration][binary]") {
    PhysFSTestFixture fixture;
    auto& vfs = fixture.getVFS();

    SECTION("Write and read both text and binary files") {
        // Write text file
        std::string textContent = "This is a text file with some content.";
        bool textResult = vfs.writeFile("mixed_test.txt", textContent);
        REQUIRE(textResult == true);

        // Write binary file
        std::vector<unsigned char> binaryContent = {
            0x89, 0x50, 0x4E, 0x47,  // PNG signature start
            0x0D, 0x0A, 0x1A, 0x0A,  // PNG signature end
            0x00, 0x01, 0x02, 0x03   // Some data
        };
        bool binaryResult = vfs.writeFileBinary("mixed_test.png", binaryContent);
        REQUIRE(binaryResult == true);

        // Verify both files exist
        REQUIRE(fixture.fileExistsOnDisk("write/mixed_test.txt"));
        REQUIRE(fixture.fileExistsOnDisk("write/mixed_test.png"));

        // Mount and read back
        vfs.mount(fixture.getWriteDir(), "write", true);

        std::string readText = vfs.readFile("write/mixed_test.txt");
        std::vector<unsigned char> readBinary = vfs.readFileBinary("write/mixed_test.png");

        REQUIRE(readText == textContent);
        REQUIRE(readBinary == binaryContent);
    }

    SECTION("Binary file with text-like content") {
        // Create binary data that looks like text but has null bytes
        std::vector<unsigned char> binaryContent = {
            0x48, 0x65, 0x6C, 0x6C, 0x6F,  // "Hello"
            0x00, 0x00,                     // Null bytes (not valid in text)
            0x57, 0x6F, 0x72, 0x6C, 0x64,  // "World"
            0x00                            // Null terminator
        };

        bool result = vfs.writeFileBinary("text_like_binary.dat", binaryContent);
        REQUIRE(result == true);

        // Mount and read back as binary
        vfs.mount(fixture.getWriteDir(), "write", true);
        std::vector<unsigned char> readContent = vfs.readFileBinary("write/text_like_binary.dat");

        REQUIRE(readContent == binaryContent);
        REQUIRE(readContent.size() == 13);

        // Verify the null bytes are preserved
        REQUIRE(readContent[5] == 0x00);
        REQUIRE(readContent[6] == 0x00);
        REQUIRE(readContent[12] == 0x00);
    }

    SECTION("Large mixed file operations") {
        // Write multiple files of different types
        std::vector<std::pair<std::string, std::string>> textFiles = {
            {"config.ini", "[Settings]\nDebug=true"},
            {"readme.txt", "This is a readme file with instructions."},
            {"script.lua", "function main()\n  print('Hello')\nend"}
        };

        std::vector<std::pair<std::string, std::vector<unsigned char>>> binaryFiles = {
            {"image1.png", {0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A}},
            {"sound.wav", {0x52, 0x49, 0x46, 0x46, 0x24, 0x08, 0x00, 0x00}},
            {"data.bin", {0x00, 0x01, 0x02, 0x03, 0xFF, 0xFE, 0xFD, 0xFC}}
        };

        // Write all text files
        for (const auto& [filename, content] : textFiles) {
            bool result = vfs.writeFile(filename, content);
            REQUIRE(result == true);
        }

        // Write all binary files
        for (const auto& [filename, content] : binaryFiles) {
            bool result = vfs.writeFileBinary(filename, content);
            REQUIRE(result == true);
        }

        // Mount and verify all files
        vfs.mount(fixture.getWriteDir(), "write", true);

        // Verify text files
        for (const auto& [filename, expectedContent] : textFiles) {
            std::string readContent = vfs.readFile("write/" + filename);
            REQUIRE(readContent == expectedContent);
        }

        // Verify binary files
        for (const auto& [filename, expectedContent] : binaryFiles) {
            std::vector<unsigned char> readContent = vfs.readFileBinary("write/" + filename);
            REQUIRE(readContent == expectedContent);
        }
    }

    SECTION("File type consistency") {
        // Write the same content as both text and binary
        std::string textData = "Hello, World!";
        std::vector<unsigned char> binaryData(textData.begin(), textData.end());

        bool textResult = vfs.writeFile("same_content.txt", textData);
        bool binaryResult = vfs.writeFileBinary("same_content.bin", binaryData);

        REQUIRE(textResult == true);
        REQUIRE(binaryResult == true);

        // Mount and read back
        vfs.mount(fixture.getWriteDir(), "write", true);

        std::string readText = vfs.readFile("write/same_content.txt");
        std::vector<unsigned char> readBinary = vfs.readFileBinary("write/same_content.bin");

        REQUIRE(readText == textData);
        REQUIRE(readBinary == binaryData);

        // Convert binary back to string and compare
        std::string binaryAsText(readBinary.begin(), readBinary.end());
        REQUIRE(binaryAsText == textData);
    }
}
