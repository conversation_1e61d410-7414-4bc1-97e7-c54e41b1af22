#include <catch2/catch_test_macros.hpp>

#include "vfs/assimp/io_stream.hpp"

#include <assimp/IOStream.hpp>
#include <vector>
#include <string>
#include <cstring>

using namespace IronFrost;

// Helper function to create test data
std::vector<unsigned char> createTestData(const std::string& data) {
    return std::vector<unsigned char>(data.begin(), data.end());
}

// Helper function to create binary test data
std::vector<unsigned char> createBinaryTestData() {
    std::vector<unsigned char> data;
    for (int i = 0; i < 256; ++i) {
        data.push_back(static_cast<unsigned char>(i));
    }
    return data;
}

TEST_CASE("VFS_IOStream basic functionality", "[vfs][assimp][io_stream][basic]") {
    SECTION("Constructor with empty buffer") {
        std::vector<unsigned char> emptyBuffer;
        VFS_IOStream stream(std::move(emptyBuffer));
        
        REQUIRE(stream.FileSize() == 0);
        REQUIRE(stream.Tell() == 0);
    }
    
    SECTION("Constructor with data") {
        auto testData = createTestData("Hello World");
        size_t expectedSize = testData.size();
        
        VFS_IOStream stream(std::move(testData));
        
        REQUIRE(stream.FileSize() == expectedSize);
        REQUIRE(stream.Tell() == 0);
    }
    
    SECTION("FileSize returns correct size") {
        auto testData = createTestData("Test data for size check");
        size_t expectedSize = testData.size();
        
        VFS_IOStream stream(std::move(testData));
        
        REQUIRE(stream.FileSize() == expectedSize);
    }
    
    SECTION("Initial position is zero") {
        auto testData = createTestData("Initial position test");
        VFS_IOStream stream(std::move(testData));
        
        REQUIRE(stream.Tell() == 0);
    }
}

TEST_CASE("VFS_IOStream read functionality", "[vfs][assimp][io_stream][read]") {
    SECTION("Read entire buffer") {
        std::string testString = "Hello World";
        auto testData = createTestData(testString);
        VFS_IOStream stream(std::move(testData));
        
        char buffer[20] = {0};
        size_t bytesRead = stream.Read(buffer, 1, testString.length());
        
        REQUIRE(bytesRead == testString.length());
        REQUIRE(std::string(buffer, bytesRead) == testString);
        REQUIRE(stream.Tell() == testString.length());
    }
    
    SECTION("Read partial buffer") {
        std::string testString = "Hello World";
        auto testData = createTestData(testString);
        VFS_IOStream stream(std::move(testData));
        
        char buffer[10] = {0};
        size_t bytesRead = stream.Read(buffer, 1, 5);
        
        REQUIRE(bytesRead == 5);
        REQUIRE(std::string(buffer, bytesRead) == "Hello");
        REQUIRE(stream.Tell() == 5);
    }
    
    SECTION("Read with different size and count parameters") {
        std::string testString = "ABCDEFGHIJ";
        auto testData = createTestData(testString);
        VFS_IOStream stream(std::move(testData));
        
        // Read 5 elements of size 2 (should read 10 bytes total)
        char buffer[20] = {0};
        size_t elementsRead = stream.Read(buffer, 2, 5);
        
        REQUIRE(elementsRead == 5); // 5 elements of size 2
        REQUIRE(std::string(buffer, 10) == testString);
        REQUIRE(stream.Tell() == 10);
    }
    
    SECTION("Read beyond buffer size") {
        std::string testString = "Short";
        auto testData = createTestData(testString);
        VFS_IOStream stream(std::move(testData));
        
        char buffer[20] = {0};
        size_t bytesRead = stream.Read(buffer, 1, 20);
        
        REQUIRE(bytesRead == testString.length());
        REQUIRE(std::string(buffer, bytesRead) == testString);
        REQUIRE(stream.Tell() == testString.length());
    }
    
    SECTION("Read from empty buffer") {
        std::vector<unsigned char> emptyBuffer;
        VFS_IOStream stream(std::move(emptyBuffer));
        
        char buffer[10] = {0};
        size_t bytesRead = stream.Read(buffer, 1, 10);
        
        REQUIRE(bytesRead == 0);
        REQUIRE(stream.Tell() == 0);
    }
    
    SECTION("Multiple consecutive reads") {
        std::string testString = "Hello World Test";
        auto testData = createTestData(testString);
        VFS_IOStream stream(std::move(testData));
        
        char buffer1[6] = {0};
        char buffer2[6] = {0};
        char buffer3[5] = {0};
        
        size_t read1 = stream.Read(buffer1, 1, 5);
        size_t read2 = stream.Read(buffer2, 1, 6);
        size_t read3 = stream.Read(buffer3, 1, 5);
        
        REQUIRE(read1 == 5);
        REQUIRE(std::string(buffer1, read1) == "Hello");
        REQUIRE(read2 == 6);
        REQUIRE(std::string(buffer2, read2) == " World");
        REQUIRE(read3 == 5);
        REQUIRE(std::string(buffer3, read3) == " Test");
        REQUIRE(stream.Tell() == 16);
    }
    
    SECTION("Read with zero size or count") {
        auto testData = createTestData("Test data");
        VFS_IOStream stream(std::move(testData));
        
        char buffer[10] = {0};
        
        // Zero size
        size_t read1 = stream.Read(buffer, 0, 5);
        REQUIRE(read1 == 0);
        REQUIRE(stream.Tell() == 0);
        
        // Zero count
        size_t read2 = stream.Read(buffer, 1, 0);
        REQUIRE(read2 == 0);
        REQUIRE(stream.Tell() == 0);
    }
    
    SECTION("Read binary data") {
        auto binaryData = createBinaryTestData();
        VFS_IOStream stream(std::move(binaryData));
        
        unsigned char buffer[256];
        size_t bytesRead = stream.Read(buffer, 1, 256);
        
        REQUIRE(bytesRead == 256);
        for (int i = 0; i < 256; ++i) {
            REQUIRE(buffer[i] == static_cast<unsigned char>(i));
        }
        REQUIRE(stream.Tell() == 256);
    }
}

TEST_CASE("VFS_IOStream write functionality", "[vfs][assimp][io_stream][write]") {
    SECTION("Write always returns 0") {
        auto testData = createTestData("Test data");
        VFS_IOStream stream(std::move(testData));
        
        const char* writeData = "New data";
        size_t written = stream.Write(writeData, 1, 8);
        
        REQUIRE(written == 0);
        // Position should not change
        REQUIRE(stream.Tell() == 0);
        // File size should not change
        REQUIRE(stream.FileSize() == 9); // "Test data" length
    }
    
    SECTION("Write with different parameters") {
        auto testData = createTestData("Test");
        VFS_IOStream stream(std::move(testData));
        
        const char* writeData = "ABCDEFGH";
        
        // Try different size and count combinations
        REQUIRE(stream.Write(writeData, 1, 8) == 0);
        REQUIRE(stream.Write(writeData, 2, 4) == 0);
        REQUIRE(stream.Write(writeData, 4, 2) == 0);
        
        // Stream state should remain unchanged
        REQUIRE(stream.Tell() == 0);
        REQUIRE(stream.FileSize() == 4);
    }
}

TEST_CASE("VFS_IOStream seek functionality", "[vfs][assimp][io_stream][seek]") {
    SECTION("Seek from beginning (aiOrigin_SET)") {
        auto testData = createTestData("Hello World");
        VFS_IOStream stream(std::move(testData));
        
        REQUIRE(stream.Seek(5, aiOrigin_SET) == aiReturn_SUCCESS);
        REQUIRE(stream.Tell() == 5);
        
        REQUIRE(stream.Seek(0, aiOrigin_SET) == aiReturn_SUCCESS);
        REQUIRE(stream.Tell() == 0);
        
        REQUIRE(stream.Seek(11, aiOrigin_SET) == aiReturn_SUCCESS);
        REQUIRE(stream.Tell() == 11);
    }
    
    SECTION("Seek from current position (aiOrigin_CUR)") {
        auto testData = createTestData("Hello World");
        VFS_IOStream stream(std::move(testData));
        
        // Move to position 3
        REQUIRE(stream.Seek(3, aiOrigin_SET) == aiReturn_SUCCESS);
        REQUIRE(stream.Tell() == 3);
        
        // Move forward by 2
        REQUIRE(stream.Seek(2, aiOrigin_CUR) == aiReturn_SUCCESS);
        REQUIRE(stream.Tell() == 5);
        
        // Move backward by 1
        REQUIRE(stream.Seek(-1, aiOrigin_CUR) == aiReturn_SUCCESS);
        REQUIRE(stream.Tell() == 4);
    }
    
    SECTION("Seek from end (aiOrigin_END)") {
        auto testData = createTestData("Hello World"); // 11 characters
        VFS_IOStream stream(std::move(testData));
        
        // Seek to end
        REQUIRE(stream.Seek(0, aiOrigin_END) == aiReturn_SUCCESS);
        REQUIRE(stream.Tell() == 11);
        
        // Seek 5 positions back from end
        REQUIRE(stream.Seek(5, aiOrigin_END) == aiReturn_SUCCESS);
        REQUIRE(stream.Tell() == 6);
        
        // Seek to beginning from end
        REQUIRE(stream.Seek(11, aiOrigin_END) == aiReturn_SUCCESS);
        REQUIRE(stream.Tell() == 0);
    }
    
    SECTION("Seek beyond boundaries gets clamped") {
        auto testData = createTestData("Test"); // 4 characters
        VFS_IOStream stream(std::move(testData));
        
        // Seek beyond end
        REQUIRE(stream.Seek(100, aiOrigin_SET) == aiReturn_SUCCESS);
        REQUIRE(stream.Tell() == 4); // Clamped to buffer size
        
        // Seek with large negative value (will underflow to large positive)
        REQUIRE(stream.Seek(-100, aiOrigin_CUR) == aiReturn_SUCCESS);
        REQUIRE(stream.Tell() == 4); // Clamped to buffer size due to overflow
    }
    
    SECTION("Invalid origin returns failure") {
        auto testData = createTestData("Test");
        VFS_IOStream stream(std::move(testData));
        
        // Invalid origin value
        REQUIRE(stream.Seek(0, static_cast<aiOrigin>(999)) == aiReturn_FAILURE);
        REQUIRE(stream.Tell() == 0); // Position should not change
    }
    
    SECTION("Seek affects subsequent reads") {
        std::string testString = "Hello World";
        auto testData = createTestData(testString);
        VFS_IOStream stream(std::move(testData));
        
        // Seek to position 6 and read
        REQUIRE(stream.Seek(6, aiOrigin_SET) == aiReturn_SUCCESS);
        
        char buffer[10] = {0};
        size_t bytesRead = stream.Read(buffer, 1, 5);
        
        REQUIRE(bytesRead == 5);
        REQUIRE(std::string(buffer, bytesRead) == "World");
        REQUIRE(stream.Tell() == 11);
    }
}

TEST_CASE("VFS_IOStream flush functionality", "[vfs][assimp][io_stream][flush]") {
    SECTION("Flush does nothing but doesn't crash") {
        auto testData = createTestData("Test data");
        VFS_IOStream stream(std::move(testData));

        // Should not crash or change state
        stream.Flush();

        REQUIRE(stream.Tell() == 0);
        REQUIRE(stream.FileSize() == 9);
    }
}

TEST_CASE("VFS_IOStream complex scenarios", "[vfs][assimp][io_stream][complex]") {
    SECTION("Read, seek, read pattern") {
        std::string testString = "ABCDEFGHIJKLMNOP";
        auto testData = createTestData(testString);
        VFS_IOStream stream(std::move(testData));

        // Read first 4 characters
        char buffer1[5] = {0};
        size_t read1 = stream.Read(buffer1, 1, 4);
        REQUIRE(read1 == 4);
        REQUIRE(std::string(buffer1, read1) == "ABCD");
        REQUIRE(stream.Tell() == 4);

        // Seek to position 8
        REQUIRE(stream.Seek(8, aiOrigin_SET) == aiReturn_SUCCESS);
        REQUIRE(stream.Tell() == 8);

        // Read 4 more characters
        char buffer2[5] = {0};
        size_t read2 = stream.Read(buffer2, 1, 4);
        REQUIRE(read2 == 4);
        REQUIRE(std::string(buffer2, read2) == "IJKL");
        REQUIRE(stream.Tell() == 12);

        // Seek back to position 2
        REQUIRE(stream.Seek(2, aiOrigin_SET) == aiReturn_SUCCESS);
        REQUIRE(stream.Tell() == 2);

        // Read 3 characters
        char buffer3[4] = {0};
        size_t read3 = stream.Read(buffer3, 1, 3);
        REQUIRE(read3 == 3);
        REQUIRE(std::string(buffer3, read3) == "CDE");
        REQUIRE(stream.Tell() == 5);
    }

    SECTION("Large buffer operations") {
        // Create a large buffer (1MB)
        std::vector<unsigned char> largeData;
        largeData.reserve(1024 * 1024);
        for (int i = 0; i < 1024 * 1024; ++i) {
            largeData.push_back(static_cast<unsigned char>(i % 256));
        }

        VFS_IOStream stream(std::move(largeData));

        REQUIRE(stream.FileSize() == 1024 * 1024);
        REQUIRE(stream.Tell() == 0);

        // Read large chunk
        std::vector<unsigned char> readBuffer(1024);
        size_t bytesRead = stream.Read(readBuffer.data(), 1, 1024);
        REQUIRE(bytesRead == 1024);
        REQUIRE(stream.Tell() == 1024);

        // Verify data integrity
        for (int i = 0; i < 1024; ++i) {
            REQUIRE(readBuffer[i] == static_cast<unsigned char>(i % 256));
        }

        // Seek to middle
        REQUIRE(stream.Seek(512 * 1024, aiOrigin_SET) == aiReturn_SUCCESS);
        REQUIRE(stream.Tell() == 512 * 1024);

        // Read from middle
        bytesRead = stream.Read(readBuffer.data(), 1, 1024);
        REQUIRE(bytesRead == 1024);
        REQUIRE(stream.Tell() == 512 * 1024 + 1024);
    }

    SECTION("Boundary conditions") {
        auto testData = createTestData("Test");
        VFS_IOStream stream(std::move(testData));

        // Read exactly the buffer size
        char buffer[4];
        size_t bytesRead = stream.Read(buffer, 1, 4);
        REQUIRE(bytesRead == 4);
        REQUIRE(stream.Tell() == 4);

        // Try to read more (should return 0)
        char extraBuffer[10];
        size_t extraRead = stream.Read(extraBuffer, 1, 10);
        REQUIRE(extraRead == 0);
        REQUIRE(stream.Tell() == 4);

        // Seek to exact end
        REQUIRE(stream.Seek(4, aiOrigin_SET) == aiReturn_SUCCESS);
        REQUIRE(stream.Tell() == 4);

        // Try to read from end
        size_t endRead = stream.Read(extraBuffer, 1, 1);
        REQUIRE(endRead == 0);
        REQUIRE(stream.Tell() == 4);
    }

    SECTION("Mixed size and count parameters") {
        auto testData = createTestData("ABCDEFGHIJKLMNOPQRSTUVWXYZ");
        VFS_IOStream stream(std::move(testData));

        // Read 13 elements of size 2 (26 bytes total)
        char buffer[30] = {0};
        size_t elementsRead = stream.Read(buffer, 2, 13);
        REQUIRE(elementsRead == 13);
        REQUIRE(stream.Tell() == 26);
        REQUIRE(std::string(buffer, 26) == "ABCDEFGHIJKLMNOPQRSTUVWXYZ");

        // Reset position
        REQUIRE(stream.Seek(0, aiOrigin_SET) == aiReturn_SUCCESS);

        // Read 2 elements of size 13 (26 bytes total)
        char buffer2[30] = {0};
        size_t elementsRead2 = stream.Read(buffer2, 13, 2);
        REQUIRE(elementsRead2 == 2);
        REQUIRE(stream.Tell() == 26);
        REQUIRE(std::string(buffer2, 26) == "ABCDEFGHIJKLMNOPQRSTUVWXYZ");
    }

    SECTION("Partial element reads") {
        auto testData = createTestData("ABCDEFGHIJ"); // 10 bytes
        VFS_IOStream stream(std::move(testData));

        // Try to read 4 elements of size 3 (12 bytes), but only 10 available
        char buffer[15] = {0};
        size_t elementsRead = stream.Read(buffer, 3, 4);

        // Should read 3 complete elements (9 bytes) and return 3
        // But the implementation reads all available bytes (10) and returns 3 (10/3 = 3)
        REQUIRE(elementsRead == 3);
        REQUIRE(stream.Tell() == 10); // All 10 bytes are read
        REQUIRE(std::string(buffer, 10) == "ABCDEFGHIJ");
    }
}

TEST_CASE("VFS_IOStream edge cases", "[vfs][assimp][io_stream][edge_cases]") {
    SECTION("Single byte operations") {
        auto testData = createTestData("A");
        VFS_IOStream stream(std::move(testData));

        REQUIRE(stream.FileSize() == 1);

        char buffer[2] = {0};
        size_t bytesRead = stream.Read(buffer, 1, 1);
        REQUIRE(bytesRead == 1);
        REQUIRE(buffer[0] == 'A');
        REQUIRE(stream.Tell() == 1);

        // Try to read more
        size_t moreRead = stream.Read(buffer, 1, 1);
        REQUIRE(moreRead == 0);
        REQUIRE(stream.Tell() == 1);
    }

    SECTION("Null buffer handling") {
        auto testData = createTestData("Test");
        VFS_IOStream stream(std::move(testData));

        // Reading into null buffer should not crash (though behavior is undefined)
        // This test mainly ensures we don't crash
        size_t result = stream.Read(nullptr, 1, 0);
        REQUIRE(result == 0);
    }

    SECTION("Very large seek values") {
        auto testData = createTestData("Test");
        VFS_IOStream stream(std::move(testData));

        // Seek with very large positive value
        REQUIRE(stream.Seek(SIZE_MAX, aiOrigin_SET) == aiReturn_SUCCESS);
        REQUIRE(stream.Tell() == 4); // Clamped to buffer size

        // Seek with very large value from current position
        // This will cause integer overflow, but the clamp should handle it
        REQUIRE(stream.Seek(SIZE_MAX, aiOrigin_CUR) == aiReturn_SUCCESS);
        // The actual result depends on overflow behavior, but should be clamped
        REQUIRE(stream.Tell() <= 4); // Should be clamped to valid range
    }

    SECTION("Multiple flush calls") {
        auto testData = createTestData("Test data");
        VFS_IOStream stream(std::move(testData));

        // Multiple flush calls should be safe
        stream.Flush();
        stream.Flush();
        stream.Flush();

        REQUIRE(stream.Tell() == 0);
        REQUIRE(stream.FileSize() == 9);
    }
}
