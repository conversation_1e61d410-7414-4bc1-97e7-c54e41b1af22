#include <catch2/catch_test_macros.hpp>

#include "services/service_locator.hpp"
#include "services/service.hpp"

#include <memory>
#include <string>

using namespace IronFrost;

// Test service interfaces and implementations
class ITestService : public IService {
public:
    virtual ~ITestService() = default;
    virtual std::string getName() const = 0;
    virtual int getValue() const = 0;
};

class TestServiceA : public ITestService {
private:
    std::string name_;
    int value_;
    
public:
    TestServiceA(const std::string& name, int value) : name_(name), value_(value) {}
    
    std::string getName() const override { return name_; }
    int getValue() const override { return value_; }
    
    void setName(const std::string& name) { name_ = name; }
    void setValue(int value) { value_ = value; }
};

class TestServiceB : public ITestService {
private:
    std::string name_;
    int value_;
    
public:
    TestServiceB(const std::string& name, int value) : name_(name), value_(value) {}
    
    std::string getName() const override { return name_; }
    int getValue() const override { return value_; }
    
    void increment() { value_++; }
};

// Another service type for testing multiple services
class ILoggerService : public IService {
public:
    virtual ~ILoggerService() = default;
    virtual void log(const std::string& message) = 0;
    virtual std::vector<std::string> getLogs() const = 0;
};

class MockLogger : public ILoggerService {
private:
    std::vector<std::string> logs_;
    
public:
    void log(const std::string& message) override {
        logs_.push_back(message);
    }
    
    std::vector<std::string> getLogs() const override {
        return logs_;
    }
    
    void clear() { logs_.clear(); }
};

// Simple service for basic testing
class SimpleService : public IService {
private:
    bool initialized_;
    
public:
    SimpleService() : initialized_(true) {}
    
    bool isInitialized() const { return initialized_; }
    void shutdown() { initialized_ = false; }
};

TEST_CASE("ServiceLocator basic functionality", "[services][service_locator][basic]") {
    
    SECTION("Register and retrieve service") {
        auto service = std::make_unique<TestServiceA>("TestA", 42);
        
        ServiceLocator::registerService<TestServiceA>(std::move(service));
        
        TestServiceA& retrieved = ServiceLocator::getService<TestServiceA>();
        
        REQUIRE(retrieved.getName() == "TestA");
        REQUIRE(retrieved.getValue() == 42);
    }
    
    SECTION("Service persists across calls") {
        auto service = std::make_unique<TestServiceA>("Persistent", 100);
        
        ServiceLocator::registerService<TestServiceA>(std::move(service));
        
        // First retrieval
        TestServiceA& service1 = ServiceLocator::getService<TestServiceA>();
        REQUIRE(service1.getName() == "Persistent");
        REQUIRE(service1.getValue() == 100);
        
        // Modify the service
        service1.setName("Modified");
        service1.setValue(200);
        
        // Second retrieval should show modifications
        TestServiceA& service2 = ServiceLocator::getService<TestServiceA>();
        REQUIRE(service2.getName() == "Modified");
        REQUIRE(service2.getValue() == 200);
        
        // Should be the same instance
        REQUIRE(&service1 == &service2);
    }
    
    SECTION("Replace existing service") {
        // Register first service
        auto service1 = std::make_unique<TestServiceA>("First", 1);
        ServiceLocator::registerService<TestServiceA>(std::move(service1));
        
        TestServiceA& retrieved1 = ServiceLocator::getService<TestServiceA>();
        REQUIRE(retrieved1.getName() == "First");
        REQUIRE(retrieved1.getValue() == 1);
        
        // Replace with second service
        auto service2 = std::make_unique<TestServiceA>("Second", 2);
        ServiceLocator::registerService<TestServiceA>(std::move(service2));
        
        TestServiceA& retrieved2 = ServiceLocator::getService<TestServiceA>();
        REQUIRE(retrieved2.getName() == "Second");
        REQUIRE(retrieved2.getValue() == 2);
        
        // Should be a different instance
        REQUIRE(&retrieved1 != &retrieved2);
    }
}

TEST_CASE("ServiceLocator multiple service types", "[services][service_locator][multiple]") {
    
    SECTION("Register different service types") {
        auto testService = std::make_unique<TestServiceA>("MultiTest", 123);
        auto loggerService = std::make_unique<MockLogger>();
        auto simpleService = std::make_unique<SimpleService>();
        
        ServiceLocator::registerService<TestServiceA>(std::move(testService));
        ServiceLocator::registerService<MockLogger>(std::move(loggerService));
        ServiceLocator::registerService<SimpleService>(std::move(simpleService));
        
        // Retrieve each service
        TestServiceA& test = ServiceLocator::getService<TestServiceA>();
        MockLogger& logger = ServiceLocator::getService<MockLogger>();
        SimpleService& simple = ServiceLocator::getService<SimpleService>();
        
        REQUIRE(test.getName() == "MultiTest");
        REQUIRE(test.getValue() == 123);
        REQUIRE(logger.getLogs().empty());
        REQUIRE(simple.isInitialized());
    }
    
    SECTION("Services are independent") {
        auto serviceA = std::make_unique<TestServiceA>("ServiceA", 10);
        auto serviceB = std::make_unique<TestServiceB>("ServiceB", 20);
        
        ServiceLocator::registerService<TestServiceA>(std::move(serviceA));
        ServiceLocator::registerService<TestServiceB>(std::move(serviceB));
        
        TestServiceA& a = ServiceLocator::getService<TestServiceA>();
        TestServiceB& b = ServiceLocator::getService<TestServiceB>();
        
        // Modify one service
        a.setName("ModifiedA");
        a.setValue(100);
        
        // Other service should be unchanged
        REQUIRE(b.getName() == "ServiceB");
        REQUIRE(b.getValue() == 20);
        
        // Modified service should show changes
        REQUIRE(a.getName() == "ModifiedA");
        REQUIRE(a.getValue() == 100);
    }
}

TEST_CASE("ServiceLocator interface polymorphism", "[services][service_locator][polymorphism]") {
    
    SECTION("Register concrete type, retrieve via interface") {
        auto service = std::make_unique<TestServiceA>("Interface", 999);
        
        ServiceLocator::registerService<ITestService>(std::move(service));
        
        ITestService& retrieved = ServiceLocator::getService<ITestService>();
        
        REQUIRE(retrieved.getName() == "Interface");
        REQUIRE(retrieved.getValue() == 999);
    }
    
    SECTION("Register different implementations of same interface") {
        // Register TestServiceA as ITestService
        auto serviceA = std::make_unique<TestServiceA>("ImplA", 111);
        ServiceLocator::registerService<ITestService>(std::move(serviceA));
        
        ITestService& retrievedA = ServiceLocator::getService<ITestService>();
        REQUIRE(retrievedA.getName() == "ImplA");
        REQUIRE(retrievedA.getValue() == 111);
        
        // Replace with TestServiceB
        auto serviceB = std::make_unique<TestServiceB>("ImplB", 222);
        ServiceLocator::registerService<ITestService>(std::move(serviceB));
        
        ITestService& retrievedB = ServiceLocator::getService<ITestService>();
        REQUIRE(retrievedB.getName() == "ImplB");
        REQUIRE(retrievedB.getValue() == 222);
        
        // Can downcast to specific type
        TestServiceB* specificB = dynamic_cast<TestServiceB*>(&retrievedB);
        REQUIRE(specificB != nullptr);
        specificB->increment();
        REQUIRE(retrievedB.getValue() == 223);
    }
}

TEST_CASE("ServiceLocator service interactions", "[services][service_locator][interactions]") {
    
    SECTION("Services can interact with each other") {
        auto logger = std::make_unique<MockLogger>();
        auto testService = std::make_unique<TestServiceA>("Interactive", 50);
        
        ServiceLocator::registerService<MockLogger>(std::move(logger));
        ServiceLocator::registerService<TestServiceA>(std::move(testService));
        
        // Get both services
        MockLogger& loggerRef = ServiceLocator::getService<MockLogger>();
        TestServiceA& testRef = ServiceLocator::getService<TestServiceA>();
        
        // Use logger from within test logic
        loggerRef.log("Service started: " + testRef.getName());
        loggerRef.log("Initial value: " + std::to_string(testRef.getValue()));
        
        testRef.setValue(75);
        loggerRef.log("Value updated: " + std::to_string(testRef.getValue()));
        
        auto logs = loggerRef.getLogs();
        REQUIRE(logs.size() == 3);
        REQUIRE(logs[0] == "Service started: Interactive");
        REQUIRE(logs[1] == "Initial value: 50");
        REQUIRE(logs[2] == "Value updated: 75");
    }
    
    SECTION("Service state persists across multiple interactions") {
        auto logger = std::make_unique<MockLogger>();
        ServiceLocator::registerService<MockLogger>(std::move(logger));
        
        // First interaction
        {
            MockLogger& loggerRef = ServiceLocator::getService<MockLogger>();
            loggerRef.log("First message");
            loggerRef.log("Second message");
        }
        
        // Second interaction
        {
            MockLogger& loggerRef = ServiceLocator::getService<MockLogger>();
            loggerRef.log("Third message");
            
            auto logs = loggerRef.getLogs();
            REQUIRE(logs.size() == 3);
            REQUIRE(logs[0] == "First message");
            REQUIRE(logs[1] == "Second message");
            REQUIRE(logs[2] == "Third message");
        }
    }
}

TEST_CASE("ServiceLocator edge cases", "[services][service_locator][edge_cases]") {
    
    SECTION("Service with complex state") {
        auto logger = std::make_unique<MockLogger>();
        
        // Pre-populate with some logs
        logger->log("Initialization log");
        logger->log("Setup complete");
        
        ServiceLocator::registerService<MockLogger>(std::move(logger));
        
        MockLogger& retrieved = ServiceLocator::getService<MockLogger>();
        auto logs = retrieved.getLogs();
        
        REQUIRE(logs.size() == 2);
        REQUIRE(logs[0] == "Initialization log");
        REQUIRE(logs[1] == "Setup complete");
        
        // Add more logs
        retrieved.log("Runtime log");
        
        auto updatedLogs = retrieved.getLogs();
        REQUIRE(updatedLogs.size() == 3);
        REQUIRE(updatedLogs[2] == "Runtime log");
    }
    
    SECTION("Service replacement preserves type safety") {
        // Register first implementation
        auto serviceA = std::make_unique<TestServiceA>("TypeSafe1", 1);
        ServiceLocator::registerService<TestServiceA>(std::move(serviceA));
        
        TestServiceA& first = ServiceLocator::getService<TestServiceA>();
        REQUIRE(first.getName() == "TypeSafe1");
        
        // Replace with another instance of same type
        auto serviceA2 = std::make_unique<TestServiceA>("TypeSafe2", 2);
        ServiceLocator::registerService<TestServiceA>(std::move(serviceA2));
        
        TestServiceA& second = ServiceLocator::getService<TestServiceA>();
        REQUIRE(second.getName() == "TypeSafe2");
        REQUIRE(second.getValue() == 2);
    }
}
