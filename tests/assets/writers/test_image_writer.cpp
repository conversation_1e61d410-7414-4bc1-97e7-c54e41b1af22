#include <catch2/catch_test_macros.hpp>

// C++ standard library
#include <memory>
#include <string>
#include <vector>
#include <filesystem>
#include <fstream>
#include <ctime>

// Local includes
#include "assets/writers/image_writer.hpp"
#include "assets/asset_data_types.hpp"
#include "../../mocks/test_mocks.hpp"

using namespace IronFrost;

// Test fixture for ImageWriter that handles temporary directories
class ImageWriterTestFixture {
private:
    std::string testDir_;

public:
    ImageWriterTestFixture() {
        // Create unique test directory
        testDir_ = "test_image_writer_" + std::to_string(std::time(nullptr));
        std::filesystem::create_directories(testDir_);
    }

    ~ImageWriterTestFixture() {
        // Clean up test directory
        std::error_code ec;
        std::filesystem::remove_all(testDir_, ec);
        // Ignore errors during cleanup
    }

    const std::string& getTestDir() const { return testDir_; }

    // Helper to get full path for test file
    std::string getTestPath(const std::string& filename) const {
        return testDir_ + "/" + filename;
    }

    // Helper to check if file was created
    bool fileExists(const std::string& filename) const {
        return std::filesystem::exists(getTestPath(filename));
    }
};

// Custom VFS for ImageWriter testing that writes to test directory
class TestImageWriterVFS : public IVFS {
private:
    std::string testDir_;
    std::string writeDir_;

public:
    TestImageWriterVFS(const std::string& testDir) : testDir_(testDir), writeDir_(testDir) {}

    // IVFS interface implementation
    bool mount(const std::string&, const std::string&, bool) override { return true; }
    bool unmount(const std::string&) override { return true; }
    bool exists(const std::string&) const override { return true; }
    std::string readFile(const std::string&) const override { return ""; }
    std::vector<std::string> listFiles(const std::string&) const override { return {}; }
    bool setWriteDirectory(const std::string& path) override {
        writeDir_ = path;
        return true;
    }
    std::vector<unsigned char> readFileBinary(const std::string&) const override { return {}; }

    bool writeFile(const std::string& path, const std::string& content) override {
        try {
            std::string fullPath = writeDir_ + "/" + path;

            // Create parent directories if needed
            std::filesystem::path filePath(fullPath);
            std::filesystem::create_directories(filePath.parent_path());

            std::ofstream file(fullPath);
            file << content;
            return file.good();
        } catch (...) {
            return false;
        }
    }

    bool writeFileBinary(const std::string& path, const std::vector<unsigned char>& content) override {
        try {
            std::string fullPath = writeDir_ + "/" + path;

            // Create parent directories if needed
            std::filesystem::path filePath(fullPath);
            std::filesystem::create_directories(filePath.parent_path());

            std::ofstream file(fullPath, std::ios::binary);
            file.write(reinterpret_cast<const char*>(content.data()), content.size());
            return file.good();
        } catch (...) {
            return false;
        }
    }
};

// Helper function to create test image data
std::unique_ptr<ImageData> createTestImageData(int width, int height, int channels) {
    auto imageData = std::make_unique<ImageData>();
    imageData->width = width;
    imageData->height = height;
    imageData->channels = channels;
    
    size_t dataSize = width * height * channels;
    auto data = std::make_shared<unsigned char[]>(dataSize);
    
    // Fill with test pattern
    for (size_t i = 0; i < dataSize; ++i) {
        data[i] = static_cast<unsigned char>(i % 256);
    }
    
    imageData->data = std::static_pointer_cast<void>(data);
    return imageData;
}

// Helper function to create test image data with specific pattern
std::unique_ptr<ImageData> createTestImageDataWithPattern(int width, int height, int channels, const std::string& pattern = "default") {
    auto imageData = std::make_unique<ImageData>();
    imageData->width = width;
    imageData->height = height;
    imageData->channels = channels;

    size_t dataSize = width * height * channels;
    auto data = std::make_shared<unsigned char[]>(dataSize);

    if (pattern == "checkerboard") {
        // Create checkerboard pattern
        for (int y = 0; y < height; ++y) {
            for (int x = 0; x < width; ++x) {
                int index = (y * width + x) * channels;
                bool isWhite = (x + y) % 2 == 0;

                for (int c = 0; c < channels; ++c) {
                    data[index + c] = isWhite ? 255 : 0;
                }
            }
        }
    } else if (pattern == "gradient") {
        // Create gradient pattern
        for (int y = 0; y < height; ++y) {
            for (int x = 0; x < width; ++x) {
                int index = (y * width + x) * channels;

                if (channels >= 1) data[index] = static_cast<unsigned char>((x * 255) / std::max(1, width - 1));     // R gradient
                if (channels >= 2) data[index + 1] = static_cast<unsigned char>((y * 255) / std::max(1, height - 1)); // G gradient
                if (channels >= 3) data[index + 2] = 128;                                                              // B constant
                if (channels >= 4) data[index + 3] = 255;                                                              // A opaque
            }
        }
    } else {
        // Default test pattern
        for (size_t i = 0; i < dataSize; ++i) {
            data[i] = static_cast<unsigned char>(i % 256);
        }
    }

    imageData->data = std::static_pointer_cast<void>(data);
    return imageData;
}

TEST_CASE("ImageWriter basic functionality", "[assets][writers][image_writer][basic]") {
    SECTION("Constructor with VFS reference") {
        ImageWriterTestFixture fixture;
        TestImageWriterVFS testVFS(fixture.getTestDir());
        ImageWriter writer(testVFS);

        // Should not crash and should be properly initialized
        // Constructor should accept VFS reference without issues
        REQUIRE(true); // Constructor completed successfully
    }
}

TEST_CASE("ImageWriter saveImage functionality", "[assets][writers][image_writer][save]") {
    SECTION("Save valid image data") {
        ImageWriterTestFixture fixture;
        TestImageWriterVFS testVFS(fixture.getTestDir());

        // Set up VFS write directory
        testVFS.setWriteDirectory(fixture.getTestDir());

        ImageWriter writer(testVFS);

        auto imageData = createTestImageData(64, 64, 3);
        std::string fileName = "test_image.png";

        bool result = writer.saveImage(*imageData, fileName);

        // Should succeed with valid image data
        REQUIRE(result == true);

        // Verify file was created
        REQUIRE(fixture.fileExists(fileName));
    }
    
    SECTION("Save images with different dimensions") {
        ImageWriterTestFixture fixture;
        TestImageWriterVFS testVFS(fixture.getTestDir());
        testVFS.setWriteDirectory(fixture.getTestDir());
        ImageWriter writer(testVFS);

        // Test various dimensions
        std::vector<std::tuple<int, int, int>> testCases = {
            {32, 32, 3},    // Small RGB
            {128, 128, 4},  // Medium RGBA
            {256, 256, 1},  // Large grayscale
            {512, 256, 3},  // Rectangular RGB
            {1, 1, 3},      // Minimum size
            {1024, 1024, 4} // Large RGBA
        };

        for (const auto& [width, height, channels] : testCases) {
            auto imageData = createTestImageData(width, height, channels);
            std::string fileName = "image_" + std::to_string(width) + "x" +
                                 std::to_string(height) + "_" +
                                 std::to_string(channels) + ".png";

            bool result = writer.saveImage(*imageData, fileName);
            REQUIRE(result == true);
            REQUIRE(fixture.fileExists(fileName));
        }
    }
    
    SECTION("Save images with different channel counts") {
        ImageWriterTestFixture fixture;
        TestImageWriterVFS testVFS(fixture.getTestDir());
        testVFS.setWriteDirectory(fixture.getTestDir());
        ImageWriter writer(testVFS);

        // Test different channel configurations
        std::vector<int> channelCounts = {1, 2, 3, 4};

        for (int channels : channelCounts) {
            auto imageData = createTestImageData(64, 64, channels);
            std::string fileName = "image_" + std::to_string(channels) + "ch.png";

            bool result = writer.saveImage(*imageData, fileName);
            REQUIRE(result == true);
            REQUIRE(fixture.fileExists(fileName));
        }
    }
}

TEST_CASE("ImageWriter edge cases", "[assets][writers][image_writer][edge_cases]") {
    SECTION("Save image with minimum dimensions") {
        ImageWriterTestFixture fixture;
        TestImageWriterVFS testVFS(fixture.getTestDir());
        testVFS.setWriteDirectory(fixture.getTestDir());
        ImageWriter writer(testVFS);

        auto imageData = createTestImageData(1, 1, 1);
        std::string fileName = "minimal_image.png";

        bool result = writer.saveImage(*imageData, fileName);
        REQUIRE(result == true);
        REQUIRE(fixture.fileExists(fileName));
    }

    SECTION("Save image with large dimensions") {
        ImageWriterTestFixture fixture;
        TestImageWriterVFS testVFS(fixture.getTestDir());
        testVFS.setWriteDirectory(fixture.getTestDir());
        ImageWriter writer(testVFS);

        auto imageData = createTestImageData(2048, 2048, 4);
        std::string fileName = "large_image.png";

        bool result = writer.saveImage(*imageData, fileName);
        REQUIRE(result == true);
        REQUIRE(fixture.fileExists(fileName));
    }
    
    SECTION("Save image with zero-filled data") {
        ImageWriterTestFixture fixture;
        TestImageWriterVFS testVFS(fixture.getTestDir());
        testVFS.setWriteDirectory(fixture.getTestDir());
        ImageWriter writer(testVFS);

        auto imageData = std::make_unique<ImageData>();
        imageData->width = 64;
        imageData->height = 64;
        imageData->channels = 3;

        size_t dataSize = 64 * 64 * 3;
        auto data = std::make_shared<unsigned char[]>(dataSize);
        // Zero-filled data (all black image)
        std::memset(data.get(), 0, dataSize);
        imageData->data = std::static_pointer_cast<void>(data);

        std::string fileName = "zero_filled_image.png";

        bool result = writer.saveImage(*imageData, fileName);
        REQUIRE(result == true);
        REQUIRE(fixture.fileExists(fileName));
    }

    SECTION("Save image with maximum values") {
        ImageWriterTestFixture fixture;
        TestImageWriterVFS testVFS(fixture.getTestDir());
        testVFS.setWriteDirectory(fixture.getTestDir());
        ImageWriter writer(testVFS);

        auto imageData = std::make_unique<ImageData>();
        imageData->width = 32;
        imageData->height = 32;
        imageData->channels = 4;

        size_t dataSize = 32 * 32 * 4;
        auto data = std::make_shared<unsigned char[]>(dataSize);
        // Maximum values (all white image)
        std::memset(data.get(), 255, dataSize);
        imageData->data = std::static_pointer_cast<void>(data);

        std::string fileName = "max_values_image.png";

        bool result = writer.saveImage(*imageData, fileName);
        REQUIRE(result == true);
        REQUIRE(fixture.fileExists(fileName));
    }
}

TEST_CASE("ImageWriter file path handling", "[assets][writers][image_writer][paths]") {
    SECTION("Save with different file extensions") {
        ImageWriterTestFixture fixture;
        TestImageWriterVFS testVFS(fixture.getTestDir());
        ImageWriter writer(testVFS);

        testVFS.setWriteDirectory(fixture.getTestDir());
        auto imageData = createTestImageData(64, 64, 3);

        // Test different extensions (though implementation only supports PNG)
        // Note: Only test simple filenames as STB doesn't create directories
        std::vector<std::string> filePaths = {
            "image.png",
            "image.PNG",
            "image_simple.png",
            "test_image.png"
        };

        for (const auto& fileName : filePaths) {
            bool result = writer.saveImage(*imageData, fileName);
            REQUIRE(result == true);
            REQUIRE(fixture.fileExists(fileName));
        }
    }
    
    SECTION("Save with special characters in filename") {
        ImageWriterTestFixture fixture;
        TestImageWriterVFS testVFS(fixture.getTestDir());
        ImageWriter writer(testVFS);

        testVFS.setWriteDirectory(fixture.getTestDir());
        auto imageData = createTestImageData(32, 32, 3);

        std::vector<std::string> specialPaths = {
            "image_with_underscores.png",
            "image-with-dashes.png",
            "image with spaces.png",
            "image.with.dots.png",
            "image123numbers.png"
        };

        for (const auto& fileName : specialPaths) {
            bool result = writer.saveImage(*imageData, fileName);
            REQUIRE(result == true);
            REQUIRE(fixture.fileExists(fileName));
        }
    }

    SECTION("Save with very long filename") {
        ImageWriterTestFixture fixture;
        TestImageWriterVFS testVFS(fixture.getTestDir());
        ImageWriter writer(testVFS);

        testVFS.setWriteDirectory(fixture.getTestDir());
        auto imageData = createTestImageData(32, 32, 3);

        // Create a long but reasonable filename
        std::string longName = "very_long_image_filename_that_tests_the_limits_of_reasonable_naming_conventions_but_should_still_work_fine.png";

        bool result = writer.saveImage(*imageData, longName);
        REQUIRE(result == true);
        REQUIRE(fixture.fileExists(longName));
    }
}

TEST_CASE("ImageWriter data integrity", "[assets][writers][image_writer][integrity]") {
    SECTION("Save image with checkerboard pattern") {
        ImageWriterTestFixture fixture;
        TestImageWriterVFS testVFS(fixture.getTestDir());
        ImageWriter writer(testVFS);

        testVFS.setWriteDirectory(fixture.getTestDir());
        auto imageData = createTestImageDataWithPattern(8, 8, 3, "checkerboard");

        std::string fileName = "checkerboard_image.png";

        bool result = writer.saveImage(*imageData, fileName);
        REQUIRE(result == true);
        REQUIRE(fixture.fileExists(fileName));
    }
    
    SECTION("Save image with gradient pattern") {
        ImageWriterTestFixture fixture;
        TestImageWriterVFS testVFS(fixture.getTestDir());
        ImageWriter writer(testVFS);

        testVFS.setWriteDirectory(fixture.getTestDir());
        auto imageData = createTestImageDataWithPattern(256, 256, 3, "gradient");

        std::string fileName = "gradient_image.png";

        bool result = writer.saveImage(*imageData, fileName);
        REQUIRE(result == true);
        REQUIRE(fixture.fileExists(fileName));
    }
}

TEST_CASE("ImageWriter real-world scenarios", "[assets][writers][image_writer][real_world]") {
    SECTION("Save game texture") {
        ImageWriterTestFixture fixture;
        TestImageWriterVFS testVFS(fixture.getTestDir());
        ImageWriter writer(testVFS);

        testVFS.setWriteDirectory(fixture.getTestDir());
        // Create a typical game texture with RGBA for transparency
        auto imageData = createTestImageData(512, 512, 4);

        std::string fileName = "game_texture.png";

        bool result = writer.saveImage(*imageData, fileName);
        REQUIRE(result == true);
        REQUIRE(fixture.fileExists(fileName));
    }
    
    SECTION("Save UI element texture") {
        ImageWriterTestFixture fixture;
        TestImageWriterVFS testVFS(fixture.getTestDir());
        ImageWriter writer(testVFS);

        testVFS.setWriteDirectory(fixture.getTestDir());
        // Create UI element texture with RGBA for transparency
        auto imageData = createTestImageData(256, 256, 4);

        std::string fileName = "ui_element.png";

        bool result = writer.saveImage(*imageData, fileName);
        REQUIRE(result == true);
        REQUIRE(fixture.fileExists(fileName));
    }
    
    SECTION("Save font texture") {
        ImageWriterTestFixture fixture;
        TestImageWriterVFS testVFS(fixture.getTestDir());
        ImageWriter writer(testVFS);

        testVFS.setWriteDirectory(fixture.getTestDir());
        // Create font texture (grayscale for font rendering)
        auto imageData = createTestImageData(1024, 1024, 1);

        std::string fileName = "font_texture.png";

        bool result = writer.saveImage(*imageData, fileName);
        REQUIRE(result == true);
        REQUIRE(fixture.fileExists(fileName));
    }

    SECTION("Save screenshot") {
        ImageWriterTestFixture fixture;
        TestImageWriterVFS testVFS(fixture.getTestDir());
        ImageWriter writer(testVFS);

        testVFS.setWriteDirectory(fixture.getTestDir());
        // Create screenshot-like image (RGB, typical screen resolution)
        auto imageData = createTestImageData(1920, 1080, 3);

        std::string fileName = "screenshot.png";

        bool result = writer.saveImage(*imageData, fileName);
        REQUIRE(result == true);
        REQUIRE(fixture.fileExists(fileName));
    }
}
