#include <catch2/catch_test_macros.hpp>
#include <catch2/matchers/catch_matchers_floating_point.hpp>

// Local includes
#include "../../../src/assets/data_types/heightmap_data.hpp"

using namespace IronFrost;
using Catch::Matchers::WithinAbs;

// Helper function to create a simple test heightmap
HeightmapData createTestHeightmap(int width, int height) {
    HeightmapData heightmap;
    heightmap.width = width;
    heightmap.height = height;
    heightmap.heightmap.resize(width * height);

    // Fill with a simple pattern: height = x + y
    for (int y = 0; y < height; ++y) {
        for (int x = 0; x < width; ++x) {
            heightmap.heightmap[y * width + x] = static_cast<float>(x + y);
        }
    }

    return heightmap;
}

TEST_CASE("HeightmapData construction and basic properties", "[assets][heightmap_data][construction]") {
    SECTION("Default constructor creates empty heightmap") {
        HeightmapData heightmap;

        REQUIRE(heightmap.width == 0);
        REQUIRE(heightmap.height == 0);
        REQUIRE(heightmap.heightmap.empty());
    }
    
    SECTION("Manual construction with data") {
        HeightmapData heightmap;
        heightmap.width = 3;
        heightmap.height = 2;
        heightmap.heightmap = {1.0f, 2.0f, 3.0f, 4.0f, 5.0f, 6.0f};

        REQUIRE(heightmap.width == 3);
        REQUIRE(heightmap.height == 2);
        REQUIRE(heightmap.heightmap.size() == 6);
        REQUIRE(heightmap.heightmap[0] == 1.0f);
        REQUIRE(heightmap.heightmap[5] == 6.0f);
    }
}

TEST_CASE("HeightmapData getHeight method", "[assets][heightmap_data][get_height]") {
    SECTION("Valid coordinates return correct height") {
        auto heightmap = createTestHeightmap(4, 3);
        
        // Test corner values
        REQUIRE(heightmap.getHeight(0, 0) == 0.0f);  // 0 + 0
        REQUIRE(heightmap.getHeight(3, 0) == 3.0f);  // 3 + 0
        REQUIRE(heightmap.getHeight(0, 2) == 2.0f);  // 0 + 2
        REQUIRE(heightmap.getHeight(3, 2) == 5.0f);  // 3 + 2
        
        // Test middle values
        REQUIRE(heightmap.getHeight(1, 1) == 2.0f);  // 1 + 1
        REQUIRE(heightmap.getHeight(2, 1) == 3.0f);  // 2 + 1
    }
    
    SECTION("Out of bounds coordinates return 0.0") {
        auto heightmap = createTestHeightmap(3, 3);
        
        // Negative coordinates
        REQUIRE(heightmap.getHeight(-1, 0) == 0.0f);
        REQUIRE(heightmap.getHeight(0, -1) == 0.0f);
        REQUIRE(heightmap.getHeight(-1, -1) == 0.0f);
        
        // Coordinates beyond bounds
        REQUIRE(heightmap.getHeight(3, 0) == 0.0f);  // width is 3, so max valid x is 2
        REQUIRE(heightmap.getHeight(0, 3) == 0.0f);  // height is 3, so max valid y is 2
        REQUIRE(heightmap.getHeight(3, 3) == 0.0f);
        
        // Far out of bounds
        REQUIRE(heightmap.getHeight(100, 100) == 0.0f);
    }
    
    SECTION("Edge case: empty heightmap") {
        HeightmapData heightmap;  // Default constructed, empty
        
        REQUIRE(heightmap.getHeight(0, 0) == 0.0f);
        REQUIRE(heightmap.getHeight(1, 1) == 0.0f);
    }
    
    SECTION("Edge case: 1x1 heightmap") {
        HeightmapData heightmap;
        heightmap.width = 1;
        heightmap.height = 1;
        heightmap.heightmap = {42.0f};
        
        REQUIRE(heightmap.getHeight(0, 0) == 42.0f);
        REQUIRE(heightmap.getHeight(1, 0) == 0.0f);  // Out of bounds
        REQUIRE(heightmap.getHeight(0, 1) == 0.0f);  // Out of bounds
    }
}

TEST_CASE("HeightmapData getInterpolatedHeight method", "[assets][heightmap_data][interpolated_height]") {
    SECTION("Integer coordinates match getHeight") {
        auto heightmap = createTestHeightmap(4, 3);
        
        // Integer coordinates should give same result as getHeight
        REQUIRE_THAT(heightmap.getInterpolatedHeight(0.0f, 0.0f), WithinAbs(heightmap.getHeight(0, 0), 0.001f));
        REQUIRE_THAT(heightmap.getInterpolatedHeight(1.0f, 1.0f), WithinAbs(heightmap.getHeight(1, 1), 0.001f));
        REQUIRE_THAT(heightmap.getInterpolatedHeight(2.0f, 1.0f), WithinAbs(heightmap.getHeight(2, 1), 0.001f));
    }
    
    SECTION("Interpolation between known values") {
        HeightmapData heightmap;
        heightmap.width = 2;
        heightmap.height = 2;
        heightmap.heightmap = {
            0.0f, 10.0f,  // Row 0: (0,0)=0, (1,0)=10
            20.0f, 30.0f  // Row 1: (0,1)=20, (1,1)=30
        };
        
        // Test center point (0.5, 0.5) - should be average of all four corners
        float centerHeight = heightmap.getInterpolatedHeight(0.5f, 0.5f);
        float expectedCenter = (0.0f + 10.0f + 20.0f + 30.0f) / 4.0f;  // 15.0f
        REQUIRE_THAT(centerHeight, WithinAbs(expectedCenter, 0.001f));
        
        // Test midpoint of bottom edge (0.5, 0.0)
        float bottomMid = heightmap.getInterpolatedHeight(0.5f, 0.0f);
        float expectedBottomMid = (0.0f + 10.0f) / 2.0f;  // 5.0f
        REQUIRE_THAT(bottomMid, WithinAbs(expectedBottomMid, 0.001f));
        
        // Test midpoint of left edge (0.0, 0.5)
        float leftMid = heightmap.getInterpolatedHeight(0.0f, 0.5f);
        float expectedLeftMid = (0.0f + 20.0f) / 2.0f;  // 10.0f
        REQUIRE_THAT(leftMid, WithinAbs(expectedLeftMid, 0.001f));
    }
    
    SECTION("Interpolation with fractional coordinates") {
        auto heightmap = createTestHeightmap(3, 3);
        
        // Test quarter points
        float h_quarter = heightmap.getInterpolatedHeight(0.25f, 0.25f);
        float h_three_quarter = heightmap.getInterpolatedHeight(0.75f, 0.75f);
        
        // These should be different from corner values
        REQUIRE(h_quarter != heightmap.getHeight(0, 0));
        REQUIRE(h_three_quarter != heightmap.getHeight(1, 1));
        
        // Interpolated values should be reasonable (between min and max of surrounding points)
        REQUIRE(h_quarter >= 0.0f);  // Should be >= minimum surrounding value
        REQUIRE(h_quarter <= 2.0f);  // Should be <= maximum surrounding value
    }
    
    SECTION("Out of bounds interpolation blends with zero values") {
        auto heightmap = createTestHeightmap(2, 2);
        // heightmap values: (0,0)=0, (1,0)=1, (0,1)=1, (1,1)=2

        // Negative coordinates will interpolate between 0.0f (out of bounds) and valid values
        float negativeX = heightmap.getInterpolatedHeight(-0.5f, 0.5f);
        REQUIRE(negativeX >= 0.0f);  // Should be >= 0 since it blends with out-of-bounds (0.0f)
        REQUIRE(negativeX < 2.0f);   // Should be < max value since it's partially out of bounds

        float negativeY = heightmap.getInterpolatedHeight(0.5f, -0.5f);
        REQUIRE(negativeY >= 0.0f);
        REQUIRE(negativeY < 2.0f);

        // Beyond bounds - similar behavior
        float beyondX = heightmap.getInterpolatedHeight(2.5f, 0.5f);
        REQUIRE(beyondX >= 0.0f);
        REQUIRE(beyondX < 2.0f);

        float beyondY = heightmap.getInterpolatedHeight(0.5f, 2.5f);
        REQUIRE(beyondY >= 0.0f);
        REQUIRE(beyondY < 2.0f);

        // Completely out of bounds should return 0.0f
        REQUIRE(heightmap.getInterpolatedHeight(-1.5f, -1.5f) == 0.0f);
        REQUIRE(heightmap.getInterpolatedHeight(3.5f, 3.5f) == 0.0f);
    }
}

TEST_CASE("HeightmapData edge cases and robustness", "[assets][heightmap_data][edge_cases]") {
    SECTION("Empty heightmap interpolation") {
        HeightmapData heightmap;  // Default constructed

        REQUIRE(heightmap.getInterpolatedHeight(0.0f, 0.0f) == 0.0f);
        REQUIRE(heightmap.getInterpolatedHeight(0.5f, 0.5f) == 0.0f);
        REQUIRE(heightmap.getInterpolatedHeight(1.0f, 1.0f) == 0.0f);
    }

    SECTION("Single pixel heightmap interpolation") {
        HeightmapData heightmap;
        heightmap.width = 1;
        heightmap.height = 1;
        heightmap.heightmap = {5.0f};

        // At the single pixel (0,0)
        REQUIRE_THAT(heightmap.getInterpolatedHeight(0.0f, 0.0f), WithinAbs(5.0f, 0.001f));

        // Fractional coordinates will interpolate between the single pixel and out-of-bounds (0.0f)
        // For (0.5, 0.5): x0=0, x1=1, y0=0, y1=1, dx=0.5, dy=0.5
        // h00=5.0f, h01=0.0f, h10=0.0f, h11=0.0f
        // h0 = 5.0f + (0.0f - 5.0f) * 0.5 = 2.5f
        // h1 = 0.0f + (0.0f - 0.0f) * 0.5 = 0.0f
        // result = 2.5f + (0.0f - 2.5f) * 0.5 = 1.25f
        REQUIRE_THAT(heightmap.getInterpolatedHeight(0.5f, 0.5f), WithinAbs(1.25f, 0.001f));
    }

    SECTION("Large heightmap coordinates") {
        auto heightmap = createTestHeightmap(1000, 1000);

        // Test coordinates near the edges
        REQUIRE(heightmap.getHeight(999, 999) == 1998.0f);  // 999 + 999
        REQUIRE(heightmap.getHeight(1000, 999) == 0.0f);    // Out of bounds

        // Test interpolation near edges
        float interpolated = heightmap.getInterpolatedHeight(998.5f, 998.5f);
        REQUIRE(interpolated > 0.0f);  // Should be a reasonable interpolated value
    }

    SECTION("Negative height values") {
        HeightmapData heightmap;
        heightmap.width = 2;
        heightmap.height = 2;
        heightmap.heightmap = {-10.0f, -5.0f, 0.0f, 5.0f};

        REQUIRE(heightmap.getHeight(0, 0) == -10.0f);
        REQUIRE(heightmap.getHeight(1, 1) == 5.0f);

        // Interpolation should handle negative values correctly
        float interpolated = heightmap.getInterpolatedHeight(0.5f, 0.5f);
        float expected = (-10.0f + -5.0f + 0.0f + 5.0f) / 4.0f;  // -2.5f
        REQUIRE_THAT(interpolated, WithinAbs(expected, 0.001f));
    }

    SECTION("Zero height values") {
        HeightmapData heightmap;
        heightmap.width = 3;
        heightmap.height = 3;
        heightmap.heightmap = {0.0f, 0.0f, 0.0f, 0.0f, 0.0f, 0.0f, 0.0f, 0.0f, 0.0f};

        REQUIRE(heightmap.getHeight(1, 1) == 0.0f);
        REQUIRE(heightmap.getInterpolatedHeight(1.5f, 1.5f) == 0.0f);
    }
}



TEST_CASE("HeightmapData performance characteristics", "[assets][heightmap_data][performance]") {
    SECTION("Large heightmap access performance") {
        const int size = 512;  // 512x512 heightmap
        auto heightmap = createTestHeightmap(size, size);

        // Test that we can access many points quickly
        float totalHeight = 0.0f;
        for (int i = 0; i < 1000; ++i) {
            int x = i % size;
            int y = (i * 7) % size;  // Some pseudo-random pattern
            totalHeight += heightmap.getHeight(x, y);
        }

        REQUIRE(totalHeight > 0.0f);  // Should have accumulated some height values
    }

    SECTION("Interpolation performance with many samples") {
        auto heightmap = createTestHeightmap(100, 100);

        float totalInterpolated = 0.0f;
        for (int i = 0; i < 1000; ++i) {
            float x = (i % 99) + 0.5f;  // Fractional coordinates
            float y = ((i * 3) % 99) + 0.3f;
            totalInterpolated += heightmap.getInterpolatedHeight(x, y);
        }

        REQUIRE(totalInterpolated > 0.0f);
    }
}

TEST_CASE("HeightmapData real-world usage scenarios", "[assets][heightmap_data][real_world]") {
    SECTION("Terrain heightmap simulation") {
        // Simulate a small terrain patch
        HeightmapData terrain;
        terrain.width = 5;
        terrain.height = 5;

        // Create a simple hill in the center
        terrain.heightmap = {
            0.0f, 0.5f, 1.0f, 0.5f, 0.0f,
            0.5f, 2.0f, 3.0f, 2.0f, 0.5f,
            1.0f, 3.0f, 5.0f, 3.0f, 1.0f,  // Peak at center
            0.5f, 2.0f, 3.0f, 2.0f, 0.5f,
            0.0f, 0.5f, 1.0f, 0.5f, 0.0f
        };

        // Test peak
        REQUIRE(terrain.getHeight(2, 2) == 5.0f);

        // Test slopes
        REQUIRE(terrain.getHeight(1, 2) == 3.0f);
        REQUIRE(terrain.getHeight(3, 2) == 3.0f);

        // Test interpolated height on slope
        float slopeHeight = terrain.getInterpolatedHeight(1.5f, 2.0f);
        REQUIRE(slopeHeight > 3.0f);  // Should be between 3.0 and 5.0
        REQUIRE(slopeHeight < 5.0f);
    }

    SECTION("Water level detection") {
        HeightmapData heightmap;
        heightmap.width = 4;
        heightmap.height = 4;
        heightmap.heightmap = {
            -2.0f, -1.0f,  1.0f,  2.0f,
            -1.0f,  0.0f,  2.0f,  3.0f,
             1.0f,  2.0f,  4.0f,  5.0f,
             2.0f,  3.0f,  5.0f,  6.0f
        };

        const float waterLevel = 0.0f;

        // Count underwater points
        int underwaterCount = 0;
        for (int y = 0; y < heightmap.height; ++y) {
            for (int x = 0; x < heightmap.width; ++x) {
                if (heightmap.getHeight(x, y) < waterLevel) {
                    underwaterCount++;
                }
            }
        }

        REQUIRE(underwaterCount == 3);  // Three points below water level

        // Test interpolated height at water boundary
        float boundaryHeight = heightmap.getInterpolatedHeight(1.5f, 0.5f);
        // This should be somewhere around the water level
        REQUIRE(boundaryHeight > -1.0f);
        REQUIRE(boundaryHeight < 3.0f);
    }
}
