#include <catch2/catch_test_macros.hpp>

// C++ standard library
#include <memory>
#include <vector>
#include <cstring>

// Third-party libraries
#include <glm/glm.hpp>

// Local includes
#include "assets/asset_data_types.hpp"
#include "assets/processors/image_atlas_processor.hpp"

using namespace IronFrost;

// Helper function to create test image data
std::unique_ptr<ImageData> createTestImageData(int width, int height, int channels, unsigned char fillValue = 0) {
    auto imageData = std::make_unique<ImageData>();
    imageData->width = width;
    imageData->height = height;
    imageData->channels = channels;
    
    size_t dataSize = width * height * channels;
    auto data = std::make_shared<unsigned char[]>(dataSize);
    
    // Fill with specified value or pattern
    for (size_t i = 0; i < dataSize; ++i) {
        data[i] = fillValue != 0 ? fillValue : static_cast<unsigned char>(i % 256);
    }
    
    imageData->data = std::static_pointer_cast<void>(data);
    return imageData;
}

// Helper function to create colored test image
std::unique_ptr<ImageData> createColoredImageData(int width, int height, int channels,
                                                  unsigned char r, unsigned char g = 0, unsigned char b = 0, unsigned char a = 255) {
    auto imageData = std::make_unique<ImageData>();
    imageData->width = width;
    imageData->height = height;
    imageData->channels = channels;

    size_t dataSize = width * height * channels;
    auto data = std::make_shared<unsigned char[]>(dataSize);

    for (int y = 0; y < height; ++y) {
        for (int x = 0; x < width; ++x) {
            size_t pixelIndex = (y * width + x) * channels;
            data[pixelIndex] = r;
            if (channels > 1) data[pixelIndex + 1] = g;
            if (channels > 2) data[pixelIndex + 2] = b;
            if (channels > 3) data[pixelIndex + 3] = a;
        }
    }

    imageData->data = std::static_pointer_cast<void>(data);
    return imageData;
}

// Helper function to verify pixel color in atlas
bool verifyPixelColor(const ImageData* atlasData, int x, int y, 
                      unsigned char expectedR, unsigned char expectedG = 0, 
                      unsigned char expectedB = 0, unsigned char expectedA = 255) {
    if (x < 0 || x >= atlasData->width || y < 0 || y >= atlasData->height) {
        return false;
    }
    
    const unsigned char* data = atlasData->get8BitData();
    size_t pixelIndex = (y * atlasData->width + x) * atlasData->channels;
    
    bool rMatch = data[pixelIndex] == expectedR;
    bool gMatch = atlasData->channels > 1 ? data[pixelIndex + 1] == expectedG : true;
    bool bMatch = atlasData->channels > 2 ? data[pixelIndex + 2] == expectedB : true;
    bool aMatch = atlasData->channels > 3 ? data[pixelIndex + 3] == expectedA : true;
    
    return rMatch && gMatch && bMatch && aMatch;
}

TEST_CASE("ImageAtlasProcessor basic functionality", "[assets][processors][image_atlas][basic]") {
    SECTION("Default constructor creates empty processor") {
        ImageAtlasProcessor processor;
        
        // Should return nullptr for empty processor
        auto result = processor.get();
        REQUIRE(result == nullptr);
    }
    
    SECTION("Single image atlas") {
        ImageAtlasProcessor processor;
        
        auto image = createColoredImageData(32, 32, 3, 255, 0, 0); // Red image
        processor.addImage(std::move(image));
        
        auto atlas = processor.get();
        REQUIRE(atlas != nullptr);
        REQUIRE(atlas->imageData != nullptr);
        REQUIRE(atlas->entries.size() == 1);
        
        // Check atlas dimensions (32x32 image + 8 padding on each side = 48x48)
        REQUIRE(atlas->imageData->width == 48);
        REQUIRE(atlas->imageData->height == 48);
        REQUIRE(atlas->imageData->channels == 3);
        
        // Check entry data
        const auto& entry = atlas->entries[0];
        REQUIRE(entry.position.x == 8.0f); // padding
        REQUIRE(entry.position.y == 8.0f); // padding
        REQUIRE(entry.width == 32);
        REQUIRE(entry.height == 32);
        
        // Check UV coordinates
        REQUIRE(entry.uvMin.x == 8.0f / 48.0f);
        REQUIRE(entry.uvMin.y == 8.0f / 48.0f);
        REQUIRE(entry.uvMax.x == 40.0f / 48.0f); // 8 + 32
        REQUIRE(entry.uvMax.y == 40.0f / 48.0f); // 8 + 32
        
        // Verify the red color is in the atlas at the correct position
        REQUIRE(verifyPixelColor(atlas->imageData.get(), 8, 8, 255, 0, 0));
        REQUIRE(verifyPixelColor(atlas->imageData.get(), 39, 39, 255, 0, 0));
    }
    
    SECTION("Two images atlas (1x2 grid)") {
        ImageAtlasProcessor processor;
        
        auto redImage = createColoredImageData(16, 16, 4, 255, 0, 0, 255);   // Red
        auto blueImage = createColoredImageData(16, 16, 4, 0, 0, 255, 255);  // Blue
        
        processor.addImage(std::move(redImage));
        processor.addImage(std::move(blueImage));
        
        auto atlas = processor.get();
        REQUIRE(atlas != nullptr);
        REQUIRE(atlas->entries.size() == 2);
        
        // Grid size should be 2 (ceil(sqrt(2)) = 2)
        // Atlas dimensions: 2 * (16 + 8) + 8 = 56x56
        REQUIRE(atlas->imageData->width == 56);
        REQUIRE(atlas->imageData->height == 56);
        REQUIRE(atlas->imageData->channels == 4);
        
        // Check first entry (red image)
        const auto& entry1 = atlas->entries[0];
        REQUIRE(entry1.position.x == 8.0f);
        REQUIRE(entry1.position.y == 8.0f);
        REQUIRE(entry1.width == 16);
        REQUIRE(entry1.height == 16);
        
        // Check second entry (blue image)
        const auto& entry2 = atlas->entries[1];
        REQUIRE(entry2.position.x == 32.0f); // 8 + 16 + 8
        REQUIRE(entry2.position.y == 8.0f);
        REQUIRE(entry2.width == 16);
        REQUIRE(entry2.height == 16);
        
        // Verify colors in atlas
        REQUIRE(verifyPixelColor(atlas->imageData.get(), 8, 8, 255, 0, 0, 255));   // Red
        REQUIRE(verifyPixelColor(atlas->imageData.get(), 32, 8, 0, 0, 255, 255));  // Blue
    }
    
    SECTION("Four images atlas (2x2 grid)") {
        ImageAtlasProcessor processor;
        
        auto redImage = createColoredImageData(8, 8, 3, 255, 0, 0);     // Red
        auto greenImage = createColoredImageData(8, 8, 3, 0, 255, 0);   // Green
        auto blueImage = createColoredImageData(8, 8, 3, 0, 0, 255);    // Blue
        auto whiteImage = createColoredImageData(8, 8, 3, 255, 255, 255); // White
        
        processor.addImage(std::move(redImage));
        processor.addImage(std::move(greenImage));
        processor.addImage(std::move(blueImage));
        processor.addImage(std::move(whiteImage));
        
        auto atlas = processor.get();
        REQUIRE(atlas != nullptr);
        REQUIRE(atlas->entries.size() == 4);
        
        // Grid size should be 2 (ceil(sqrt(4)) = 2)
        // Atlas dimensions: 2 * (8 + 8) + 8 = 40x40
        REQUIRE(atlas->imageData->width == 40);
        REQUIRE(atlas->imageData->height == 40);
        
        // Check positions
        REQUIRE(atlas->entries[0].position.x == 8.0f);   // Red: top-left
        REQUIRE(atlas->entries[0].position.y == 8.0f);
        REQUIRE(atlas->entries[1].position.x == 24.0f);  // Green: top-right
        REQUIRE(atlas->entries[1].position.y == 8.0f);
        REQUIRE(atlas->entries[2].position.x == 8.0f);   // Blue: bottom-left
        REQUIRE(atlas->entries[2].position.y == 24.0f);
        REQUIRE(atlas->entries[3].position.x == 24.0f);  // White: bottom-right
        REQUIRE(atlas->entries[3].position.y == 24.0f);
        
        // Verify colors
        REQUIRE(verifyPixelColor(atlas->imageData.get(), 8, 8, 255, 0, 0));      // Red
        REQUIRE(verifyPixelColor(atlas->imageData.get(), 24, 8, 0, 255, 0));     // Green
        REQUIRE(verifyPixelColor(atlas->imageData.get(), 8, 24, 0, 0, 255));     // Blue
        REQUIRE(verifyPixelColor(atlas->imageData.get(), 24, 24, 255, 255, 255)); // White
    }
}

TEST_CASE("ImageAtlasProcessor error handling", "[assets][processors][image_atlas][errors]") {
    SECTION("Images with different dimensions throw exception") {
        ImageAtlasProcessor processor;

        auto image1 = createTestImageData(32, 32, 3);
        auto image2 = createTestImageData(16, 16, 3); // Different size

        processor.addImage(std::move(image1));
        processor.addImage(std::move(image2));

        REQUIRE_THROWS_AS(processor.get(), std::runtime_error);
    }

    SECTION("Images with different channel counts throw exception") {
        ImageAtlasProcessor processor;

        auto image1 = createTestImageData(32, 32, 3);
        auto image2 = createTestImageData(32, 32, 4); // Different channels

        processor.addImage(std::move(image1));
        processor.addImage(std::move(image2));

        REQUIRE_THROWS_AS(processor.get(), std::runtime_error);
    }

    SECTION("Mixed dimension and channel differences throw exception") {
        ImageAtlasProcessor processor;

        auto image1 = createTestImageData(32, 32, 3);
        auto image2 = createTestImageData(16, 24, 4); // Different size and channels

        processor.addImage(std::move(image1));
        processor.addImage(std::move(image2));

        REQUIRE_THROWS_AS(processor.get(), std::runtime_error);
    }
}

TEST_CASE("ImageAtlasProcessor edge cases", "[assets][processors][image_atlas][edge_cases]") {
    SECTION("Single pixel images") {
        ImageAtlasProcessor processor;

        auto image = createColoredImageData(1, 1, 3, 128, 64, 32);
        processor.addImage(std::move(image));

        auto atlas = processor.get();
        REQUIRE(atlas != nullptr);
        REQUIRE(atlas->entries.size() == 1);

        // Atlas should be 1 + 8*2 = 17x17
        REQUIRE(atlas->imageData->width == 17);
        REQUIRE(atlas->imageData->height == 17);

        // Verify the single pixel is at position (8, 8)
        REQUIRE(verifyPixelColor(atlas->imageData.get(), 8, 8, 128, 64, 32));
    }

    SECTION("Large number of small images") {
        ImageAtlasProcessor processor;

        // Add 9 images (3x3 grid)
        for (int i = 0; i < 9; ++i) {
            auto image = createColoredImageData(4, 4, 3,
                                               static_cast<unsigned char>(i * 28),
                                               static_cast<unsigned char>(i * 28),
                                               static_cast<unsigned char>(i * 28));
            processor.addImage(std::move(image));
        }

        auto atlas = processor.get();
        REQUIRE(atlas != nullptr);
        REQUIRE(atlas->entries.size() == 9);

        // Grid size should be 3 (ceil(sqrt(9)) = 3)
        // Atlas dimensions: 3 * (4 + 8) + 8 = 44x44
        REQUIRE(atlas->imageData->width == 44);
        REQUIRE(atlas->imageData->height == 44);

        // Check that all entries have correct dimensions
        for (const auto& entry : atlas->entries) {
            REQUIRE(entry.width == 4);
            REQUIRE(entry.height == 4);
        }
    }

    SECTION("Different channel counts") {
        SECTION("Grayscale images (1 channel)") {
            ImageAtlasProcessor processor;

            auto image1 = createColoredImageData(8, 8, 1, 100);
            auto image2 = createColoredImageData(8, 8, 1, 200);

            processor.addImage(std::move(image1));
            processor.addImage(std::move(image2));

            auto atlas = processor.get();
            REQUIRE(atlas != nullptr);
            REQUIRE(atlas->imageData->channels == 1);

            // Verify grayscale values
            REQUIRE(verifyPixelColor(atlas->imageData.get(), 8, 8, 100));
            REQUIRE(verifyPixelColor(atlas->imageData.get(), 24, 8, 200));
        }

        SECTION("RGBA images (4 channels)") {
            ImageAtlasProcessor processor;

            auto image1 = createColoredImageData(8, 8, 4, 255, 0, 0, 128);   // Semi-transparent red
            auto image2 = createColoredImageData(8, 8, 4, 0, 255, 0, 64);    // More transparent green

            processor.addImage(std::move(image1));
            processor.addImage(std::move(image2));

            auto atlas = processor.get();
            REQUIRE(atlas != nullptr);
            REQUIRE(atlas->imageData->channels == 4);

            // Verify RGBA values
            REQUIRE(verifyPixelColor(atlas->imageData.get(), 8, 8, 255, 0, 0, 128));
            REQUIRE(verifyPixelColor(atlas->imageData.get(), 24, 8, 0, 255, 0, 64));
        }
    }
}

TEST_CASE("ImageAtlasProcessor UV coordinate calculations", "[assets][processors][image_atlas][uv]") {
    SECTION("UV coordinates for single image") {
        ImageAtlasProcessor processor;

        auto image = createTestImageData(16, 16, 3);
        processor.addImage(std::move(image));

        auto atlas = processor.get();
        REQUIRE(atlas != nullptr);

        const auto& entry = atlas->entries[0];

        // Atlas is 32x32 (16 + 8*2), image is at (8,8) to (24,24)
        float expectedMinU = 8.0f / 32.0f;   // 0.25
        float expectedMinV = 8.0f / 32.0f;   // 0.25
        float expectedMaxU = 24.0f / 32.0f;  // 0.75
        float expectedMaxV = 24.0f / 32.0f;  // 0.75

        REQUIRE(entry.uvMin.x == expectedMinU);
        REQUIRE(entry.uvMin.y == expectedMinV);
        REQUIRE(entry.uvMax.x == expectedMaxU);
        REQUIRE(entry.uvMax.y == expectedMaxV);
    }

    SECTION("UV coordinates for multiple images") {
        ImageAtlasProcessor processor;

        // Add 4 images in 2x2 grid
        for (int i = 0; i < 4; ++i) {
            auto image = createTestImageData(10, 10, 3);
            processor.addImage(std::move(image));
        }

        auto atlas = processor.get();
        REQUIRE(atlas != nullptr);
        REQUIRE(atlas->entries.size() == 4);

        // Atlas is 44x44 (2 * (10 + 8) + 8)
        float atlasSize = 44.0f;

        // First image: position (8, 8), size 10x10
        const auto& entry0 = atlas->entries[0];
        REQUIRE(entry0.uvMin.x == 8.0f / atlasSize);
        REQUIRE(entry0.uvMin.y == 8.0f / atlasSize);
        REQUIRE(entry0.uvMax.x == 18.0f / atlasSize);
        REQUIRE(entry0.uvMax.y == 18.0f / atlasSize);

        // Second image: position (26, 8), size 10x10
        const auto& entry1 = atlas->entries[1];
        REQUIRE(entry1.uvMin.x == 26.0f / atlasSize);
        REQUIRE(entry1.uvMin.y == 8.0f / atlasSize);
        REQUIRE(entry1.uvMax.x == 36.0f / atlasSize);
        REQUIRE(entry1.uvMax.y == 18.0f / atlasSize);

        // Third image: position (8, 26), size 10x10
        const auto& entry2 = atlas->entries[2];
        REQUIRE(entry2.uvMin.x == 8.0f / atlasSize);
        REQUIRE(entry2.uvMin.y == 26.0f / atlasSize);
        REQUIRE(entry2.uvMax.x == 18.0f / atlasSize);
        REQUIRE(entry2.uvMax.y == 36.0f / atlasSize);

        // Fourth image: position (26, 26), size 10x10
        const auto& entry3 = atlas->entries[3];
        REQUIRE(entry3.uvMin.x == 26.0f / atlasSize);
        REQUIRE(entry3.uvMin.y == 26.0f / atlasSize);
        REQUIRE(entry3.uvMax.x == 36.0f / atlasSize);
        REQUIRE(entry3.uvMax.y == 36.0f / atlasSize);
    }
}

TEST_CASE("ImageAtlasProcessor padding behavior", "[assets][processors][image_atlas][padding]") {
    SECTION("Padding creates border around images") {
        ImageAtlasProcessor processor;

        // Create a distinctive colored image
        auto image = createColoredImageData(4, 4, 3, 255, 128, 64);
        processor.addImage(std::move(image));

        auto atlas = processor.get();
        REQUIRE(atlas != nullptr);

        // Atlas should be 20x20 (4 + 8*2)
        REQUIRE(atlas->imageData->width == 20);
        REQUIRE(atlas->imageData->height == 20);

        // Image should be at position (8, 8) to (11, 11)
        // Check that the image color is present
        REQUIRE(verifyPixelColor(atlas->imageData.get(), 8, 8, 255, 128, 64));
        REQUIRE(verifyPixelColor(atlas->imageData.get(), 11, 11, 255, 128, 64));

        // Check that padding areas have edge-extended colors (from copyImageToAtlas)
        // Left edge padding should have the leftmost pixel color
        REQUIRE(verifyPixelColor(atlas->imageData.get(), 7, 8, 255, 128, 64));  // 1 pixel left
        REQUIRE(verifyPixelColor(atlas->imageData.get(), 4, 8, 255, 128, 64));  // 4 pixels left

        // Right edge padding should have the rightmost pixel color
        REQUIRE(verifyPixelColor(atlas->imageData.get(), 12, 8, 255, 128, 64)); // 1 pixel right
        REQUIRE(verifyPixelColor(atlas->imageData.get(), 15, 8, 255, 128, 64)); // 4 pixels right

        // Top edge padding should have the top pixel color
        REQUIRE(verifyPixelColor(atlas->imageData.get(), 8, 7, 255, 128, 64));  // 1 pixel up
        REQUIRE(verifyPixelColor(atlas->imageData.get(), 8, 4, 255, 128, 64));  // 4 pixels up

        // Bottom edge padding should have the bottom pixel color
        REQUIRE(verifyPixelColor(atlas->imageData.get(), 8, 12, 255, 128, 64)); // 1 pixel down
        REQUIRE(verifyPixelColor(atlas->imageData.get(), 8, 15, 255, 128, 64)); // 4 pixels down
    }

    SECTION("Multiple images maintain proper spacing") {
        ImageAtlasProcessor processor;

        auto redImage = createColoredImageData(6, 6, 3, 255, 0, 0);
        auto blueImage = createColoredImageData(6, 6, 3, 0, 0, 255);

        processor.addImage(std::move(redImage));
        processor.addImage(std::move(blueImage));

        auto atlas = processor.get();
        REQUIRE(atlas != nullptr);

        // Atlas should be 36x36 (2 * (6 + 8) + 8)
        REQUIRE(atlas->imageData->width == 36);
        REQUIRE(atlas->imageData->height == 36);

        // Red image at (8, 8) to (13, 13)
        REQUIRE(verifyPixelColor(atlas->imageData.get(), 8, 8, 255, 0, 0));
        REQUIRE(verifyPixelColor(atlas->imageData.get(), 13, 13, 255, 0, 0));

        // Blue image at (22, 8) to (27, 13)
        REQUIRE(verifyPixelColor(atlas->imageData.get(), 22, 8, 0, 0, 255));
        REQUIRE(verifyPixelColor(atlas->imageData.get(), 27, 13, 0, 0, 255));

        // Check spacing between images (should be padding)
        // The space between red and blue images should have red's right edge color
        REQUIRE(verifyPixelColor(atlas->imageData.get(), 17, 8, 255, 0, 0)); // Red edge extension
        REQUIRE(verifyPixelColor(atlas->imageData.get(), 18, 8, 0, 0, 255));  // Blue edge extension
    }
}
