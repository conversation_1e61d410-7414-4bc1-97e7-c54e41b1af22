#include <catch2/catch_test_macros.hpp>

// C++ standard library
#include <string>
#include <optional>

// Third-party libraries
#include <glm/glm.hpp>
#include <nlohmann/json.hpp>

// Local includes
#include "assets/asset_primitive_types.hpp"

using namespace IronFrost;
using json = nlohmann::json;

TEST_CASE("PrimitiveParams basic functionality", "[assets][primitive_params][basic]") {
    SECTION("Default constructor creates empty params") {
        PrimitiveParams params;
        REQUIRE(params.values.empty());
    }
    
    SECTION("Set and get integer values") {
        PrimitiveParams params;
        params.set("width", 100);
        params.set("height", 200);
        params.set("depth", -50);
        
        auto width = params.get<int>("width");
        auto height = params.get<int>("height");
        auto depth = params.get<int>("depth");
        
        REQUIRE(width.has_value());
        REQUIRE(width.value() == 100);
        REQUIRE(height.has_value());
        REQUIRE(height.value() == 200);
        REQUIRE(depth.has_value());
        REQUIRE(depth.value() == -50);
    }
    
    SECTION("Set and get unsigned integer values") {
        PrimitiveParams params;
        params.set("segments", 32u);
        params.set("rings", 16u);
        
        auto segments = params.get<unsigned int>("segments");
        auto rings = params.get<unsigned int>("rings");
        
        REQUIRE(segments.has_value());
        REQUIRE(segments.value() == 32u);
        REQUIRE(rings.has_value());
        REQUIRE(rings.value() == 16u);
    }
    
    SECTION("Set and get float values") {
        PrimitiveParams params;
        params.set("radius", 5.5f);
        params.set("scale", 2.0f);
        params.set("offset", -1.25f);
        
        auto radius = params.get<float>("radius");
        auto scale = params.get<float>("scale");
        auto offset = params.get<float>("offset");
        
        REQUIRE(radius.has_value());
        REQUIRE(radius.value() == 5.5f);
        REQUIRE(scale.has_value());
        REQUIRE(scale.value() == 2.0f);
        REQUIRE(offset.has_value());
        REQUIRE(offset.value() == -1.25f);
    }
    
    SECTION("Set and get double values") {
        PrimitiveParams params;
        params.set("precision", 3.14159265359);
        params.set("large_value", 1e10);
        
        auto precision = params.get<double>("precision");
        auto large_value = params.get<double>("large_value");
        
        REQUIRE(precision.has_value());
        REQUIRE(precision.value() == 3.14159265359);
        REQUIRE(large_value.has_value());
        REQUIRE(large_value.value() == 1e10);
    }
    
    SECTION("Set and get boolean values") {
        PrimitiveParams params;
        params.set("smooth", true);
        params.set("wireframe", false);
        params.set("visible", true);
        
        auto smooth = params.get<bool>("smooth");
        auto wireframe = params.get<bool>("wireframe");
        auto visible = params.get<bool>("visible");
        
        REQUIRE(smooth.has_value());
        REQUIRE(smooth.value() == true);
        REQUIRE(wireframe.has_value());
        REQUIRE(wireframe.value() == false);
        REQUIRE(visible.has_value());
        REQUIRE(visible.value() == true);
    }
    
    SECTION("Set and get string values") {
        PrimitiveParams params;
        params.set("name", std::string("test_primitive"));
        params.set("material", std::string("metal"));
        params.set("texture", std::string(""));
        
        auto name = params.get<std::string>("name");
        auto material = params.get<std::string>("material");
        auto texture = params.get<std::string>("texture");
        
        REQUIRE(name.has_value());
        REQUIRE(name.value() == "test_primitive");
        REQUIRE(material.has_value());
        REQUIRE(material.value() == "metal");
        REQUIRE(texture.has_value());
        REQUIRE(texture.value() == "");
    }
}

TEST_CASE("PrimitiveParams GLM vector support", "[assets][primitive_params][vectors]") {
    SECTION("Set and get vec2 values") {
        PrimitiveParams params;
        glm::vec2 size(10.0f, 20.0f);
        glm::vec2 offset(-5.0f, 3.5f);
        
        params.set("size", size);
        params.set("offset", offset);
        
        auto retrieved_size = params.get<glm::vec2>("size");
        auto retrieved_offset = params.get<glm::vec2>("offset");
        
        REQUIRE(retrieved_size.has_value());
        REQUIRE(retrieved_size.value().x == 10.0f);
        REQUIRE(retrieved_size.value().y == 20.0f);
        REQUIRE(retrieved_offset.has_value());
        REQUIRE(retrieved_offset.value().x == -5.0f);
        REQUIRE(retrieved_offset.value().y == 3.5f);
    }
    
    SECTION("Set and get vec3 values") {
        PrimitiveParams params;
        glm::vec3 position(1.0f, 2.0f, 3.0f);
        glm::vec3 rotation(0.0f, 90.0f, 180.0f);
        glm::vec3 scale(2.0f, 2.0f, 2.0f);
        
        params.set("position", position);
        params.set("rotation", rotation);
        params.set("scale", scale);
        
        auto retrieved_position = params.get<glm::vec3>("position");
        auto retrieved_rotation = params.get<glm::vec3>("rotation");
        auto retrieved_scale = params.get<glm::vec3>("scale");
        
        REQUIRE(retrieved_position.has_value());
        REQUIRE(retrieved_position.value().x == 1.0f);
        REQUIRE(retrieved_position.value().y == 2.0f);
        REQUIRE(retrieved_position.value().z == 3.0f);
        REQUIRE(retrieved_rotation.has_value());
        REQUIRE(retrieved_rotation.value().x == 0.0f);
        REQUIRE(retrieved_rotation.value().y == 90.0f);
        REQUIRE(retrieved_rotation.value().z == 180.0f);
        REQUIRE(retrieved_scale.has_value());
        REQUIRE(retrieved_scale.value().x == 2.0f);
        REQUIRE(retrieved_scale.value().y == 2.0f);
        REQUIRE(retrieved_scale.value().z == 2.0f);
    }
    
    SECTION("Set and get vec4 values") {
        PrimitiveParams params;
        glm::vec4 color(1.0f, 0.5f, 0.0f, 1.0f);
        glm::vec4 bounds(-10.0f, -10.0f, 10.0f, 10.0f);
        
        params.set("color", color);
        params.set("bounds", bounds);
        
        auto retrieved_color = params.get<glm::vec4>("color");
        auto retrieved_bounds = params.get<glm::vec4>("bounds");
        
        REQUIRE(retrieved_color.has_value());
        REQUIRE(retrieved_color.value().x == 1.0f);
        REQUIRE(retrieved_color.value().y == 0.5f);
        REQUIRE(retrieved_color.value().z == 0.0f);
        REQUIRE(retrieved_color.value().w == 1.0f);
        REQUIRE(retrieved_bounds.has_value());
        REQUIRE(retrieved_bounds.value().x == -10.0f);
        REQUIRE(retrieved_bounds.value().y == -10.0f);
        REQUIRE(retrieved_bounds.value().z == 10.0f);
        REQUIRE(retrieved_bounds.value().w == 10.0f);
    }
}

TEST_CASE("PrimitiveParams error handling", "[assets][primitive_params][errors]") {
    SECTION("Get non-existent key returns nullopt") {
        PrimitiveParams params;
        
        auto missing_int = params.get<int>("missing");
        auto missing_float = params.get<float>("missing");
        auto missing_string = params.get<std::string>("missing");
        auto missing_vec3 = params.get<glm::vec3>("missing");
        
        REQUIRE_FALSE(missing_int.has_value());
        REQUIRE_FALSE(missing_float.has_value());
        REQUIRE_FALSE(missing_string.has_value());
        REQUIRE_FALSE(missing_vec3.has_value());
    }
    
    SECTION("Get wrong type returns nullopt") {
        PrimitiveParams params;
        params.set("int_value", 42);
        params.set("float_value", 3.14f);
        params.set("string_value", std::string("hello"));
        params.set("vec3_value", glm::vec3(1.0f, 2.0f, 3.0f));
        
        // Try to get wrong types
        auto int_as_float = params.get<float>("int_value");
        auto float_as_int = params.get<int>("float_value");
        auto string_as_int = params.get<int>("string_value");
        auto vec3_as_float = params.get<float>("vec3_value");
        
        REQUIRE_FALSE(int_as_float.has_value());
        REQUIRE_FALSE(float_as_int.has_value());
        REQUIRE_FALSE(string_as_int.has_value());
        REQUIRE_FALSE(vec3_as_float.has_value());
    }
    
    SECTION("Overwrite existing values") {
        PrimitiveParams params;
        
        // Set initial value
        params.set("value", 100);
        auto initial = params.get<int>("value");
        REQUIRE(initial.has_value());
        REQUIRE(initial.value() == 100);
        
        // Overwrite with different value of same type
        params.set("value", 200);
        auto updated = params.get<int>("value");
        REQUIRE(updated.has_value());
        REQUIRE(updated.value() == 200);
        
        // Overwrite with different type
        params.set("value", 3.14f);
        auto as_int = params.get<int>("value");
        auto as_float = params.get<float>("value");
        REQUIRE_FALSE(as_int.has_value());
        REQUIRE(as_float.has_value());
        REQUIRE(as_float.value() == 3.14f);
    }
}

TEST_CASE("PrimitiveParams JSON deserialization", "[assets][primitive_params][json]") {
    SECTION("Parse JSON with integer values") {
        json j = {
            {"width", 100},
            {"height", 200},
            {"segments", 32}
        };

        auto params = PrimitiveParams::fromJSON(j);

        auto width = params.get<int>("width");
        auto height = params.get<int>("height");
        auto segments = params.get<int>("segments");

        REQUIRE(width.has_value());
        REQUIRE(width.value() == 100);
        REQUIRE(height.has_value());
        REQUIRE(height.value() == 200);
        REQUIRE(segments.has_value());
        REQUIRE(segments.value() == 32);
    }

    SECTION("Parse JSON with float values") {
        json j = {
            {"radius", 5.5},
            {"scale", 2.0},
            {"offset", -1.25}
        };

        auto params = PrimitiveParams::fromJSON(j);

        auto radius = params.get<float>("radius");
        auto scale = params.get<float>("scale");
        auto offset = params.get<float>("offset");

        REQUIRE(radius.has_value());
        REQUIRE(radius.value() == 5.5f);
        REQUIRE(scale.has_value());
        REQUIRE(scale.value() == 2.0f);
        REQUIRE(offset.has_value());
        REQUIRE(offset.value() == -1.25f);
    }

    SECTION("Parse JSON with boolean values") {
        json j = {
            {"smooth", true},
            {"wireframe", false},
            {"visible", true}
        };

        auto params = PrimitiveParams::fromJSON(j);

        auto smooth = params.get<bool>("smooth");
        auto wireframe = params.get<bool>("wireframe");
        auto visible = params.get<bool>("visible");

        REQUIRE(smooth.has_value());
        REQUIRE(smooth.value() == true);
        REQUIRE(wireframe.has_value());
        REQUIRE(wireframe.value() == false);
        REQUIRE(visible.has_value());
        REQUIRE(visible.value() == true);
    }

    SECTION("Parse JSON with string values") {
        json j = {
            {"name", "test_primitive"},
            {"material", "metal"},
            {"texture", "wood.jpg"}
        };

        auto params = PrimitiveParams::fromJSON(j);

        auto name = params.get<std::string>("name");
        auto material = params.get<std::string>("material");
        auto texture = params.get<std::string>("texture");

        REQUIRE(name.has_value());
        REQUIRE(name.value() == "test_primitive");
        REQUIRE(material.has_value());
        REQUIRE(material.value() == "metal");
        REQUIRE(texture.has_value());
        REQUIRE(texture.value() == "wood.jpg");
    }

    SECTION("Parse JSON with vec2 arrays") {
        json j = {
            {"size", {10.0, 20.0}},
            {"offset", {-5.0, 3.5}},
            {"uv_scale", {2.0, 1.0}}
        };

        auto params = PrimitiveParams::fromJSON(j);

        auto size = params.get<glm::vec2>("size");
        auto offset = params.get<glm::vec2>("offset");
        auto uv_scale = params.get<glm::vec2>("uv_scale");

        REQUIRE(size.has_value());
        REQUIRE(size.value().x == 10.0f);
        REQUIRE(size.value().y == 20.0f);
        REQUIRE(offset.has_value());
        REQUIRE(offset.value().x == -5.0f);
        REQUIRE(offset.value().y == 3.5f);
        REQUIRE(uv_scale.has_value());
        REQUIRE(uv_scale.value().x == 2.0f);
        REQUIRE(uv_scale.value().y == 1.0f);
    }

    SECTION("Parse JSON with vec3 arrays") {
        json j = {
            {"position", {1.0, 2.0, 3.0}},
            {"rotation", {0.0, 90.0, 180.0}},
            {"scale", {2.0, 2.0, 2.0}}
        };

        auto params = PrimitiveParams::fromJSON(j);

        auto position = params.get<glm::vec3>("position");
        auto rotation = params.get<glm::vec3>("rotation");
        auto scale = params.get<glm::vec3>("scale");

        REQUIRE(position.has_value());
        REQUIRE(position.value().x == 1.0f);
        REQUIRE(position.value().y == 2.0f);
        REQUIRE(position.value().z == 3.0f);
        REQUIRE(rotation.has_value());
        REQUIRE(rotation.value().x == 0.0f);
        REQUIRE(rotation.value().y == 90.0f);
        REQUIRE(rotation.value().z == 180.0f);
        REQUIRE(scale.has_value());
        REQUIRE(scale.value().x == 2.0f);
        REQUIRE(scale.value().y == 2.0f);
        REQUIRE(scale.value().z == 2.0f);
    }

    SECTION("Parse JSON with vec4 arrays") {
        json j = {
            {"color", {1.0, 0.5, 0.0, 1.0}},
            {"bounds", {-10.0, -10.0, 10.0, 10.0}}
        };

        auto params = PrimitiveParams::fromJSON(j);

        auto color = params.get<glm::vec4>("color");
        auto bounds = params.get<glm::vec4>("bounds");

        REQUIRE(color.has_value());
        REQUIRE(color.value().x == 1.0f);
        REQUIRE(color.value().y == 0.5f);
        REQUIRE(color.value().z == 0.0f);
        REQUIRE(color.value().w == 1.0f);
        REQUIRE(bounds.has_value());
        REQUIRE(bounds.value().x == -10.0f);
        REQUIRE(bounds.value().y == -10.0f);
        REQUIRE(bounds.value().z == 10.0f);
        REQUIRE(bounds.value().w == 10.0f);
    }

    SECTION("Parse JSON with mixed types") {
        json j = {
            {"width", 100},
            {"radius", 5.5},
            {"smooth", true},
            {"name", "mixed_primitive"},
            {"position", {1.0, 2.0, 3.0}},
            {"color", {1.0, 0.0, 0.0, 1.0}}
        };

        auto params = PrimitiveParams::fromJSON(j);

        auto width = params.get<int>("width");
        auto radius = params.get<float>("radius");
        auto smooth = params.get<bool>("smooth");
        auto name = params.get<std::string>("name");
        auto position = params.get<glm::vec3>("position");
        auto color = params.get<glm::vec4>("color");

        REQUIRE(width.has_value());
        REQUIRE(width.value() == 100);
        REQUIRE(radius.has_value());
        REQUIRE(radius.value() == 5.5f);
        REQUIRE(smooth.has_value());
        REQUIRE(smooth.value() == true);
        REQUIRE(name.has_value());
        REQUIRE(name.value() == "mixed_primitive");
        REQUIRE(position.has_value());
        REQUIRE(position.value().x == 1.0f);
        REQUIRE(position.value().y == 2.0f);
        REQUIRE(position.value().z == 3.0f);
        REQUIRE(color.has_value());
        REQUIRE(color.value().x == 1.0f);
        REQUIRE(color.value().y == 0.0f);
        REQUIRE(color.value().z == 0.0f);
        REQUIRE(color.value().w == 1.0f);
    }
}

TEST_CASE("PrimitiveParams JSON error handling", "[assets][primitive_params][json_errors]") {
    SECTION("Parse empty JSON") {
        json j = {};

        auto params = PrimitiveParams::fromJSON(j);

        REQUIRE(params.values.empty());
    }

    SECTION("Parse JSON with single element array is ignored") {
        json j = {
            {"single_value", {1.0}},
            {"valid_vec2", {1.0, 2.0}}
        };

        auto params = PrimitiveParams::fromJSON(j);

        // Single element array is silently ignored
        auto single = params.get<float>("single_value");
        REQUIRE_FALSE(single.has_value());

        // But valid vec2 is processed correctly
        auto vec2 = params.get<glm::vec2>("valid_vec2");
        REQUIRE(vec2.has_value());
        REQUIRE(vec2.value().x == 1.0f);
        REQUIRE(vec2.value().y == 2.0f);
    }

    SECTION("Parse JSON with five element array is ignored") {
        json j = {
            {"five_values", {1.0, 2.0, 3.0, 4.0, 5.0}},
            {"valid_vec3", {1.0, 2.0, 3.0}}
        };

        auto params = PrimitiveParams::fromJSON(j);

        // Five element array is silently ignored
        auto five = params.get<glm::vec3>("five_values");
        REQUIRE_FALSE(five.has_value());

        // But valid vec3 is processed correctly
        auto vec3 = params.get<glm::vec3>("valid_vec3");
        REQUIRE(vec3.has_value());
        REQUIRE(vec3.value().x == 1.0f);
        REQUIRE(vec3.value().y == 2.0f);
        REQUIRE(vec3.value().z == 3.0f);
    }

    SECTION("Parse JSON with empty array is ignored") {
        json j = {
            {"empty_array", json::array()},
            {"valid_vec4", {1.0, 2.0, 3.0, 4.0}}
        };

        auto params = PrimitiveParams::fromJSON(j);

        // Empty array is silently ignored
        auto empty = params.get<glm::vec2>("empty_array");
        REQUIRE_FALSE(empty.has_value());

        // But valid vec4 is processed correctly
        auto vec4 = params.get<glm::vec4>("valid_vec4");
        REQUIRE(vec4.has_value());
        REQUIRE(vec4.value().x == 1.0f);
        REQUIRE(vec4.value().y == 2.0f);
        REQUIRE(vec4.value().z == 3.0f);
        REQUIRE(vec4.value().w == 4.0f);
    }

    SECTION("Parse JSON with null values throws exception") {
        json j = {
            {"null_value", nullptr}
        };

        REQUIRE_THROWS_AS(PrimitiveParams::fromJSON(j), std::runtime_error);
    }

    SECTION("Parse JSON with object values throws exception") {
        json j = {
            {"object_value", {{"nested", "value"}}}
        };

        REQUIRE_THROWS_AS(PrimitiveParams::fromJSON(j), std::runtime_error);
    }

    SECTION("Parse JSON with mixed array types throws exception") {
        json j = {
            {"mixed_array", {1, "string", true}}
        };

        // This should throw because it tries to convert non-float values to float
        REQUIRE_THROWS_AS(PrimitiveParams::fromJSON(j), nlohmann::json::type_error);
    }
}

TEST_CASE("PrimitiveParams real-world scenarios", "[assets][primitive_params][real_world]") {
    SECTION("Sphere primitive parameters") {
        json sphere_json = {
            {"radius", 2.5},
            {"segments", 32},
            {"rings", 16},
            {"smooth", true},
            {"material", "metal"},
            {"color", {0.8, 0.2, 0.1, 1.0}}
        };

        auto params = PrimitiveParams::fromJSON(sphere_json);

        auto radius = params.get<float>("radius");
        auto segments = params.get<int>("segments");
        auto rings = params.get<int>("rings");
        auto smooth = params.get<bool>("smooth");
        auto material = params.get<std::string>("material");
        auto color = params.get<glm::vec4>("color");

        REQUIRE(radius.has_value());
        REQUIRE(radius.value() == 2.5f);
        REQUIRE(segments.has_value());
        REQUIRE(segments.value() == 32);
        REQUIRE(rings.has_value());
        REQUIRE(rings.value() == 16);
        REQUIRE(smooth.has_value());
        REQUIRE(smooth.value() == true);
        REQUIRE(material.has_value());
        REQUIRE(material.value() == "metal");
        REQUIRE(color.has_value());
        REQUIRE(color.value().x == 0.8f);
        REQUIRE(color.value().y == 0.2f);
        REQUIRE(color.value().z == 0.1f);
        REQUIRE(color.value().w == 1.0f);
    }

    SECTION("Cube primitive parameters") {
        PrimitiveParams params;

        // Set cube parameters programmatically
        params.set("size", glm::vec3(2.0f, 3.0f, 1.5f));
        params.set("subdivisions", 2);
        params.set("wireframe", false);
        params.set("texture", std::string("wood.jpg"));
        params.set("uv_scale", glm::vec2(2.0f, 1.0f));

        auto size = params.get<glm::vec3>("size");
        auto subdivisions = params.get<int>("subdivisions");
        auto wireframe = params.get<bool>("wireframe");
        auto texture = params.get<std::string>("texture");
        auto uv_scale = params.get<glm::vec2>("uv_scale");

        REQUIRE(size.has_value());
        REQUIRE(size.value().x == 2.0f);
        REQUIRE(size.value().y == 3.0f);
        REQUIRE(size.value().z == 1.5f);
        REQUIRE(subdivisions.has_value());
        REQUIRE(subdivisions.value() == 2);
        REQUIRE(wireframe.has_value());
        REQUIRE(wireframe.value() == false);
        REQUIRE(texture.has_value());
        REQUIRE(texture.value() == "wood.jpg");
        REQUIRE(uv_scale.has_value());
        REQUIRE(uv_scale.value().x == 2.0f);
        REQUIRE(uv_scale.value().y == 1.0f);
    }

    SECTION("Plane primitive parameters") {
        json plane_json = {
            {"width", 10.0},
            {"height", 5.0},
            {"width_segments", 10},
            {"height_segments", 5},
            {"center", {0.0, 0.0, 0.0}},
            {"normal", {0.0, 1.0, 0.0}},
            {"double_sided", true}
        };

        auto params = PrimitiveParams::fromJSON(plane_json);

        auto width = params.get<float>("width");
        auto height = params.get<float>("height");
        auto width_segments = params.get<int>("width_segments");
        auto height_segments = params.get<int>("height_segments");
        auto center = params.get<glm::vec3>("center");
        auto normal = params.get<glm::vec3>("normal");
        auto double_sided = params.get<bool>("double_sided");

        REQUIRE(width.has_value());
        REQUIRE(width.value() == 10.0f);
        REQUIRE(height.has_value());
        REQUIRE(height.value() == 5.0f);
        REQUIRE(width_segments.has_value());
        REQUIRE(width_segments.value() == 10);
        REQUIRE(height_segments.has_value());
        REQUIRE(height_segments.value() == 5);
        REQUIRE(center.has_value());
        REQUIRE(center.value().x == 0.0f);
        REQUIRE(center.value().y == 0.0f);
        REQUIRE(center.value().z == 0.0f);
        REQUIRE(normal.has_value());
        REQUIRE(normal.value().x == 0.0f);
        REQUIRE(normal.value().y == 1.0f);
        REQUIRE(normal.value().z == 0.0f);
        REQUIRE(double_sided.has_value());
        REQUIRE(double_sided.value() == true);
    }

    SECTION("Complex primitive with all parameter types") {
        json complex_json = {
            {"id", 12345},
            {"name", "complex_primitive"},
            {"scale", 1.5},
            {"visible", true},
            {"position", {1.0, 2.0, 3.0}},
            {"rotation", {0.0, 45.0, 90.0}},
            {"color", {1.0, 0.5, 0.2, 0.8}},
            {"uv_offset", {0.1, 0.2}},
            {"segments", 64},
            {"smooth_normals", true},
            {"material_id", 42},
            {"texture_path", "assets/textures/metal.jpg"}
        };

        auto params = PrimitiveParams::fromJSON(complex_json);

        // Verify all parameters are correctly parsed
        REQUIRE(params.get<int>("id").value() == 12345);
        REQUIRE(params.get<std::string>("name").value() == "complex_primitive");
        REQUIRE(params.get<float>("scale").value() == 1.5f);
        REQUIRE(params.get<bool>("visible").value() == true);

        auto position = params.get<glm::vec3>("position").value();
        REQUIRE(position.x == 1.0f);
        REQUIRE(position.y == 2.0f);
        REQUIRE(position.z == 3.0f);

        auto rotation = params.get<glm::vec3>("rotation").value();
        REQUIRE(rotation.x == 0.0f);
        REQUIRE(rotation.y == 45.0f);
        REQUIRE(rotation.z == 90.0f);

        auto color = params.get<glm::vec4>("color").value();
        REQUIRE(color.x == 1.0f);
        REQUIRE(color.y == 0.5f);
        REQUIRE(color.z == 0.2f);
        REQUIRE(color.w == 0.8f);

        auto uv_offset = params.get<glm::vec2>("uv_offset").value();
        REQUIRE(uv_offset.x == 0.1f);
        REQUIRE(uv_offset.y == 0.2f);

        REQUIRE(params.get<int>("segments").value() == 64);
        REQUIRE(params.get<bool>("smooth_normals").value() == true);
        REQUIRE(params.get<int>("material_id").value() == 42);
        REQUIRE(params.get<std::string>("texture_path").value() == "assets/textures/metal.jpg");
    }
}
