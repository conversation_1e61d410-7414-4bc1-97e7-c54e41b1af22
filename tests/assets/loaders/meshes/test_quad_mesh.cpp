#include <catch2/catch_test_macros.hpp>

// C++ standard library
#include <cmath>
#include <memory>

// Third-party libraries
#include <glm/glm.hpp>

// Local includes
#include "assets/loaders/meshes/quad_mesh.hpp"
#include "test_utils.hpp"

using namespace IronFrost;

TEST_CASE("QuadMesh basic functionality", "[assets][loaders][meshes][quad_mesh][basic]") {
    SECTION("Create quad mesh") {
        auto meshData = QuadMesh::createMeshData();
        
        REQUIRE(meshData != nullptr);
        REQUIRE_FALSE(meshData->vertices.empty());
        REQUIRE_FALSE(meshData->indices.empty());
        
        // Quad should have 4 vertices
        REQUIRE(meshData->vertices.size() == 4);
        
        // Quad should have 6 indices (2 triangles * 3 indices)
        REQUIRE(meshData->indices.size() == 6);
    }

    SECTION("Indices form two triangles") {
        auto meshData = QuadMesh::createMeshData();
        
        // Expected indices: 0, 1, 3, 1, 2, 3
        REQUIRE(meshData->indices[0] == 0);
        REQUIRE(meshData->indices[1] == 1);
        REQUIRE(meshData->indices[2] == 3);
        REQUIRE(meshData->indices[3] == 1);
        REQUIRE(meshData->indices[4] == 2);
        REQUIRE(meshData->indices[5] == 3);
    }
}

TEST_CASE("QuadMesh vertex properties", "[assets][loaders][meshes][quad_mesh][vertices]") {
    SECTION("Vertices form unit square") {
        auto meshData = QuadMesh::createMeshData();
        
        glm::vec3 v0 = meshData->vertices[0].getPosition(); // Top right
        glm::vec3 v1 = meshData->vertices[1].getPosition(); // Bottom right
        glm::vec3 v2 = meshData->vertices[2].getPosition(); // Bottom left
        glm::vec3 v3 = meshData->vertices[3].getPosition(); // Top left
        
        // Check expected positions
        REQUIRE(isVec3Equal(v0, glm::vec3(0.5f, 0.5f, 0.0f)));
        REQUIRE(isVec3Equal(v1, glm::vec3(0.5f, -0.5f, 0.0f)));
        REQUIRE(isVec3Equal(v2, glm::vec3(-0.5f, -0.5f, 0.0f)));
        REQUIRE(isVec3Equal(v3, glm::vec3(-0.5f, 0.5f, 0.0f)));
    }

    SECTION("All vertices are in XY plane") {
        auto meshData = QuadMesh::createMeshData();
        
        for (const auto& vertex : meshData->vertices) {
            glm::vec3 pos = vertex.getPosition();
            REQUIRE(isFloatEqual(pos.z, 0.0f));
        }
    }

    SECTION("UV coordinates form complete mapping") {
        auto meshData = QuadMesh::createMeshData();
        
        glm::vec2 uv0 = meshData->vertices[0].getUV(); // Top right
        glm::vec2 uv1 = meshData->vertices[1].getUV(); // Bottom right
        glm::vec2 uv2 = meshData->vertices[2].getUV(); // Bottom left
        glm::vec2 uv3 = meshData->vertices[3].getUV(); // Top left
        
        // Check expected UV coordinates
        REQUIRE(isVec2Equal(uv0, glm::vec2(1.0f, 1.0f))); // Top right
        REQUIRE(isVec2Equal(uv1, glm::vec2(1.0f, 0.0f))); // Bottom right
        REQUIRE(isVec2Equal(uv2, glm::vec2(0.0f, 0.0f))); // Bottom left
        REQUIRE(isVec2Equal(uv3, glm::vec2(0.0f, 1.0f))); // Top left
    }

    SECTION("UV coordinates are in valid range") {
        auto meshData = QuadMesh::createMeshData();
        
        for (const auto& vertex : meshData->vertices) {
            glm::vec2 uv = vertex.getUV();
            
            // UV coordinates should be in [0, 1] range
            REQUIRE(uv.x >= 0.0f);
            REQUIRE(uv.x <= 1.0f);
            REQUIRE(uv.y >= 0.0f);
            REQUIRE(uv.y <= 1.0f);
        }
    }

    SECTION("All normals point in same direction") {
        auto meshData = QuadMesh::createMeshData();
        
        glm::vec3 expectedNormal(0.0f, 0.0f, -1.0f);
        
        for (const auto& vertex : meshData->vertices) {
            glm::vec3 normal = vertex.getNormal();
            REQUIRE(isVec3Equal(normal, expectedNormal));
            REQUIRE(isVec3Normalized(normal));
        }
    }

    SECTION("Tangents and bitangents are calculated") {
        auto meshData = QuadMesh::createMeshData();
        
        for (const auto& vertex : meshData->vertices) {
            glm::vec3 tangent = vertex.getTangent();
            glm::vec3 bitangent = vertex.getBitangent();
            
            // Should not be zero vectors
            REQUIRE_FALSE(isVec3Equal(tangent, glm::vec3(0.0f)));
            REQUIRE_FALSE(isVec3Equal(bitangent, glm::vec3(0.0f)));
            
            // Should be normalized
            REQUIRE(isVec3Normalized(tangent));
            REQUIRE(isVec3Normalized(bitangent));
        }
    }
}

TEST_CASE("QuadMesh geometric properties", "[assets][loaders][meshes][quad_mesh][geometry]") {
    SECTION("Quad has correct area") {
        auto meshData = QuadMesh::createMeshData();
        
        // Calculate total area of both triangles
        float totalArea = 0.0f;
        
        for (size_t i = 0; i < meshData->indices.size(); i += 3) {
            unsigned int i0 = meshData->indices[i];
            unsigned int i1 = meshData->indices[i + 1];
            unsigned int i2 = meshData->indices[i + 2];
            
            glm::vec3 v0 = meshData->vertices[i0].getPosition();
            glm::vec3 v1 = meshData->vertices[i1].getPosition();
            glm::vec3 v2 = meshData->vertices[i2].getPosition();
            
            glm::vec3 edge1 = v1 - v0;
            glm::vec3 edge2 = v2 - v0;
            float triangleArea = 0.5f * glm::length(glm::cross(edge1, edge2));
            totalArea += triangleArea;
        }
        
        // Expected area: 1.0 * 1.0 = 1.0
        REQUIRE(isFloatEqual(totalArea, 1.0f, 0.01f));
    }

    SECTION("Quad is planar") {
        auto meshData = QuadMesh::createMeshData();
        
        glm::vec3 v0 = meshData->vertices[0].getPosition();
        glm::vec3 v1 = meshData->vertices[1].getPosition();
        glm::vec3 v2 = meshData->vertices[2].getPosition();
        glm::vec3 v3 = meshData->vertices[3].getPosition();
        
        // All vertices should be in the same plane (Z = 0)
        REQUIRE(isFloatEqual(v0.z, 0.0f));
        REQUIRE(isFloatEqual(v1.z, 0.0f));
        REQUIRE(isFloatEqual(v2.z, 0.0f));
        REQUIRE(isFloatEqual(v3.z, 0.0f));
    }

    SECTION("Quad sides are perpendicular") {
        auto meshData = QuadMesh::createMeshData();
        
        glm::vec3 v0 = meshData->vertices[0].getPosition(); // Top right
        glm::vec3 v1 = meshData->vertices[1].getPosition(); // Bottom right
        glm::vec3 v2 = meshData->vertices[2].getPosition(); // Bottom left
        glm::vec3 v3 = meshData->vertices[3].getPosition(); // Top left
        
        // Calculate edge vectors
        glm::vec3 rightEdge = v1 - v0;  // Top to bottom on right side
        glm::vec3 bottomEdge = v2 - v1; // Right to left on bottom
        glm::vec3 leftEdge = v3 - v2;   // Bottom to top on left side
        glm::vec3 topEdge = v0 - v3;    // Left to right on top
        
        // Adjacent edges should be perpendicular
        REQUIRE(areVec3Orthogonal(rightEdge, bottomEdge));
        REQUIRE(areVec3Orthogonal(bottomEdge, leftEdge));
        REQUIRE(areVec3Orthogonal(leftEdge, topEdge));
        REQUIRE(areVec3Orthogonal(topEdge, rightEdge));
    }

    SECTION("Quad diagonals bisect each other") {
        auto meshData = QuadMesh::createMeshData();
        
        glm::vec3 v0 = meshData->vertices[0].getPosition(); // Top right
        glm::vec3 v1 = meshData->vertices[1].getPosition(); // Bottom right
        glm::vec3 v2 = meshData->vertices[2].getPosition(); // Bottom left
        glm::vec3 v3 = meshData->vertices[3].getPosition(); // Top left
        
        // Calculate diagonal midpoints
        glm::vec3 diagonal1Mid = (v0 + v2) * 0.5f; // Top-right to bottom-left
        glm::vec3 diagonal2Mid = (v1 + v3) * 0.5f; // Bottom-right to top-left
        
        // Midpoints should be the same (center of quad)
        REQUIRE(isVec3Equal(diagonal1Mid, diagonal2Mid, 0.01f));
        
        // Should be at origin for this centered quad
        REQUIRE(isVec3Equal(diagonal1Mid, glm::vec3(0.0f, 0.0f, 0.0f), 0.01f));
    }
}

TEST_CASE("QuadMesh triangle properties", "[assets][loaders][meshes][quad_mesh][triangles]") {
    SECTION("Both triangles have positive area") {
        auto meshData = QuadMesh::createMeshData();
        
        // First triangle: indices 0, 1, 3
        unsigned int i0 = meshData->indices[0];
        unsigned int i1 = meshData->indices[1];
        unsigned int i2 = meshData->indices[2];
        
        glm::vec3 v0 = meshData->vertices[i0].getPosition();
        glm::vec3 v1 = meshData->vertices[i1].getPosition();
        glm::vec3 v2 = meshData->vertices[i2].getPosition();
        
        glm::vec3 edge1 = v1 - v0;
        glm::vec3 edge2 = v2 - v0;
        float area1 = 0.5f * glm::length(glm::cross(edge1, edge2));
        
        // Second triangle: indices 1, 2, 3
        i0 = meshData->indices[3];
        i1 = meshData->indices[4];
        i2 = meshData->indices[5];
        
        v0 = meshData->vertices[i0].getPosition();
        v1 = meshData->vertices[i1].getPosition();
        v2 = meshData->vertices[i2].getPosition();
        
        edge1 = v1 - v0;
        edge2 = v2 - v0;
        float area2 = 0.5f * glm::length(glm::cross(edge1, edge2));
        
        REQUIRE(area1 > 0.0f);
        REQUIRE(area2 > 0.0f);
        
        // Both triangles should have equal area (half of quad)
        REQUIRE(isFloatEqual(area1, area2, 0.01f));
        REQUIRE(isFloatEqual(area1, 0.5f, 0.01f));
    }

    SECTION("Triangle winding is counter-clockwise") {
        auto meshData = QuadMesh::createMeshData();
        
        for (size_t i = 0; i < meshData->indices.size(); i += 3) {
            unsigned int i0 = meshData->indices[i];
            unsigned int i1 = meshData->indices[i + 1];
            unsigned int i2 = meshData->indices[i + 2];
            
            glm::vec3 v0 = meshData->vertices[i0].getPosition();
            glm::vec3 v1 = meshData->vertices[i1].getPosition();
            glm::vec3 v2 = meshData->vertices[i2].getPosition();
            
            glm::vec3 edge1 = v1 - v0;
            glm::vec3 edge2 = v2 - v0;
            glm::vec3 triangleNormal = glm::normalize(glm::cross(edge1, edge2));
            
            // For CCW winding in XY plane, normal should point in -Z direction
            glm::vec3 expectedNormal(0.0f, 0.0f, -1.0f);
            REQUIRE(isVec3Equal(triangleNormal, expectedNormal, 0.01f));
        }
    }
}

TEST_CASE("QuadMesh real-world scenarios", "[assets][loaders][meshes][quad_mesh][real_world]") {
    SECTION("UI background quad") {
        auto meshData = QuadMesh::createMeshData();
        
        // Should be suitable for UI rendering
        REQUIRE(meshData->vertices.size() == 4);
        REQUIRE(meshData->indices.size() == 6);
        
        // All vertices should be in a reasonable range for UI
        for (const auto& vertex : meshData->vertices) {
            glm::vec3 pos = vertex.getPosition();
            REQUIRE((pos.x >= -1.0f && pos.x <= 1.0f));
            REQUIRE((pos.y >= -1.0f && pos.y <= 1.0f));
            REQUIRE(isFloatEqual(pos.z, 0.0f)); // Flat for UI
        }
        
        // UV should cover full texture space
        bool foundUV00 = false, foundUV10 = false, foundUV01 = false, foundUV11 = false;
        
        for (const auto& vertex : meshData->vertices) {
            glm::vec2 uv = vertex.getUV();
            
            if (isVec2Equal(uv, glm::vec2(0.0f, 0.0f))) foundUV00 = true;
            if (isVec2Equal(uv, glm::vec2(1.0f, 0.0f))) foundUV10 = true;
            if (isVec2Equal(uv, glm::vec2(0.0f, 1.0f))) foundUV01 = true;
            if (isVec2Equal(uv, glm::vec2(1.0f, 1.0f))) foundUV11 = true;
        }
        
        REQUIRE(foundUV00);
        REQUIRE(foundUV10);
        REQUIRE(foundUV01);
        REQUIRE(foundUV11);
    }

    SECTION("Sprite or billboard quad") {
        auto meshData = QuadMesh::createMeshData();
        
        // Should have proper tangent space for sprite effects
        for (const auto& vertex : meshData->vertices) {
            glm::vec3 normal = vertex.getNormal();
            glm::vec3 tangent = vertex.getTangent();
            glm::vec3 bitangent = vertex.getBitangent();
            
            // Should form orthogonal basis
            REQUIRE(areVec3Orthogonal(normal, tangent));
            REQUIRE(areVec3Orthogonal(normal, bitangent));
            REQUIRE(areVec3Orthogonal(tangent, bitangent));
            
            // All should be normalized
            REQUIRE(isVec3Normalized(normal));
            REQUIRE(isVec3Normalized(tangent));
            REQUIRE(isVec3Normalized(bitangent));
        }
    }

    SECTION("Particle system quad") {
        auto meshData = QuadMesh::createMeshData();
        
        // Check bounds for particle systems
        glm::vec3 minBounds(1.0f);
        glm::vec3 maxBounds(-1.0f);
        
        for (const auto& vertex : meshData->vertices) {
            glm::vec3 pos = vertex.getPosition();
            minBounds = glm::min(minBounds, pos);
            maxBounds = glm::max(maxBounds, pos);
        }
        
        REQUIRE(isVec3Equal(minBounds, glm::vec3(-0.5f, -0.5f, 0.0f)));
        REQUIRE(isVec3Equal(maxBounds, glm::vec3(0.5f, 0.5f, 0.0f)));
        
        // Should be centered at origin
        glm::vec3 center = (minBounds + maxBounds) * 0.5f;
        REQUIRE(isVec3Equal(center, glm::vec3(0.0f, 0.0f, 0.0f)));
    }

    SECTION("Screen-space quad") {
        auto meshData = QuadMesh::createMeshData();
        
        // Should be suitable for full-screen effects
        // Check that quad covers a unit square area
        float totalArea = 0.0f;
        
        for (size_t i = 0; i < meshData->indices.size(); i += 3) {
            unsigned int i0 = meshData->indices[i];
            unsigned int i1 = meshData->indices[i + 1];
            unsigned int i2 = meshData->indices[i + 2];
            
            glm::vec3 v0 = meshData->vertices[i0].getPosition();
            glm::vec3 v1 = meshData->vertices[i1].getPosition();
            glm::vec3 v2 = meshData->vertices[i2].getPosition();
            
            glm::vec3 edge1 = v1 - v0;
            glm::vec3 edge2 = v2 - v0;
            float triangleArea = 0.5f * glm::length(glm::cross(edge1, edge2));
            totalArea += triangleArea;
        }
        
        REQUIRE(isFloatEqual(totalArea, 1.0f, 0.01f));
    }

    SECTION("Texture mapping quad") {
        auto meshData = QuadMesh::createMeshData();
        
        // Check that UV area matches geometric area ratio
        float geometricArea = 1.0f; // 1x1 quad
        
        // Calculate UV area
        glm::vec2 uv0 = meshData->vertices[0].getUV();
        glm::vec2 uv1 = meshData->vertices[1].getUV();
        glm::vec2 uv2 = meshData->vertices[2].getUV();
        glm::vec2 uv3 = meshData->vertices[3].getUV();
        
        // UV area using shoelace formula
        float uvArea = 0.5f * std::abs(
            (uv0.x * uv1.y - uv1.x * uv0.y) +
            (uv1.x * uv2.y - uv2.x * uv1.y) +
            (uv2.x * uv3.y - uv3.x * uv2.y) +
            (uv3.x * uv0.y - uv0.x * uv3.y)
        );
        
        REQUIRE(isFloatEqual(uvArea, 1.0f, 0.01f)); // Should cover full UV space
    }
}
