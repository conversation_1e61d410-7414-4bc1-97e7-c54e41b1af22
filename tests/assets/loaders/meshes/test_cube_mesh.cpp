#include <catch2/catch_test_macros.hpp>

// C++ standard library
#include <cmath>
#include <memory>
#include <unordered_set>

// Third-party libraries
#include <glm/glm.hpp>

// Local includes
#include "assets/loaders/meshes/cube_mesh.hpp"
#include "test_utils.hpp"

using namespace IronFrost;

TEST_CASE("CubeMesh basic functionality", "[assets][loaders][meshes][cube_mesh][basic]") {
    SECTION("Create cube mesh") {
        auto meshData = CubeMesh::createMeshData();
        
        REQUIRE(meshData != nullptr);
        REQUIRE_FALSE(meshData->vertices.empty());
        REQUIRE_FALSE(meshData->indices.empty());
        
        // Cube should have 36 vertices (6 faces * 6 vertices per face)
        REQUIRE(meshData->vertices.size() == 36);
        
        // Cube should have 36 indices (6 faces * 2 triangles * 3 indices)
        REQUIRE(meshData->indices.size() == 36);
    }

    SECTION("Indices are sequential") {
        auto meshData = CubeMesh::createMeshData();
        
        // Indices should be 0, 1, 2, 3, ..., 35
        for (size_t i = 0; i < meshData->indices.size(); ++i) {
            REQUIRE(meshData->indices[i] == i);
        }
    }
}

TEST_CASE("CubeMesh vertex properties", "[assets][loaders][meshes][cube_mesh][vertices]") {
    SECTION("All vertices are within cube bounds") {
        auto meshData = CubeMesh::createMeshData();
        
        for (const auto& vertex : meshData->vertices) {
            glm::vec3 pos = vertex.getPosition();
            
            // All coordinates should be within [-0.5, 0.5]
            REQUIRE(pos.x >= -0.5f);
            REQUIRE(pos.x <= 0.5f);
            REQUIRE(pos.y >= -0.5f);
            REQUIRE(pos.y <= 0.5f);
            REQUIRE(pos.z >= -0.5f);
            REQUIRE(pos.z <= 0.5f);
        }
    }

    SECTION("Vertices are at cube corners and edges") {
        auto meshData = CubeMesh::createMeshData();
        
        // Each vertex coordinate should be exactly -0.5 or 0.5
        for (const auto& vertex : meshData->vertices) {
            glm::vec3 pos = vertex.getPosition();
            
            REQUIRE((isFloatEqual(pos.x, -0.5f) || isFloatEqual(pos.x, 0.5f)));
            REQUIRE((isFloatEqual(pos.y, -0.5f) || isFloatEqual(pos.y, 0.5f)));
            REQUIRE((isFloatEqual(pos.z, -0.5f) || isFloatEqual(pos.z, 0.5f)));
        }
    }

    SECTION("UV coordinates are in valid range") {
        auto meshData = CubeMesh::createMeshData();
        
        for (const auto& vertex : meshData->vertices) {
            glm::vec2 uv = vertex.getUV();
            
            // UV coordinates should be in [0, 1] range
            REQUIRE(uv.x >= 0.0f);
            REQUIRE(uv.x <= 1.0f);
            REQUIRE(uv.y >= 0.0f);
            REQUIRE(uv.y <= 1.0f);
        }
    }

    SECTION("Normals are unit vectors") {
        auto meshData = CubeMesh::createMeshData();
        
        for (const auto& vertex : meshData->vertices) {
            glm::vec3 normal = vertex.getNormal();
            REQUIRE(isVec3Normalized(normal));
        }
    }

    SECTION("Face normals are correct") {
        auto meshData = CubeMesh::createMeshData();
        
        // Check normals for each face (6 vertices per face)
        // Back face (vertices 0-5): normal should be (0, 0, -1)
        for (int i = 0; i < 6; ++i) {
            glm::vec3 normal = meshData->vertices[i].getNormal();
            REQUIRE(isVec3Equal(normal, glm::vec3(0.0f, 0.0f, -1.0f)));
        }
        
        // Front face (vertices 6-11): normal should be (0, 0, 1)
        for (int i = 6; i < 12; ++i) {
            glm::vec3 normal = meshData->vertices[i].getNormal();
            REQUIRE(isVec3Equal(normal, glm::vec3(0.0f, 0.0f, 1.0f)));
        }
        
        // Left face (vertices 12-17): normal should be (-1, 0, 0)
        for (int i = 12; i < 18; ++i) {
            glm::vec3 normal = meshData->vertices[i].getNormal();
            REQUIRE(isVec3Equal(normal, glm::vec3(-1.0f, 0.0f, 0.0f)));
        }
        
        // Right face (vertices 18-23): normal should be (1, 0, 0)
        for (int i = 18; i < 24; ++i) {
            glm::vec3 normal = meshData->vertices[i].getNormal();
            REQUIRE(isVec3Equal(normal, glm::vec3(1.0f, 0.0f, 0.0f)));
        }
        
        // Bottom face (vertices 24-29): normal should be (0, -1, 0)
        for (int i = 24; i < 30; ++i) {
            glm::vec3 normal = meshData->vertices[i].getNormal();
            REQUIRE(isVec3Equal(normal, glm::vec3(0.0f, -1.0f, 0.0f)));
        }
        
        // Top face (vertices 30-35): normal should be (0, 1, 0)
        for (int i = 30; i < 36; ++i) {
            glm::vec3 normal = meshData->vertices[i].getNormal();
            REQUIRE(isVec3Equal(normal, glm::vec3(0.0f, 1.0f, 0.0f)));
        }
    }

    SECTION("Tangents and bitangents are calculated") {
        auto meshData = CubeMesh::createMeshData();
        
        for (const auto& vertex : meshData->vertices) {
            glm::vec3 tangent = vertex.getTangent();
            glm::vec3 bitangent = vertex.getBitangent();
            
            // Should not be zero vectors
            REQUIRE_FALSE(isVec3Equal(tangent, glm::vec3(0.0f)));
            REQUIRE_FALSE(isVec3Equal(bitangent, glm::vec3(0.0f)));
            
            // Should be normalized
            REQUIRE(isVec3Normalized(tangent));
            REQUIRE(isVec3Normalized(bitangent));
        }
    }
}

TEST_CASE("CubeMesh geometric properties", "[assets][loaders][meshes][cube_mesh][geometry]") {
    SECTION("Cube has correct volume") {
        auto meshData = CubeMesh::createMeshData();
        
        // Calculate volume using triangle areas (approximate)
        float totalVolume = 0.0f;
        
        for (size_t i = 0; i < meshData->indices.size(); i += 3) {
            unsigned int i0 = meshData->indices[i];
            unsigned int i1 = meshData->indices[i + 1];
            unsigned int i2 = meshData->indices[i + 2];
            
            glm::vec3 v0 = meshData->vertices[i0].getPosition();
            glm::vec3 v1 = meshData->vertices[i1].getPosition();
            glm::vec3 v2 = meshData->vertices[i2].getPosition();
            
            // Calculate triangle contribution to volume (using divergence theorem)
            glm::vec3 cross = glm::cross(v1, v2);
            totalVolume += glm::dot(v0, cross) / 6.0f;
        }
        
        // Expected volume of unit cube: 1.0
        REQUIRE(isFloatEqual(std::abs(totalVolume), 1.0f, 0.01f));
    }

    SECTION("All triangles have positive area") {
        auto meshData = CubeMesh::createMeshData();
        
        for (size_t i = 0; i < meshData->indices.size(); i += 3) {
            unsigned int i0 = meshData->indices[i];
            unsigned int i1 = meshData->indices[i + 1];
            unsigned int i2 = meshData->indices[i + 2];
            
            glm::vec3 v0 = meshData->vertices[i0].getPosition();
            glm::vec3 v1 = meshData->vertices[i1].getPosition();
            glm::vec3 v2 = meshData->vertices[i2].getPosition();
            
            glm::vec3 edge1 = v1 - v0;
            glm::vec3 edge2 = v2 - v0;
            float area = 0.5f * glm::length(glm::cross(edge1, edge2));
            
            REQUIRE(area > 0.001f); // Should have positive area
        }
    }

    SECTION("Face areas are correct") {
        auto meshData = CubeMesh::createMeshData();
        
        // Each face should have area 1.0 (two triangles of 0.5 each)
        for (int face = 0; face < 6; ++face) {
            float faceArea = 0.0f;
            
            // Each face has 2 triangles (6 vertices, indices in groups of 3)
            for (int tri = 0; tri < 2; ++tri) {
                int baseIndex = face * 6 + tri * 3;
                
                unsigned int i0 = meshData->indices[baseIndex];
                unsigned int i1 = meshData->indices[baseIndex + 1];
                unsigned int i2 = meshData->indices[baseIndex + 2];
                
                glm::vec3 v0 = meshData->vertices[i0].getPosition();
                glm::vec3 v1 = meshData->vertices[i1].getPosition();
                glm::vec3 v2 = meshData->vertices[i2].getPosition();
                
                glm::vec3 edge1 = v1 - v0;
                glm::vec3 edge2 = v2 - v0;
                float triangleArea = 0.5f * glm::length(glm::cross(edge1, edge2));
                faceArea += triangleArea;
            }
            
            REQUIRE(isFloatEqual(faceArea, 1.0f, 0.01f));
        }
    }
}

TEST_CASE("CubeMesh triangle winding", "[assets][loaders][meshes][cube_mesh][winding]") {
    SECTION("All triangles have counter-clockwise winding") {
        auto meshData = CubeMesh::createMeshData();
        
        for (size_t i = 0; i < meshData->indices.size(); i += 3) {
            unsigned int i0 = meshData->indices[i];
            unsigned int i1 = meshData->indices[i + 1];
            unsigned int i2 = meshData->indices[i + 2];
            
            glm::vec3 v0 = meshData->vertices[i0].getPosition();
            glm::vec3 v1 = meshData->vertices[i1].getPosition();
            glm::vec3 v2 = meshData->vertices[i2].getPosition();
            glm::vec3 normal = meshData->vertices[i0].getNormal(); // Face normal
            
            // Calculate triangle normal
            glm::vec3 edge1 = v1 - v0;
            glm::vec3 edge2 = v2 - v0;
            glm::vec3 triangleNormal = glm::normalize(glm::cross(edge1, edge2));
            
            // Triangle normal should align with face normal (CCW winding)
            float dot = glm::dot(triangleNormal, normal);
            REQUIRE(dot > 0.5f); // Should be pointing in same direction
        }
    }
}

TEST_CASE("CubeMesh real-world scenarios", "[assets][loaders][meshes][cube_mesh][real_world]") {
    SECTION("Game object cube") {
        auto meshData = CubeMesh::createMeshData();
        
        // Should be suitable for rendering as a game object
        REQUIRE(meshData->vertices.size() == 36);
        REQUIRE(meshData->indices.size() == 36);

        // All vertices should have valid data for rendering
        for (const auto& vertex : meshData->vertices) {
            glm::vec3 pos = vertex.getPosition();
            glm::vec2 uv = vertex.getUV();
            glm::vec3 normal = vertex.getNormal();
            glm::vec3 tangent = vertex.getTangent();
            glm::vec3 bitangent = vertex.getBitangent();

            // Position within bounds
            REQUIRE(glm::length(pos) <= 1.0f);

            // UV in valid range
            REQUIRE((uv.x >= 0.0f && uv.x <= 1.0f));
            REQUIRE((uv.y >= 0.0f && uv.y <= 1.0f));

            // Normalized vectors
            REQUIRE(isVec3Normalized(normal));
            REQUIRE(isVec3Normalized(tangent));
            REQUIRE(isVec3Normalized(bitangent));
        }
    }

    SECTION("Collision detection cube") {
        auto meshData = CubeMesh::createMeshData();
        
        // Check that cube bounds are correct for collision detection
        glm::vec3 minBounds(1.0f);
        glm::vec3 maxBounds(-1.0f);
        
        for (const auto& vertex : meshData->vertices) {
            glm::vec3 pos = vertex.getPosition();
            minBounds = glm::min(minBounds, pos);
            maxBounds = glm::max(maxBounds, pos);
        }
        
        REQUIRE(isVec3Equal(minBounds, glm::vec3(-0.5f)));
        REQUIRE(isVec3Equal(maxBounds, glm::vec3(0.5f)));
    }

    SECTION("Texture mapping cube") {
        auto meshData = CubeMesh::createMeshData();
        
        // Check that each face has proper UV mapping
        std::unordered_set<std::string> uvCorners;
        
        for (const auto& vertex : meshData->vertices) {
            glm::vec2 uv = vertex.getUV();
            
            // Check for UV corners (should have all combinations)
            if (isFloatEqual(uv.x, 0.0f) && isFloatEqual(uv.y, 0.0f)) uvCorners.insert("00");
            if (isFloatEqual(uv.x, 1.0f) && isFloatEqual(uv.y, 0.0f)) uvCorners.insert("10");
            if (isFloatEqual(uv.x, 0.0f) && isFloatEqual(uv.y, 1.0f)) uvCorners.insert("01");
            if (isFloatEqual(uv.x, 1.0f) && isFloatEqual(uv.y, 1.0f)) uvCorners.insert("11");
        }
        
        // Should have all four UV corners represented
        REQUIRE(uvCorners.size() == 4);
        REQUIRE(uvCorners.count("00") == 1);
        REQUIRE(uvCorners.count("10") == 1);
        REQUIRE(uvCorners.count("01") == 1);
        REQUIRE(uvCorners.count("11") == 1);
    }
}
