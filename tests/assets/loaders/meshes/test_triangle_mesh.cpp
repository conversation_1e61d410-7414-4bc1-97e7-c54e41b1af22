#include <catch2/catch_test_macros.hpp>

// C++ standard library
#include <cmath>
#include <memory>

// Third-party libraries
#include <glm/glm.hpp>

// Local includes
#include "assets/loaders/meshes/triangle_mesh.hpp"
#include "test_utils.hpp"

using namespace IronFrost;

TEST_CASE("TriangleMesh basic functionality", "[assets][loaders][meshes][triangle_mesh][basic]") {
    SECTION("Create triangle mesh") {
        auto meshData = TriangleMesh::createMeshData();
        
        REQUIRE(meshData != nullptr);
        REQUIRE_FALSE(meshData->vertices.empty());
        REQUIRE_FALSE(meshData->indices.empty());
        
        // Triangle should have 3 vertices
        REQUIRE(meshData->vertices.size() == 3);
        
        // Triangle should have 3 indices
        REQUIRE(meshData->indices.size() == 3);
    }

    SECTION("Indices are correct") {
        auto meshData = TriangleMesh::createMeshData();
        
        // Indices should be 0, 1, 2
        REQUIRE(meshData->indices[0] == 0);
        REQUIRE(meshData->indices[1] == 1);
        REQUIRE(meshData->indices[2] == 2);
    }
}

TEST_CASE("TriangleMesh vertex properties", "[assets][loaders][meshes][triangle_mesh][vertices]") {
    SECTION("Vertices form equilateral-like triangle") {
        auto meshData = TriangleMesh::createMeshData();
        
        glm::vec3 v0 = meshData->vertices[0].getPosition(); // Top vertex
        glm::vec3 v1 = meshData->vertices[1].getPosition(); // Bottom right
        glm::vec3 v2 = meshData->vertices[2].getPosition(); // Bottom left
        
        // Check expected positions
        REQUIRE(isVec3Equal(v0, glm::vec3(0.0f, 0.5f, 0.0f)));
        REQUIRE(isVec3Equal(v1, glm::vec3(0.5f, -0.5f, 0.0f)));
        REQUIRE(isVec3Equal(v2, glm::vec3(-0.5f, -0.5f, 0.0f)));
    }

    SECTION("All vertices are in XY plane") {
        auto meshData = TriangleMesh::createMeshData();
        
        for (const auto& vertex : meshData->vertices) {
            glm::vec3 pos = vertex.getPosition();
            REQUIRE(isFloatEqual(pos.z, 0.0f));
        }
    }

    SECTION("UV coordinates are valid") {
        auto meshData = TriangleMesh::createMeshData();
        
        for (const auto& vertex : meshData->vertices) {
            glm::vec2 uv = vertex.getUV();
            
            // UV coordinates should be in [0, 1] range
            REQUIRE(uv.x >= 0.0f);
            REQUIRE(uv.x <= 1.0f);
            REQUIRE(uv.y >= 0.0f);
            REQUIRE(uv.y <= 1.0f);
        }
    }

    SECTION("UV coordinates form triangle mapping") {
        auto meshData = TriangleMesh::createMeshData();
        
        glm::vec2 uv0 = meshData->vertices[0].getUV(); // Top vertex
        glm::vec2 uv1 = meshData->vertices[1].getUV(); // Bottom right
        glm::vec2 uv2 = meshData->vertices[2].getUV(); // Bottom left
        
        // Check expected UV coordinates
        REQUIRE(isVec2Equal(uv0, glm::vec2(0.5f, 1.0f))); // Top center
        REQUIRE(isVec2Equal(uv1, glm::vec2(1.0f, 0.0f))); // Bottom right
        REQUIRE(isVec2Equal(uv2, glm::vec2(0.0f, 0.0f))); // Bottom left
    }

    SECTION("All normals point in same direction") {
        auto meshData = TriangleMesh::createMeshData();
        
        glm::vec3 expectedNormal(0.0f, 0.0f, -1.0f);
        
        for (const auto& vertex : meshData->vertices) {
            glm::vec3 normal = vertex.getNormal();
            REQUIRE(isVec3Equal(normal, expectedNormal));
            REQUIRE(isVec3Normalized(normal));
        }
    }

    SECTION("Tangents and bitangents are calculated") {
        auto meshData = TriangleMesh::createMeshData();
        
        for (const auto& vertex : meshData->vertices) {
            glm::vec3 tangent = vertex.getTangent();
            glm::vec3 bitangent = vertex.getBitangent();
            
            // Should not be zero vectors
            REQUIRE_FALSE(isVec3Equal(tangent, glm::vec3(0.0f)));
            REQUIRE_FALSE(isVec3Equal(bitangent, glm::vec3(0.0f)));
            
            // Should be normalized
            REQUIRE(isVec3Normalized(tangent));
            REQUIRE(isVec3Normalized(bitangent));
        }
    }
}

TEST_CASE("TriangleMesh geometric properties", "[assets][loaders][meshes][triangle_mesh][geometry]") {
    SECTION("Triangle has positive area") {
        auto meshData = TriangleMesh::createMeshData();
        
        glm::vec3 v0 = meshData->vertices[0].getPosition();
        glm::vec3 v1 = meshData->vertices[1].getPosition();
        glm::vec3 v2 = meshData->vertices[2].getPosition();
        
        glm::vec3 edge1 = v1 - v0;
        glm::vec3 edge2 = v2 - v0;
        float area = 0.5f * glm::length(glm::cross(edge1, edge2));
        
        REQUIRE(area > 0.0f);
        
        // Expected area: 0.5 * base * height = 0.5 * 1.0 * 1.0 = 0.5
        REQUIRE(isFloatEqual(area, 0.5f, 0.01f));
    }

    SECTION("Triangle centroid is correct") {
        auto meshData = TriangleMesh::createMeshData();
        
        glm::vec3 v0 = meshData->vertices[0].getPosition();
        glm::vec3 v1 = meshData->vertices[1].getPosition();
        glm::vec3 v2 = meshData->vertices[2].getPosition();
        
        glm::vec3 centroid = (v0 + v1 + v2) / 3.0f;
        
        // Expected centroid: (0, 0, 0) for this symmetric triangle
        REQUIRE(isVec3Equal(centroid, glm::vec3(0.0f, -1.0f/6.0f, 0.0f), 0.01f));
    }

    SECTION("Triangle winding is counter-clockwise") {
        auto meshData = TriangleMesh::createMeshData();
        
        unsigned int i0 = meshData->indices[0];
        unsigned int i1 = meshData->indices[1];
        unsigned int i2 = meshData->indices[2];
        
        glm::vec3 v0 = meshData->vertices[i0].getPosition();
        glm::vec3 v1 = meshData->vertices[i1].getPosition();
        glm::vec3 v2 = meshData->vertices[i2].getPosition();
        
        glm::vec3 edge1 = v1 - v0;
        glm::vec3 edge2 = v2 - v0;
        glm::vec3 triangleNormal = glm::normalize(glm::cross(edge1, edge2));
        
        // For CCW winding in XY plane, normal should point in -Z direction
        glm::vec3 expectedNormal(0.0f, 0.0f, -1.0f);
        REQUIRE(isVec3Equal(triangleNormal, expectedNormal, 0.01f));
    }

    SECTION("Edge lengths are correct") {
        auto meshData = TriangleMesh::createMeshData();
        
        glm::vec3 v0 = meshData->vertices[0].getPosition(); // Top
        glm::vec3 v1 = meshData->vertices[1].getPosition(); // Bottom right
        glm::vec3 v2 = meshData->vertices[2].getPosition(); // Bottom left
        
        float edge01 = glm::length(v1 - v0); // Top to bottom right
        float edge02 = glm::length(v2 - v0); // Top to bottom left
        float edge12 = glm::length(v2 - v1); // Bottom left to bottom right
        
        // Check edge lengths
        REQUIRE(isFloatEqual(edge01, std::sqrt(1.25f), 0.01f)); // sqrt((0.5)^2 + (1.0)^2)
        REQUIRE(isFloatEqual(edge02, std::sqrt(1.25f), 0.01f)); // sqrt((0.5)^2 + (1.0)^2)
        REQUIRE(isFloatEqual(edge12, 1.0f, 0.01f)); // Base edge
    }
}

TEST_CASE("TriangleMesh mathematical properties", "[assets][loaders][meshes][triangle_mesh][mathematics]") {
    SECTION("Barycentric coordinates work correctly") {
        auto meshData = TriangleMesh::createMeshData();
        
        glm::vec3 v0 = meshData->vertices[0].getPosition();
        glm::vec3 v1 = meshData->vertices[1].getPosition();
        glm::vec3 v2 = meshData->vertices[2].getPosition();
        
        // Test centroid (should have barycentric coords (1/3, 1/3, 1/3))
        glm::vec3 centroid = (v0 + v1 + v2) / 3.0f;
        
        // Calculate barycentric coordinates for centroid
        glm::vec3 v0v1 = v1 - v0;
        glm::vec3 v0v2 = v2 - v0;
        glm::vec3 v0p = centroid - v0;
        
        float dot00 = glm::dot(v0v2, v0v2);
        float dot01 = glm::dot(v0v2, v0v1);
        float dot02 = glm::dot(v0v2, v0p);
        float dot11 = glm::dot(v0v1, v0v1);
        float dot12 = glm::dot(v0v1, v0p);
        
        float invDenom = 1.0f / (dot00 * dot11 - dot01 * dot01);
        float u = (dot11 * dot02 - dot01 * dot12) * invDenom;
        float v = (dot00 * dot12 - dot01 * dot02) * invDenom;
        float w = 1.0f - u - v;
        
        // Should be approximately (1/3, 1/3, 1/3)
        REQUIRE(isFloatEqual(u, 1.0f/3.0f, 0.01f));
        REQUIRE(isFloatEqual(v, 1.0f/3.0f, 0.01f));
        REQUIRE(isFloatEqual(w, 1.0f/3.0f, 0.01f));
    }

    SECTION("Triangle normal calculation is consistent") {
        auto meshData = TriangleMesh::createMeshData();
        
        glm::vec3 v0 = meshData->vertices[0].getPosition();
        glm::vec3 v1 = meshData->vertices[1].getPosition();
        glm::vec3 v2 = meshData->vertices[2].getPosition();
        
        // Calculate normal using cross product
        glm::vec3 edge1 = v1 - v0;
        glm::vec3 edge2 = v2 - v0;
        glm::vec3 calculatedNormal = glm::normalize(glm::cross(edge1, edge2));
        
        // Should match vertex normals
        glm::vec3 vertexNormal = meshData->vertices[0].getNormal();
        REQUIRE(isVec3Equal(calculatedNormal, vertexNormal, 0.01f));
    }
}

TEST_CASE("TriangleMesh real-world scenarios", "[assets][loaders][meshes][triangle_mesh][real_world]") {
    SECTION("UI triangle element") {
        auto meshData = TriangleMesh::createMeshData();
        
        // Should be suitable for UI rendering
        REQUIRE(meshData->vertices.size() == 3);
        REQUIRE(meshData->indices.size() == 3);
        
        // All vertices should be in a reasonable range for UI
        for (const auto& vertex : meshData->vertices) {
            glm::vec3 pos = vertex.getPosition();
            REQUIRE((pos.x >= -1.0f && pos.x <= 1.0f));
            REQUIRE((pos.y >= -1.0f && pos.y <= 1.0f));
            REQUIRE(isFloatEqual(pos.z, 0.0f)); // Flat for UI
        }
    }

    SECTION("Arrow or pointer mesh") {
        auto meshData = TriangleMesh::createMeshData();
        
        // Check that triangle points upward (suitable for arrow)
        glm::vec3 topVertex = meshData->vertices[0].getPosition();
        glm::vec3 bottomLeft = meshData->vertices[2].getPosition();
        glm::vec3 bottomRight = meshData->vertices[1].getPosition();
        
        REQUIRE(topVertex.y > bottomLeft.y);
        REQUIRE(topVertex.y > bottomRight.y);
        REQUIRE(isFloatEqual(bottomLeft.y, bottomRight.y)); // Base is horizontal
    }

    SECTION("Simple collision triangle") {
        auto meshData = TriangleMesh::createMeshData();
        
        // Check bounds for collision detection
        glm::vec3 minBounds(1.0f);
        glm::vec3 maxBounds(-1.0f);
        
        for (const auto& vertex : meshData->vertices) {
            glm::vec3 pos = vertex.getPosition();
            minBounds = glm::min(minBounds, pos);
            maxBounds = glm::max(maxBounds, pos);
        }
        
        REQUIRE(isVec3Equal(minBounds, glm::vec3(-0.5f, -0.5f, 0.0f)));
        REQUIRE(isVec3Equal(maxBounds, glm::vec3(0.5f, 0.5f, 0.0f)));
    }

    SECTION("Texture mapping triangle") {
        auto meshData = TriangleMesh::createMeshData();
        
        // Check that UV coordinates cover a reasonable area
        float uvArea = 0.0f;
        
        glm::vec2 uv0 = meshData->vertices[0].getUV();
        glm::vec2 uv1 = meshData->vertices[1].getUV();
        glm::vec2 uv2 = meshData->vertices[2].getUV();
        
        // Calculate UV triangle area
        glm::vec2 edge1 = uv1 - uv0;
        glm::vec2 edge2 = uv2 - uv0;
        uvArea = 0.5f * std::abs(edge1.x * edge2.y - edge1.y * edge2.x);
        
        REQUIRE(uvArea > 0.0f);
        REQUIRE(uvArea <= 0.5f); // Should not exceed half the UV space
    }

    SECTION("Particle or billboard triangle") {
        auto meshData = TriangleMesh::createMeshData();
        
        // Should have proper tangent space for billboard effects
        for (const auto& vertex : meshData->vertices) {
            glm::vec3 normal = vertex.getNormal();
            glm::vec3 tangent = vertex.getTangent();
            glm::vec3 bitangent = vertex.getBitangent();
            
            // Should form orthogonal basis
            REQUIRE(areVec3Orthogonal(normal, tangent));
            REQUIRE(areVec3Orthogonal(normal, bitangent));
            REQUIRE(areVec3Orthogonal(tangent, bitangent));
            
            // All should be normalized
            REQUIRE(isVec3Normalized(normal));
            REQUIRE(isVec3Normalized(tangent));
            REQUIRE(isVec3Normalized(bitangent));
        }
    }
}
