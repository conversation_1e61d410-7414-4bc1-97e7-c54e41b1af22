#include <catch2/catch_test_macros.hpp>

// C++ standard library
#include <cmath>
#include <memory>

// Third-party libraries
#include <glm/glm.hpp>
#include <glm/gtc/constants.hpp>

// Local includes
#include "assets/loaders/meshes/sphere_mesh.hpp"
#include "test_utils.hpp"

using namespace IronFrost;

TEST_CASE("SphereMesh basic functionality", "[assets][loaders][meshes][sphere_mesh][basic]") {
    SECTION("Create mesh with minimum parameters") {
        auto meshData = SphereMesh::createMeshData(3, 4);
        
        REQUIRE(meshData != nullptr);
        REQUIRE_FALSE(meshData->vertices.empty());
        REQUIRE_FALSE(meshData->indices.empty());
        
        // Expected vertices: (latitudeBands + 1) * (longitudeBands + 1)
        size_t expectedVertices = (3 + 1) * (4 + 1);
        REQUIRE(meshData->vertices.size() == expectedVertices);
        
        // Expected indices: latitudeBands * longitudeBands * 6 (2 triangles per quad)
        size_t expectedIndices = 3 * 4 * 6;
        REQUIRE(meshData->indices.size() == expectedIndices);
    }

    SECTION("Create mesh with standard parameters") {
        auto meshData = SphereMesh::createMeshData(16, 32);
        
        REQUIRE(meshData != nullptr);
        
        // Expected vertices: (16 + 1) * (32 + 1) = 17 * 33 = 561
        REQUIRE(meshData->vertices.size() == 561);
        
        // Expected indices: 16 * 32 * 6 = 3072
        REQUIRE(meshData->indices.size() == 3072);
    }

    SECTION("Create mesh with different latitude/longitude ratios") {
        auto meshData = SphereMesh::createMeshData(8, 16);
        
        REQUIRE(meshData != nullptr);
        
        // Expected vertices: (8 + 1) * (16 + 1) = 9 * 17 = 153
        REQUIRE(meshData->vertices.size() == 153);
        
        // Expected indices: 8 * 16 * 6 = 768
        REQUIRE(meshData->indices.size() == 768);
    }
}

TEST_CASE("SphereMesh vertex properties", "[assets][loaders][meshes][sphere_mesh][vertices]") {
    SECTION("Vertices are on sphere surface") {
        auto meshData = SphereMesh::createMeshData(8, 12);
        
        // All vertices should be at distance 0.5 from origin (radius = 0.5)
        const float expectedRadius = 0.5f;
        
        for (const auto& vertex : meshData->vertices) {
            glm::vec3 pos = vertex.getPosition();
            float distance = glm::length(pos);
            REQUIRE(isFloatEqual(distance, expectedRadius, 0.001f));
        }
    }

    SECTION("Normals point outward from center") {
        auto meshData = SphereMesh::createMeshData(6, 8);
        
        for (const auto& vertex : meshData->vertices) {
            glm::vec3 pos = vertex.getPosition();
            glm::vec3 normal = vertex.getNormal();
            
            // Normal should be normalized
            REQUIRE(isVec3Normalized(normal));
            
            // For a sphere centered at origin, normal should point in same direction as position
            glm::vec3 expectedNormal = glm::normalize(pos);
            REQUIRE(isVec3Equal(normal, expectedNormal, 0.001f));
        }
    }

    SECTION("UV coordinates are in valid range") {
        auto meshData = SphereMesh::createMeshData(10, 16);
        
        for (const auto& vertex : meshData->vertices) {
            glm::vec2 uv = vertex.getUV();
            
            // UV coordinates should be in [0, 1] range
            REQUIRE(uv.x >= 0.0f);
            REQUIRE(uv.x <= 1.0f);
            REQUIRE(uv.y >= 0.0f);
            REQUIRE(uv.y <= 1.0f);
        }
    }

    SECTION("Pole vertices have correct positions") {
        auto meshData = SphereMesh::createMeshData(4, 6);
        
        // Top pole (lat = 0): should be at (0, 0.5, 0)
        glm::vec3 topPole = meshData->vertices[0].getPosition();
        REQUIRE(isVec3Equal(topPole, glm::vec3(0.0f, 0.5f, 0.0f), 0.001f));
        
        // Bottom pole (lat = latitudeBands): should be at (0, -0.5, 0)
        size_t bottomPoleIndex = 4 * (6 + 1); // lat * (longitudeBands + 1)
        glm::vec3 bottomPole = meshData->vertices[bottomPoleIndex].getPosition();
        REQUIRE(isVec3Equal(bottomPole, glm::vec3(0.0f, -0.5f, 0.0f), 0.001f));
    }

    SECTION("Equator vertices have Y = 0") {
        auto meshData = SphereMesh::createMeshData(4, 8);
        
        // Equator is at lat = latitudeBands / 2 = 2
        int equatorLat = 2;
        
        for (int lon = 0; lon <= 8; ++lon) {
            size_t vertexIndex = equatorLat * (8 + 1) + lon;
            glm::vec3 pos = meshData->vertices[vertexIndex].getPosition();
            REQUIRE(isFloatEqual(pos.y, 0.0f, 0.001f));
        }
    }

    SECTION("Tangents and bitangents are calculated") {
        auto meshData = SphereMesh::createMeshData(6, 8);
        
        // After calculateTangentsAndBitangents(), tangents and bitangents should be non-zero
        for (const auto& vertex : meshData->vertices) {
            glm::vec3 tangent = vertex.getTangent();
            glm::vec3 bitangent = vertex.getBitangent();
            
            // Should not be zero vectors (except possibly at poles)
            // Should be normalized
            if (glm::length(tangent) > 0.001f) {
                REQUIRE(isVec3Normalized(tangent));
            }
            if (glm::length(bitangent) > 0.001f) {
                REQUIRE(isVec3Normalized(bitangent));
            }
        }
    }
}

TEST_CASE("SphereMesh index generation", "[assets][loaders][meshes][sphere_mesh][indices]") {
    SECTION("Indices form valid triangles") {
        auto meshData = SphereMesh::createMeshData(3, 4);
        
        // All indices should be valid
        for (unsigned int index : meshData->indices) {
            REQUIRE(index < meshData->vertices.size());
        }
        
        // Should have correct number of triangles
        size_t expectedTriangles = 3 * 4 * 2; // latitudeBands * longitudeBands * 2
        REQUIRE(meshData->indices.size() == expectedTriangles * 3);
    }

    SECTION("Triangle winding order is consistent") {
        auto meshData = SphereMesh::createMeshData(4, 6);

        // Check a few triangles for consistent winding
        for (size_t i = 0; i < meshData->indices.size(); i += 3) {
            unsigned int i0 = meshData->indices[i];
            unsigned int i1 = meshData->indices[i + 1];
            unsigned int i2 = meshData->indices[i + 2];

            glm::vec3 v0 = meshData->vertices[i0].getPosition();
            glm::vec3 v1 = meshData->vertices[i1].getPosition();
            glm::vec3 v2 = meshData->vertices[i2].getPosition();

            // Calculate triangle normal using cross product
            glm::vec3 edge1 = v1 - v0;
            glm::vec3 edge2 = v2 - v0;
            glm::vec3 crossProduct = glm::cross(edge1, edge2);

            // Skip degenerate triangles (very small cross product)
            if (glm::length(crossProduct) < 0.0001f) {
                continue;
            }

            glm::vec3 triangleNormal = glm::normalize(crossProduct);

            // For a sphere, triangle normal should generally point outward
            // (same direction as center of triangle from origin)
            glm::vec3 triangleCenter = (v0 + v1 + v2) / 3.0f;
            glm::vec3 expectedDirection = glm::normalize(triangleCenter);

            // Allow some tolerance for numerical precision and edge cases
            float dot = glm::dot(triangleNormal, expectedDirection);
            REQUIRE(dot > -0.5f); // Allow some variation but generally outward
        }
    }

    SECTION("Most triangles are non-degenerate") {
        auto meshData = SphereMesh::createMeshData(5, 8);

        int validTriangles = 0;
        int totalTriangles = static_cast<int>(meshData->indices.size() / 3);

        for (size_t i = 0; i < meshData->indices.size(); i += 3) {
            unsigned int i0 = meshData->indices[i];
            unsigned int i1 = meshData->indices[i + 1];
            unsigned int i2 = meshData->indices[i + 2];

            // Indices should be different
            REQUIRE(i0 != i1);
            REQUIRE(i1 != i2);
            REQUIRE(i0 != i2);

            glm::vec3 v0 = meshData->vertices[i0].getPosition();
            glm::vec3 v1 = meshData->vertices[i1].getPosition();
            glm::vec3 v2 = meshData->vertices[i2].getPosition();

            // Triangle should have non-zero area (allow some very small triangles near poles)
            glm::vec3 edge1 = v1 - v0;
            glm::vec3 edge2 = v2 - v0;
            float area = 0.5f * glm::length(glm::cross(edge1, edge2));

            if (area > 0.0001f) {
                validTriangles++;
            }
        }

        // Most triangles should be valid (allow some degenerate ones near poles)
        float validRatio = static_cast<float>(validTriangles) / totalTriangles;
        REQUIRE(validRatio >= 0.8f); // At least 80% should be valid
    }
}

TEST_CASE("SphereMesh mathematical properties", "[assets][loaders][meshes][sphere_mesh][mathematics]") {
    SECTION("Spherical coordinate mapping") {
        auto meshData = SphereMesh::createMeshData(6, 12);
        
        // Check that vertices follow spherical coordinate equations
        for (size_t i = 0; i < meshData->vertices.size(); ++i) {
            glm::vec3 pos = meshData->vertices[i].getPosition();
            
            // Convert back to spherical coordinates
            float r = glm::length(pos);
            float theta = std::acos(pos.y / r); // latitude angle
            float phi = std::atan2(pos.z, pos.x); // longitude angle
            
            // Verify radius is correct
            REQUIRE(isFloatEqual(r, 0.5f, 0.001f));
            
            // Verify angles are in expected ranges
            REQUIRE(theta >= 0.0f);
            REQUIRE(theta <= glm::pi<float>());
            REQUIRE(phi >= -glm::pi<float>());
            REQUIRE(phi <= glm::pi<float>());
        }
    }

    SECTION("UV mapping consistency") {
        auto meshData = SphereMesh::createMeshData(4, 8);
        
        // Check UV mapping follows expected pattern
        for (int lat = 0; lat <= 4; ++lat) {
            for (int lon = 0; lon <= 8; ++lon) {
                size_t vertexIndex = lat * (8 + 1) + lon;
                glm::vec2 uv = meshData->vertices[vertexIndex].getUV();
                
                float expectedU = 1.0f - (float(lon) / 8);
                float expectedV = 1.0f - (float(lat) / 4);
                
                REQUIRE(isFloatEqual(uv.x, expectedU, 0.001f));
                REQUIRE(isFloatEqual(uv.y, expectedV, 0.001f));
            }
        }
    }

    SECTION("Surface area approximation") {
        auto meshData = SphereMesh::createMeshData(16, 32);
        
        // Calculate total surface area of all triangles
        float totalArea = 0.0f;
        
        for (size_t i = 0; i < meshData->indices.size(); i += 3) {
            unsigned int i0 = meshData->indices[i];
            unsigned int i1 = meshData->indices[i + 1];
            unsigned int i2 = meshData->indices[i + 2];
            
            glm::vec3 v0 = meshData->vertices[i0].getPosition();
            glm::vec3 v1 = meshData->vertices[i1].getPosition();
            glm::vec3 v2 = meshData->vertices[i2].getPosition();
            
            glm::vec3 edge1 = v1 - v0;
            glm::vec3 edge2 = v2 - v0;
            float area = 0.5f * glm::length(glm::cross(edge1, edge2));
            totalArea += area;
        }
        
        // Expected surface area of sphere with radius 0.5: 4 * π * r² = π
        float expectedArea = glm::pi<float>();
        
        // Allow some tolerance due to tessellation approximation
        REQUIRE(isFloatEqual(totalArea, expectedArea, 0.1f));
    }
}

TEST_CASE("SphereMesh edge cases", "[assets][loaders][meshes][sphere_mesh][edge_cases]") {
    SECTION("Minimum tessellation (1x1)") {
        auto meshData = SphereMesh::createMeshData(1, 1);

        REQUIRE(meshData->vertices.size() == 4); // (1+1) * (1+1)
        REQUIRE(meshData->indices.size() == 6);  // 1 * 1 * 6

        // Should still form a valid (though very low-poly) sphere
        for (const auto& vertex : meshData->vertices) {
            glm::vec3 pos = vertex.getPosition();
            float distance = glm::length(pos);
            REQUIRE(isFloatEqual(distance, 0.5f, 0.001f));
        }
    }

    SECTION("Very low tessellation (2x3)") {
        auto meshData = SphereMesh::createMeshData(2, 3);

        REQUIRE(meshData->vertices.size() == 12); // (2+1) * (3+1)
        REQUIRE(meshData->indices.size() == 36);  // 2 * 3 * 6

        // All indices should be valid
        for (unsigned int index : meshData->indices) {
            REQUIRE(index < meshData->vertices.size());
        }
    }

    SECTION("High tessellation sphere") {
        auto meshData = SphereMesh::createMeshData(32, 64);

        REQUIRE(meshData->vertices.size() == 2145); // (32+1) * (64+1)
        REQUIRE(meshData->indices.size() == 12288); // 32 * 64 * 6

        // Verify sphere properties still hold with high tessellation
        for (const auto& vertex : meshData->vertices) {
            glm::vec3 pos = vertex.getPosition();
            float distance = glm::length(pos);
            REQUIRE(isFloatEqual(distance, 0.5f, 0.001f));

            glm::vec3 normal = vertex.getNormal();
            REQUIRE(isVec3Normalized(normal));
        }
    }

    SECTION("Asymmetric tessellation") {
        auto meshData = SphereMesh::createMeshData(4, 16);

        REQUIRE(meshData->vertices.size() == 85);  // (4+1) * (16+1)
        REQUIRE(meshData->indices.size() == 384); // 4 * 16 * 6

        // Should still maintain sphere properties
        for (const auto& vertex : meshData->vertices) {
            glm::vec3 pos = vertex.getPosition();
            float distance = glm::length(pos);
            REQUIRE(isFloatEqual(distance, 0.5f, 0.001f));
        }
    }
}

TEST_CASE("SphereMesh performance characteristics", "[assets][loaders][meshes][sphere_mesh][performance]") {
    SECTION("Large sphere generation") {
        auto meshData = SphereMesh::createMeshData(50, 100);

        // Should have (50+1) * (100+1) = 5151 vertices
        REQUIRE(meshData->vertices.size() == 5151);

        // Should have 50 * 100 * 6 = 30000 indices
        REQUIRE(meshData->indices.size() == 30000);

        // All vertices should have valid data
        for (const auto& vertex : meshData->vertices) {
            glm::vec3 pos = vertex.getPosition();
            glm::vec2 uv = vertex.getUV();
            glm::vec3 normal = vertex.getNormal();

            // Position should be on sphere surface
            float distance = glm::length(pos);
            REQUIRE(isFloatEqual(distance, 0.5f, 0.001f));

            // UV should be in [0, 1] range
            REQUIRE(uv.x >= 0.0f);
            REQUIRE(uv.x <= 1.0f);
            REQUIRE(uv.y >= 0.0f);
            REQUIRE(uv.y <= 1.0f);

            // Normal should be normalized
            REQUIRE(isVec3Normalized(normal));
        }
    }

    SECTION("Memory efficiency test") {
        // Test multiple sphere creations don't interfere
        std::vector<std::unique_ptr<MeshData>> spheres;

        for (int i = 2; i <= 6; ++i) {
            spheres.push_back(SphereMesh::createMeshData(i, i * 2));
        }

        // Verify each sphere has correct properties
        for (size_t i = 0; i < spheres.size(); ++i) {
            int latBands = static_cast<int>(i + 2);
            int lonBands = latBands * 2;

            size_t expectedVertices = (latBands + 1) * (lonBands + 1);
            size_t expectedIndices = latBands * lonBands * 6;

            REQUIRE(spheres[i]->vertices.size() == expectedVertices);
            REQUIRE(spheres[i]->indices.size() == expectedIndices);
        }
    }
}

TEST_CASE("SphereMesh real-world scenarios", "[assets][loaders][meshes][sphere_mesh][real_world]") {
    SECTION("Planet/celestial body mesh") {
        // Create a planet-like sphere with good detail
        auto meshData = SphereMesh::createMeshData(32, 64);

        REQUIRE(meshData->vertices.size() == 2145);
        REQUIRE(meshData->indices.size() == 12288);

        // Check that UV mapping is suitable for texture mapping
        bool foundUV00 = false, foundUV10 = false, foundUV01 = false, foundUV11 = false;

        for (const auto& vertex : meshData->vertices) {
            glm::vec2 uv = vertex.getUV();

            if (isVec2Equal(uv, glm::vec2(0.0f, 0.0f), 0.001f)) foundUV00 = true;
            if (isVec2Equal(uv, glm::vec2(1.0f, 0.0f), 0.001f)) foundUV10 = true;
            if (isVec2Equal(uv, glm::vec2(0.0f, 1.0f), 0.001f)) foundUV01 = true;
            if (isVec2Equal(uv, glm::vec2(1.0f, 1.0f), 0.001f)) foundUV11 = true;
        }

        REQUIRE(foundUV00);
        REQUIRE(foundUV10);
        REQUIRE(foundUV01);
        REQUIRE(foundUV11);
    }

    SECTION("Game object sphere (medium detail)") {
        // Create a sphere suitable for game objects
        auto meshData = SphereMesh::createMeshData(16, 32);

        REQUIRE(meshData->vertices.size() == 561);
        REQUIRE(meshData->indices.size() == 3072);

        // Verify tangents and bitangents for normal mapping
        for (const auto& vertex : meshData->vertices) {
            glm::vec3 normal = vertex.getNormal();
            glm::vec3 tangent = vertex.getTangent();
            glm::vec3 bitangent = vertex.getBitangent();

            REQUIRE(isVec3Normalized(normal));

            // Tangent and bitangent should be normalized (if non-zero)
            if (glm::length(tangent) > 0.001f) {
                REQUIRE(isVec3Normalized(tangent));
            }
            if (glm::length(bitangent) > 0.001f) {
                REQUIRE(isVec3Normalized(bitangent));
            }
        }
    }

    SECTION("Low-poly sphere for performance") {
        // Create a low-poly sphere for performance-critical scenarios
        auto meshData = SphereMesh::createMeshData(8, 16);

        REQUIRE(meshData->vertices.size() == 153);
        REQUIRE(meshData->indices.size() == 768);

        // Should still maintain basic sphere properties
        for (const auto& vertex : meshData->vertices) {
            glm::vec3 pos = vertex.getPosition();
            float distance = glm::length(pos);
            REQUIRE(isFloatEqual(distance, 0.5f, 0.001f));
        }

        // All indices should be valid
        for (unsigned int index : meshData->indices) {
            REQUIRE(index < meshData->vertices.size());
        }
    }

    SECTION("Particle system sphere") {
        // Very low-poly sphere for particle systems
        auto meshData = SphereMesh::createMeshData(4, 8);

        REQUIRE(meshData->vertices.size() == 45);
        REQUIRE(meshData->indices.size() == 192);

        // Should be a recognizable sphere despite low poly count
        // Check that poles are at correct positions
        glm::vec3 topPole = meshData->vertices[0].getPosition();
        glm::vec3 bottomPole = meshData->vertices[4 * 9].getPosition(); // lat=4, lon=0

        REQUIRE(isVec3Equal(topPole, glm::vec3(0.0f, 0.5f, 0.0f), 0.001f));
        REQUIRE(isVec3Equal(bottomPole, glm::vec3(0.0f, -0.5f, 0.0f), 0.001f));
    }

    SECTION("Skybox/environment sphere") {
        // Large sphere for skybox rendering
        auto meshData = SphereMesh::createMeshData(24, 48);

        REQUIRE(meshData->vertices.size() == 1225);
        REQUIRE(meshData->indices.size() == 6912);

        // For skybox, UV mapping is crucial
        for (const auto& vertex : meshData->vertices) {
            glm::vec2 uv = vertex.getUV();

            // UV coordinates should cover full range
            REQUIRE(uv.x >= 0.0f);
            REQUIRE(uv.x <= 1.0f);
            REQUIRE(uv.y >= 0.0f);
            REQUIRE(uv.y <= 1.0f);
        }

        // Normals should point outward for skybox rendering
        for (const auto& vertex : meshData->vertices) {
            glm::vec3 pos = vertex.getPosition();
            glm::vec3 normal = vertex.getNormal();

            // Normal should point in same direction as position (outward from center)
            glm::vec3 expectedNormal = glm::normalize(pos);
            REQUIRE(isVec3Equal(normal, expectedNormal, 0.001f));
        }
    }
}
