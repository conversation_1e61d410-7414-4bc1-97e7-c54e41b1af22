#include <catch2/catch_test_macros.hpp>

// C++ standard library
#include <cmath>
#include <memory>

// Third-party libraries
#include <glm/glm.hpp>

// Local includes
#include "assets/loaders/meshes/plane_mesh.hpp"
#include "test_utils.hpp"

using namespace IronFrost;

TEST_CASE("PlaneMesh basic functionality", "[assets][loaders][meshes][plane_mesh][basic]") {
    SECTION("Default plane options") {
        PlaneMesh::PlaneOptions options;
        
        REQUIRE(options.width == 100.0f);
        REQUIRE(options.depth == 100.0f);
        REQUIRE(options.widthSegments == 10);
        REQUIRE(options.depthSegments == 10);
    }

    SECTION("Create mesh with default options") {
        PlaneMesh::PlaneOptions options;
        auto meshData = PlaneMesh::createMeshData(options);
        
        REQUIRE(meshData != nullptr);
        REQUIRE_FALSE(meshData->vertices.empty());
        REQUIRE_FALSE(meshData->indices.empty());
        
        // Expected vertices: (widthSegments + 1) * (depthSegments + 1)
        size_t expectedVertices = (options.widthSegments + 1) * (options.depthSegments + 1);
        REQUIRE(meshData->vertices.size() == expectedVertices);
        
        // Expected indices: widthSegments * depthSegments * 6 (2 triangles per quad)
        size_t expectedIndices = options.widthSegments * options.depthSegments * 6;
        REQUIRE(meshData->indices.size() == expectedIndices);
    }

    SECTION("Create mesh with custom options") {
        PlaneMesh::PlaneOptions options{50.0f, 30.0f, 5, 3};
        auto meshData = PlaneMesh::createMeshData(options);
        
        REQUIRE(meshData != nullptr);
        
        // Expected vertices: (5 + 1) * (3 + 1) = 6 * 4 = 24
        REQUIRE(meshData->vertices.size() == 24);
        
        // Expected indices: 5 * 3 * 6 = 90
        REQUIRE(meshData->indices.size() == 90);
    }
}

TEST_CASE("PlaneMesh vertex properties", "[assets][loaders][meshes][plane_mesh][vertices]") {
    SECTION("Vertex positions are correct") {
        PlaneMesh::PlaneOptions options{10.0f, 6.0f, 2, 1};
        auto meshData = PlaneMesh::createMeshData(options);
        
        // Expected vertices: (2 + 1) * (1 + 1) = 3 * 2 = 6 vertices
        REQUIRE(meshData->vertices.size() == 6);
        
        // Check corner vertices
        glm::vec3 pos0 = meshData->vertices[0].getPosition();
        glm::vec3 pos2 = meshData->vertices[2].getPosition();
        glm::vec3 pos3 = meshData->vertices[3].getPosition();
        glm::vec3 pos5 = meshData->vertices[5].getPosition();
        
        // Bottom-left corner
        REQUIRE(isVec3Equal(pos0, glm::vec3(-5.0f, 0.0f, -3.0f)));
        // Bottom-right corner
        REQUIRE(isVec3Equal(pos2, glm::vec3(5.0f, 0.0f, -3.0f)));
        // Top-left corner
        REQUIRE(isVec3Equal(pos3, glm::vec3(-5.0f, 0.0f, 3.0f)));
        // Top-right corner
        REQUIRE(isVec3Equal(pos5, glm::vec3(5.0f, 0.0f, 3.0f)));
    }

    SECTION("All vertices have Y = 0") {
        PlaneMesh::PlaneOptions options{20.0f, 15.0f, 4, 3};
        auto meshData = PlaneMesh::createMeshData(options);
        
        for (const auto& vertex : meshData->vertices) {
            glm::vec3 pos = vertex.getPosition();
            REQUIRE(isFloatEqual(pos.y, 0.0f));
        }
    }

    SECTION("UV coordinates are correct") {
        PlaneMesh::PlaneOptions options{10.0f, 6.0f, 2, 1};
        auto meshData = PlaneMesh::createMeshData(options);
        
        // Check UV coordinates for corner vertices
        glm::vec2 uv0 = meshData->vertices[0].getUV();
        glm::vec2 uv2 = meshData->vertices[2].getUV();
        glm::vec2 uv3 = meshData->vertices[3].getUV();
        glm::vec2 uv5 = meshData->vertices[5].getUV();
        
        // Bottom-left: (0, 0)
        REQUIRE(isVec2Equal(uv0, glm::vec2(0.0f, 0.0f)));
        // Bottom-right: (1, 0)
        REQUIRE(isVec2Equal(uv2, glm::vec2(1.0f, 0.0f)));
        // Top-left: (0, 1)
        REQUIRE(isVec2Equal(uv3, glm::vec2(0.0f, 1.0f)));
        // Top-right: (1, 1)
        REQUIRE(isVec2Equal(uv5, glm::vec2(1.0f, 1.0f)));
    }

    SECTION("All normals point upward") {
        PlaneMesh::PlaneOptions options{8.0f, 12.0f, 3, 2};
        auto meshData = PlaneMesh::createMeshData(options);
        
        glm::vec3 expectedNormal(0.0f, 1.0f, 0.0f);
        
        for (const auto& vertex : meshData->vertices) {
            glm::vec3 normal = vertex.getNormal();
            REQUIRE(isVec3Equal(normal, expectedNormal));
        }
    }

    SECTION("Tangents and bitangents are calculated") {
        PlaneMesh::PlaneOptions options{4.0f, 4.0f, 1, 1};
        auto meshData = PlaneMesh::createMeshData(options);
        
        // After calculateTangentsAndBitangents(), tangents and bitangents should be non-zero
        for (const auto& vertex : meshData->vertices) {
            glm::vec3 tangent = vertex.getTangent();
            glm::vec3 bitangent = vertex.getBitangent();
            
            // Should not be zero vectors
            REQUIRE_FALSE(isVec3Equal(tangent, glm::vec3(0.0f)));
            REQUIRE_FALSE(isVec3Equal(bitangent, glm::vec3(0.0f)));
            
            // Should be normalized
            REQUIRE(isVec3Normalized(tangent));
            REQUIRE(isVec3Normalized(bitangent));
        }
    }
}

TEST_CASE("PlaneMesh index generation", "[assets][loaders][meshes][plane_mesh][indices]") {
    SECTION("Indices form valid triangles") {
        PlaneMesh::PlaneOptions options{6.0f, 4.0f, 1, 1};
        auto meshData = PlaneMesh::createMeshData(options);
        
        // Should have 4 vertices and 6 indices (2 triangles)
        REQUIRE(meshData->vertices.size() == 4);
        REQUIRE(meshData->indices.size() == 6);
        
        // All indices should be valid
        for (unsigned int index : meshData->indices) {
            REQUIRE(index < meshData->vertices.size());
        }
        
        // Check triangle winding order (should be counter-clockwise)
        // First triangle: indices 0, 1, 2
        REQUIRE(meshData->indices[0] == 0);
        REQUIRE(meshData->indices[1] == 2);
        REQUIRE(meshData->indices[2] == 1);
        
        // Second triangle: indices 3, 4, 5
        REQUIRE(meshData->indices[3] == 1);
        REQUIRE(meshData->indices[4] == 2);
        REQUIRE(meshData->indices[5] == 3);
    }

    SECTION("Multiple segments create correct triangles") {
        PlaneMesh::PlaneOptions options{8.0f, 6.0f, 2, 1};
        auto meshData = PlaneMesh::createMeshData(options);
        
        // Should have (2+1) * (1+1) = 6 vertices
        REQUIRE(meshData->vertices.size() == 6);
        
        // Should have 2 * 1 * 6 = 12 indices (4 triangles)
        REQUIRE(meshData->indices.size() == 12);
        
        // All indices should be valid
        for (unsigned int index : meshData->indices) {
            REQUIRE(index < meshData->vertices.size());
        }
    }

    SECTION("Index calculation for larger grids") {
        PlaneMesh::PlaneOptions options{10.0f, 10.0f, 3, 2};
        auto meshData = PlaneMesh::createMeshData(options);
        
        // Should have (3+1) * (2+1) = 12 vertices
        REQUIRE(meshData->vertices.size() == 12);
        
        // Should have 3 * 2 * 6 = 36 indices
        REQUIRE(meshData->indices.size() == 36);
        
        // Verify no index is out of bounds
        for (unsigned int index : meshData->indices) {
            REQUIRE(index < meshData->vertices.size());
        }
    }
}

TEST_CASE("PlaneMesh edge cases", "[assets][loaders][meshes][plane_mesh][edge_cases]") {
    SECTION("Minimum segments (1x1)") {
        PlaneMesh::PlaneOptions options{2.0f, 2.0f, 1, 1};
        auto meshData = PlaneMesh::createMeshData(options);
        
        REQUIRE(meshData->vertices.size() == 4);
        REQUIRE(meshData->indices.size() == 6);
        
        // Check that all vertices are at correct positions
        glm::vec3 positions[4] = {
            meshData->vertices[0].getPosition(),
            meshData->vertices[1].getPosition(),
            meshData->vertices[2].getPosition(),
            meshData->vertices[3].getPosition()
        };
        
        // Should form a 2x2 square centered at origin
        REQUIRE(isVec3Equal(positions[0], glm::vec3(-1.0f, 0.0f, -1.0f)));
        REQUIRE(isVec3Equal(positions[1], glm::vec3(1.0f, 0.0f, -1.0f)));
        REQUIRE(isVec3Equal(positions[2], glm::vec3(-1.0f, 0.0f, 1.0f)));
        REQUIRE(isVec3Equal(positions[3], glm::vec3(1.0f, 0.0f, 1.0f)));
    }

    SECTION("Very small dimensions") {
        PlaneMesh::PlaneOptions options{0.1f, 0.05f, 1, 1};
        auto meshData = PlaneMesh::createMeshData(options);
        
        REQUIRE(meshData->vertices.size() == 4);
        REQUIRE(meshData->indices.size() == 6);
        
        // Check corner positions
        glm::vec3 pos0 = meshData->vertices[0].getPosition();
        glm::vec3 pos3 = meshData->vertices[3].getPosition();
        
        REQUIRE(isVec3Equal(pos0, glm::vec3(-0.05f, 0.0f, -0.025f)));
        REQUIRE(isVec3Equal(pos3, glm::vec3(0.05f, 0.0f, 0.025f)));
    }

    SECTION("Large dimensions") {
        PlaneMesh::PlaneOptions options{1000.0f, 500.0f, 2, 1};
        auto meshData = PlaneMesh::createMeshData(options);
        
        REQUIRE(meshData->vertices.size() == 6);
        REQUIRE(meshData->indices.size() == 12);
        
        // Check corner positions
        glm::vec3 pos0 = meshData->vertices[0].getPosition();
        glm::vec3 pos5 = meshData->vertices[5].getPosition();
        
        REQUIRE(isVec3Equal(pos0, glm::vec3(-500.0f, 0.0f, -250.0f)));
        REQUIRE(isVec3Equal(pos5, glm::vec3(500.0f, 0.0f, 250.0f)));
    }

    SECTION("Many segments") {
        PlaneMesh::PlaneOptions options{4.0f, 4.0f, 10, 8};
        auto meshData = PlaneMesh::createMeshData(options);
        
        // Should have (10+1) * (8+1) = 99 vertices
        REQUIRE(meshData->vertices.size() == 99);
        
        // Should have 10 * 8 * 6 = 480 indices
        REQUIRE(meshData->indices.size() == 480);
        
        // All indices should be valid
        for (unsigned int index : meshData->indices) {
            REQUIRE(index < meshData->vertices.size());
        }
    }
}

TEST_CASE("PlaneMesh mathematical properties", "[assets][loaders][meshes][plane_mesh][mathematics]") {
    SECTION("Vertex spacing is uniform") {
        PlaneMesh::PlaneOptions options{12.0f, 8.0f, 3, 2};
        auto meshData = PlaneMesh::createMeshData(options);

        // Check horizontal spacing (width direction)
        float expectedXSpacing = 12.0f / 3.0f; // 4.0f

        // Check first row vertices (z = -4.0f)
        for (int i = 0; i < 3; ++i) {
            glm::vec3 pos1 = meshData->vertices[i].getPosition();
            glm::vec3 pos2 = meshData->vertices[i + 1].getPosition();

            float actualSpacing = pos2.x - pos1.x;
            REQUIRE(isFloatEqual(actualSpacing, expectedXSpacing));
        }

        // Check vertical spacing (depth direction)
        float expectedZSpacing = 8.0f / 2.0f; // 4.0f

        // Check first column vertices (x = -6.0f)
        for (int i = 0; i < 2; ++i) {
            int idx1 = i * 4; // 4 vertices per row
            int idx2 = (i + 1) * 4;

            glm::vec3 pos1 = meshData->vertices[idx1].getPosition();
            glm::vec3 pos2 = meshData->vertices[idx2].getPosition();

            float actualSpacing = pos2.z - pos1.z;
            REQUIRE(isFloatEqual(actualSpacing, expectedZSpacing));
        }
    }

    SECTION("UV coordinates are uniformly distributed") {
        PlaneMesh::PlaneOptions options{6.0f, 4.0f, 2, 1};
        auto meshData = PlaneMesh::createMeshData(options);

        // Check U coordinate progression
        float expectedUStep = 1.0f / 2.0f; // 0.5f

        // First row (z = 0)
        for (int i = 0; i < 2; ++i) {
            glm::vec2 uv1 = meshData->vertices[i].getUV();
            glm::vec2 uv2 = meshData->vertices[i + 1].getUV();

            float uStep = uv2.x - uv1.x;
            REQUIRE(isFloatEqual(uStep, expectedUStep));
        }

        // Check V coordinate progression
        float expectedVStep = 1.0f / 1.0f; // 1.0f

        // First column (x = 0)
        glm::vec2 uv1 = meshData->vertices[0].getUV();
        glm::vec2 uv2 = meshData->vertices[3].getUV(); // Next row

        float vStep = uv2.y - uv1.y;
        REQUIRE(isFloatEqual(vStep, expectedVStep));
    }

    SECTION("Tangent and bitangent orthogonality") {
        PlaneMesh::PlaneOptions options{4.0f, 4.0f, 2, 2};
        auto meshData = PlaneMesh::createMeshData(options);

        for (const auto& vertex : meshData->vertices) {
            glm::vec3 normal = vertex.getNormal();
            glm::vec3 tangent = vertex.getTangent();
            glm::vec3 bitangent = vertex.getBitangent();

            // Tangent and bitangent should be orthogonal to normal
            REQUIRE(areVec3Orthogonal(normal, tangent));
            REQUIRE(areVec3Orthogonal(normal, bitangent));

            // Tangent and bitangent should be orthogonal to each other
            REQUIRE(areVec3Orthogonal(tangent, bitangent));
        }
    }

    SECTION("Triangle area calculation") {
        PlaneMesh::PlaneOptions options{2.0f, 2.0f, 1, 1};
        auto meshData = PlaneMesh::createMeshData(options);

        // Get first triangle vertices
        unsigned int i0 = meshData->indices[0];
        unsigned int i1 = meshData->indices[1];
        unsigned int i2 = meshData->indices[2];

        glm::vec3 v0 = meshData->vertices[i0].getPosition();
        glm::vec3 v1 = meshData->vertices[i1].getPosition();
        glm::vec3 v2 = meshData->vertices[i2].getPosition();

        // Calculate triangle area using cross product
        glm::vec3 edge1 = v1 - v0;
        glm::vec3 edge2 = v2 - v0;
        float area = 0.5f * glm::length(glm::cross(edge1, edge2));

        // Expected area: half of the total plane area (2.0f for a 2x2 plane)
        REQUIRE(isFloatEqual(area, 2.0f));
    }
}

TEST_CASE("PlaneMesh performance characteristics", "[assets][loaders][meshes][plane_mesh][performance]") {
    SECTION("Large mesh generation") {
        PlaneMesh::PlaneOptions options{100.0f, 100.0f, 50, 50};
        auto meshData = PlaneMesh::createMeshData(options);

        // Should have (50+1) * (50+1) = 2601 vertices
        REQUIRE(meshData->vertices.size() == 2601);

        // Should have 50 * 50 * 6 = 15000 indices
        REQUIRE(meshData->indices.size() == 15000);

        // All vertices should have valid data
        for (const auto& vertex : meshData->vertices) {
            glm::vec3 pos = vertex.getPosition();
            glm::vec2 uv = vertex.getUV();
            glm::vec3 normal = vertex.getNormal();

            // Position should be within bounds
            REQUIRE(pos.x >= -50.0f);
            REQUIRE(pos.x <= 50.0f);
            REQUIRE(pos.z >= -50.0f);
            REQUIRE(pos.z <= 50.0f);
            REQUIRE(isFloatEqual(pos.y, 0.0f));

            // UV should be in [0, 1] range
            REQUIRE(uv.x >= 0.0f);
            REQUIRE(uv.x <= 1.0f);
            REQUIRE(uv.y >= 0.0f);
            REQUIRE(uv.y <= 1.0f);

            // Normal should be up
            REQUIRE(isVec3Equal(normal, glm::vec3(0.0f, 1.0f, 0.0f)));
        }
    }

    SECTION("Memory efficiency test") {
        // Test multiple mesh creations don't interfere
        std::vector<std::unique_ptr<MeshData>> meshes;

        for (unsigned int i = 1; i <= 5; ++i) {
            PlaneMesh::PlaneOptions options{static_cast<float>(i * 2), static_cast<float>(i * 2), i, i};
            meshes.push_back(PlaneMesh::createMeshData(options));
        }

        // Verify each mesh has correct properties
        for (size_t i = 0; i < meshes.size(); ++i) {
            unsigned int segments = static_cast<unsigned int>(i + 1);
            size_t expectedVertices = (segments + 1) * (segments + 1);
            size_t expectedIndices = segments * segments * 6;

            REQUIRE(meshes[i]->vertices.size() == expectedVertices);
            REQUIRE(meshes[i]->indices.size() == expectedIndices);
        }
    }
}

TEST_CASE("PlaneMesh real-world scenarios", "[assets][loaders][meshes][plane_mesh][real_world]") {
    SECTION("Terrain base mesh") {
        // Create a terrain-like base mesh
        PlaneMesh::PlaneOptions options{1000.0f, 1000.0f, 100, 100};
        auto meshData = PlaneMesh::createMeshData(options);

        REQUIRE(meshData->vertices.size() == 10201); // (100+1)^2
        REQUIRE(meshData->indices.size() == 60000);  // 100^2 * 6

        // Check corner vertices for terrain bounds
        glm::vec3 corner1 = meshData->vertices[0].getPosition();
        glm::vec3 corner2 = meshData->vertices[100].getPosition();
        glm::vec3 corner3 = meshData->vertices[10100].getPosition();
        glm::vec3 corner4 = meshData->vertices[10200].getPosition();

        REQUIRE(isVec3Equal(corner1, glm::vec3(-500.0f, 0.0f, -500.0f)));
        REQUIRE(isVec3Equal(corner2, glm::vec3(500.0f, 0.0f, -500.0f)));
        REQUIRE(isVec3Equal(corner3, glm::vec3(-500.0f, 0.0f, 500.0f)));
        REQUIRE(isVec3Equal(corner4, glm::vec3(500.0f, 0.0f, 500.0f)));
    }

    SECTION("Floor plane for indoor scene") {
        // Create a room floor
        PlaneMesh::PlaneOptions options{20.0f, 15.0f, 4, 3};
        auto meshData = PlaneMesh::createMeshData(options);

        REQUIRE(meshData->vertices.size() == 20); // (4+1) * (3+1)
        REQUIRE(meshData->indices.size() == 72);  // 4 * 3 * 6

        // All vertices should be at floor level (y = 0)
        for (const auto& vertex : meshData->vertices) {
            REQUIRE(isFloatEqual(vertex.getPosition().y, 0.0f));
        }

        // UV coordinates should tile properly
        for (const auto& vertex : meshData->vertices) {
            glm::vec2 uv = vertex.getUV();
            REQUIRE(uv.x >= 0.0f);
            REQUIRE(uv.x <= 1.0f);
            REQUIRE(uv.y >= 0.0f);
            REQUIRE(uv.y <= 1.0f);
        }
    }

    SECTION("Water surface mesh") {
        // Create a water surface with moderate tessellation
        PlaneMesh::PlaneOptions options{50.0f, 50.0f, 25, 25};
        auto meshData = PlaneMesh::createMeshData(options);

        REQUIRE(meshData->vertices.size() == 676);  // (25+1)^2
        REQUIRE(meshData->indices.size() == 3750); // 25^2 * 6

        // Check that tangents and bitangents are properly calculated for water shading
        for (const auto& vertex : meshData->vertices) {
            glm::vec3 tangent = vertex.getTangent();
            glm::vec3 bitangent = vertex.getBitangent();

            REQUIRE(isVec3Normalized(tangent));
            REQUIRE(isVec3Normalized(bitangent));

            // For a horizontal plane, tangent should be roughly in X direction
            // and bitangent in Z direction (after normalization)
            REQUIRE(std::abs(tangent.y) < 0.1f); // Minimal Y component
            REQUIRE(std::abs(bitangent.y) < 0.1f); // Minimal Y component
        }
    }

    SECTION("UI background quad") {
        // Create a simple quad for UI backgrounds
        PlaneMesh::PlaneOptions options{2.0f, 2.0f, 1, 1};
        auto meshData = PlaneMesh::createMeshData(options);

        REQUIRE(meshData->vertices.size() == 4);
        REQUIRE(meshData->indices.size() == 6);

        // Check that UV coordinates cover the full [0,1] range
        bool foundUV00 = false, foundUV10 = false, foundUV01 = false, foundUV11 = false;

        for (const auto& vertex : meshData->vertices) {
            glm::vec2 uv = vertex.getUV();

            if (isVec2Equal(uv, glm::vec2(0.0f, 0.0f))) foundUV00 = true;
            if (isVec2Equal(uv, glm::vec2(1.0f, 0.0f))) foundUV10 = true;
            if (isVec2Equal(uv, glm::vec2(0.0f, 1.0f))) foundUV01 = true;
            if (isVec2Equal(uv, glm::vec2(1.0f, 1.0f))) foundUV11 = true;
        }

        REQUIRE(foundUV00);
        REQUIRE(foundUV10);
        REQUIRE(foundUV01);
        REQUIRE(foundUV11);
    }
}
