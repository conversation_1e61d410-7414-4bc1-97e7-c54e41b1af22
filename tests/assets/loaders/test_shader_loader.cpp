#include <catch2/catch_test_macros.hpp>

#include "assets/loaders/shader_loader.hpp"
#include "assets/asset_data_types.hpp"
#include "../../mocks/test_mocks.hpp"

#include <memory>
#include <string>

using namespace IronFrost;

// Extended MockVFS for shader testing
class ShaderMockVFS : public MockVFS {
private:
    std::string vertexShaderSource_;
    std::string fragmentShaderSource_;
    
public:
    void setShaderSources(const std::string& vertexSource, const std::string& fragmentSource) {
        vertexShaderSource_ = vertexSource;
        fragmentShaderSource_ = fragmentSource;
    }
    
    std::string readFile(const std::string& path) const override {
        if (path.ends_with(".vs")) {
            return vertexShaderSource_;
        } else if (path.ends_with(".fs")) {
            return fragmentShaderSource_;
        }
        return "";
    }
};

TEST_CASE("ShaderLoader constructor", "[assets][shader_loader][constructor]") {
    ShaderMockVFS mockVFS;
    
    SECTION("Constructor with VFS reference") {
        ShaderLoader loader(mockVFS);
        
        // Test that constructor works without issues
        REQUIRE(true);
    }
}

TEST_CASE("ShaderLoader basic functionality", "[assets][shader_loader][basic]") {
    ShaderMockVFS mockVFS;
    ShaderLoader loader(mockVFS);
    
    SECTION("Load shader with vertex and fragment sources") {
        std::string vertexSource = R"(
            #version 330 core
            layout (location = 0) in vec3 aPos;
            void main() {
                gl_Position = vec4(aPos, 1.0);
            }
        )";
        
        std::string fragmentSource = R"(
            #version 330 core
            out vec4 FragColor;
            void main() {
                FragColor = vec4(1.0, 0.5, 0.2, 1.0);
            }
        )";
        
        mockVFS.setShaderSources(vertexSource, fragmentSource);
        
        auto shaderData = loader.loadShader("basic_shader");
        
        REQUIRE(shaderData != nullptr);
        REQUIRE(shaderData->vertexShader == vertexSource);
        REQUIRE(shaderData->fragmentShader == fragmentSource);
    }
    
    SECTION("Load shader with empty sources") {
        mockVFS.setShaderSources("", "");
        
        auto shaderData = loader.loadShader("empty_shader");
        
        REQUIRE(shaderData != nullptr);
        REQUIRE(shaderData->vertexShader.empty());
        REQUIRE(shaderData->fragmentShader.empty());
    }
    
    SECTION("Load shader with different path names") {
        std::string vertexSource = "#version 330 core\nvoid main() {}";
        std::string fragmentSource = "#version 330 core\nvoid main() {}";
        
        mockVFS.setShaderSources(vertexSource, fragmentSource);
        
        std::vector<std::string> shaderPaths = {
            "test_shader",
            "shaders/basic",
            "assets/shaders/complex_shader",
            "shader_with_underscores",
            "shader-with-dashes"
        };
        
        for (const auto& path : shaderPaths) {
            auto shaderData = loader.loadShader(path);
            
            REQUIRE(shaderData != nullptr);
            REQUIRE(shaderData->vertexShader == vertexSource);
            REQUIRE(shaderData->fragmentShader == fragmentSource);
        }
    }
}

TEST_CASE("ShaderLoader file extension handling", "[assets][shader_loader][extensions]") {
    ShaderMockVFS mockVFS;
    ShaderLoader loader(mockVFS);
    
    SECTION("Correctly appends .vs and .fs extensions") {
        std::string vertexSource = "vertex shader content";
        std::string fragmentSource = "fragment shader content";
        
        mockVFS.setShaderSources(vertexSource, fragmentSource);
        
        auto shaderData = loader.loadShader("test_shader");
        
        // The loader should have requested "test_shader.vs" and "test_shader.fs"
        REQUIRE(shaderData != nullptr);
        REQUIRE(shaderData->vertexShader == vertexSource);
        REQUIRE(shaderData->fragmentShader == fragmentSource);
    }
}

TEST_CASE("ShaderLoader memory management", "[assets][shader_loader][memory]") {
    ShaderMockVFS mockVFS;
    ShaderLoader loader(mockVFS);
    
    SECTION("Multiple shader loading") {
        std::string vertexSource = "vertex content";
        std::string fragmentSource = "fragment content";
        
        mockVFS.setShaderSources(vertexSource, fragmentSource);
        
        std::vector<std::unique_ptr<ShaderData>> shaders;
        
        // Load multiple shaders
        for (int i = 0; i < 5; ++i) {
            shaders.push_back(loader.loadShader("shader_" + std::to_string(i)));
        }
        
        REQUIRE(shaders.size() == 5);
        
        // Verify all shaders are valid and independent
        for (const auto& shader : shaders) {
            REQUIRE(shader != nullptr);
            REQUIRE(shader->vertexShader == vertexSource);
            REQUIRE(shader->fragmentShader == fragmentSource);
        }
        
        // Clear all shaders - should not cause memory issues
        shaders.clear();
        
        // Test passes if no memory errors occur
        REQUIRE(true);
    }
    
    SECTION("Shader data independence") {
        mockVFS.setShaderSources("vertex1", "fragment1");
        auto shader1 = loader.loadShader("shader1");
        
        mockVFS.setShaderSources("vertex2", "fragment2");
        auto shader2 = loader.loadShader("shader2");
        
        REQUIRE(shader1 != nullptr);
        REQUIRE(shader2 != nullptr);
        REQUIRE(shader1.get() != shader2.get());
        
        // Shaders should have different content
        REQUIRE(shader1->vertexShader != shader2->vertexShader);
        REQUIRE(shader1->fragmentShader != shader2->fragmentShader);
    }
}

TEST_CASE("ShaderLoader lifecycle", "[assets][shader_loader][lifecycle]") {
    ShaderMockVFS mockVFS;
    
    SECTION("Loader lifecycle with shader data") {
        std::unique_ptr<ShaderData> shaderData;
        
        {
            ShaderLoader loader(mockVFS);
            mockVFS.setShaderSources("vertex", "fragment");
            shaderData = loader.loadShader("test_shader");
            
            // loader goes out of scope here
        }
        
        // Test that ShaderData survives loader destruction
        REQUIRE(shaderData != nullptr);
        REQUIRE(shaderData->vertexShader == "vertex");
        REQUIRE(shaderData->fragmentShader == "fragment");
    }
    
    SECTION("Multiple loaders with same VFS") {
        ShaderLoader loader1(mockVFS);
        ShaderLoader loader2(mockVFS);
        
        mockVFS.setShaderSources("shared_vertex", "shared_fragment");
        
        auto shader1 = loader1.loadShader("shader1");
        auto shader2 = loader2.loadShader("shader2");
        
        REQUIRE(shader1 != nullptr);
        REQUIRE(shader2 != nullptr);
        REQUIRE(shader1.get() != shader2.get());
        
        // Both should have same content since they use same VFS
        REQUIRE(shader1->vertexShader == shader2->vertexShader);
        REQUIRE(shader1->fragmentShader == shader2->fragmentShader);
    }
}

TEST_CASE("ShaderLoader edge cases", "[assets][shader_loader][edge_cases]") {
    ShaderMockVFS mockVFS;
    ShaderLoader loader(mockVFS);
    
    SECTION("Very long shader sources") {
        std::string longVertexSource(10000, 'v');
        std::string longFragmentSource(10000, 'f');
        
        mockVFS.setShaderSources(longVertexSource, longFragmentSource);
        
        auto shaderData = loader.loadShader("long_shader");
        
        REQUIRE(shaderData != nullptr);
        REQUIRE(shaderData->vertexShader.length() == 10000);
        REQUIRE(shaderData->fragmentShader.length() == 10000);
        REQUIRE(shaderData->vertexShader == longVertexSource);
        REQUIRE(shaderData->fragmentShader == longFragmentSource);
    }
    
    SECTION("Shader sources with special characters") {
        std::string vertexSource = "vertex\nwith\ttabs\rand\nnewlines";
        std::string fragmentSource = "fragment with unicode: αβγ and symbols: !@#$%^&*()";
        
        mockVFS.setShaderSources(vertexSource, fragmentSource);
        
        auto shaderData = loader.loadShader("special_shader");
        
        REQUIRE(shaderData != nullptr);
        REQUIRE(shaderData->vertexShader == vertexSource);
        REQUIRE(shaderData->fragmentShader == fragmentSource);
    }
    
    SECTION("Path with special characters") {
        std::string vertexSource = "vertex";
        std::string fragmentSource = "fragment";
        
        mockVFS.setShaderSources(vertexSource, fragmentSource);
        
        std::vector<std::string> specialPaths = {
            "shader with spaces",
            "shader-with-dashes",
            "shader_with_underscores",
            "shader.with.dots",
            "path/to/shader"
        };
        
        for (const auto& path : specialPaths) {
            auto shaderData = loader.loadShader(path);
            
            REQUIRE(shaderData != nullptr);
            REQUIRE(shaderData->vertexShader == vertexSource);
            REQUIRE(shaderData->fragmentShader == fragmentSource);
        }
    }
}
