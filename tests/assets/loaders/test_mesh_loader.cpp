#include <catch2/catch_test_macros.hpp>

#include "assets/loaders/mesh_loader.hpp"
#include "assets/asset_data_types.hpp"
#include "assets/asset_primitive_types.hpp"
#include "../../mocks/test_mocks.hpp"

#include <memory>
#include <stdexcept>

using namespace IronFrost;

TEST_CASE("MeshLoader constructor", "[assets][mesh_loader][constructor]") {
    MockVFS mockVFS;
    
    SECTION("Constructor with VFS reference") {
        MeshLoader loader(mockVFS);
        
        // Test that constructor works without issues
        // MeshLoader doesn't use VFS for primitive creation, but stores the reference
        REQUIRE(true);
    }
}

TEST_CASE("MeshLoader primitive creation", "[assets][mesh_loader][primitives]") {
    MockVFS mockVFS;
    MeshLoader loader(mockVFS);
    
    SECTION("Create triangle primitive") {
        auto meshData = loader.createPrimitive(PrimitiveType::TRIANGLE);
        
        REQUIRE(meshData != nullptr);
        REQUIRE(meshData->vertices.size() == 3);
        REQUIRE(meshData->indices.size() == 3);
        
        // Verify triangle has proper vertex data
        for (const auto& vertex : meshData->vertices) {
            // Each vertex should have valid position, UV, and normal data
            const auto& pos = vertex.getPosition();
            const auto& uv = vertex.getUV();
            const auto& normal = vertex.getNormal();
            
            REQUIRE(std::isfinite(pos.x));
            REQUIRE(std::isfinite(pos.y));
            REQUIRE(std::isfinite(pos.z));
            REQUIRE(std::isfinite(uv.x));
            REQUIRE(std::isfinite(uv.y));
            REQUIRE(std::isfinite(normal.x));
            REQUIRE(std::isfinite(normal.y));
            REQUIRE(std::isfinite(normal.z));
        }
        
        // Verify indices are valid
        for (unsigned int index : meshData->indices) {
            REQUIRE(index < meshData->vertices.size());
        }
    }
    
    SECTION("Create quad primitive") {
        auto meshData = loader.createPrimitive(PrimitiveType::QUAD);
        
        REQUIRE(meshData != nullptr);
        REQUIRE(meshData->vertices.size() == 4);
        REQUIRE(meshData->indices.size() == 6); // 2 triangles = 6 indices
        
        // Verify all indices are valid
        for (unsigned int index : meshData->indices) {
            REQUIRE(index < meshData->vertices.size());
        }
    }
    
    SECTION("Create cube primitive") {
        auto meshData = loader.createPrimitive(PrimitiveType::CUBE);
        
        REQUIRE(meshData != nullptr);
        REQUIRE(meshData->vertices.size() == 36); // 6 faces * 6 vertices per face
        REQUIRE(meshData->indices.size() == 36);
        
        // Verify cube has proper dimensions (should be unit cube from -0.5 to 0.5)
        bool hasMinVertex = false, hasMaxVertex = false;
        for (const auto& vertex : meshData->vertices) {
            const auto& pos = vertex.getPosition();
            
            // Check if we have vertices at the expected cube bounds
            if (std::abs(pos.x + 0.5f) < 0.001f || std::abs(pos.x - 0.5f) < 0.001f) {
                if (std::abs(pos.y + 0.5f) < 0.001f || std::abs(pos.y - 0.5f) < 0.001f) {
                    if (std::abs(pos.z + 0.5f) < 0.001f || std::abs(pos.z - 0.5f) < 0.001f) {
                        if (pos.x < 0 && pos.y < 0 && pos.z < 0) hasMinVertex = true;
                        if (pos.x > 0 && pos.y > 0 && pos.z > 0) hasMaxVertex = true;
                    }
                }
            }
        }
        
        REQUIRE(hasMinVertex);
        REQUIRE(hasMaxVertex);
    }
    
    SECTION("Create sphere primitive with default parameters") {
        auto meshData = loader.createPrimitive(PrimitiveType::SPHERE);
        
        REQUIRE(meshData != nullptr);
        REQUIRE(meshData->vertices.size() > 0);
        REQUIRE(meshData->indices.size() > 0);
        REQUIRE(meshData->indices.size() % 3 == 0); // Should be triangles
        
        // Verify sphere vertices are roughly on unit sphere surface
        for (const auto& vertex : meshData->vertices) {
            const auto& pos = vertex.getPosition();
            float distance = glm::length(pos);
            
            // Should be approximately on sphere surface (allowing some tolerance for discretization)
            // The sphere appears to have radius 0.5, so check for that
            REQUIRE(distance >= 0.4f);
            REQUIRE(distance <= 0.6f);
        }
    }
    
    SECTION("Create plane primitive with default parameters") {
        auto meshData = loader.createPrimitive(PrimitiveType::PLANE);
        
        REQUIRE(meshData != nullptr);
        REQUIRE(meshData->vertices.size() > 0);
        REQUIRE(meshData->indices.size() > 0);
        REQUIRE(meshData->indices.size() % 3 == 0); // Should be triangles
        
        // Verify plane vertices are in XZ plane (Y should be 0)
        for (const auto& vertex : meshData->vertices) {
            const auto& pos = vertex.getPosition();
            REQUIRE(std::abs(pos.y) < 0.001f); // Should be in XZ plane
        }
    }
}

TEST_CASE("MeshLoader primitive parameters", "[assets][mesh_loader][parameters]") {
    MockVFS mockVFS;
    MeshLoader loader(mockVFS);
    
    SECTION("Create sphere with custom parameters") {
        PrimitiveParams params;
        params.set<int>("lat-bands", 16);
        params.set<int>("long-bands", 16);
        
        auto meshData = loader.createPrimitive(PrimitiveType::SPHERE, params);
        
        REQUIRE(meshData != nullptr);
        REQUIRE(meshData->vertices.size() > 0);
        REQUIRE(meshData->indices.size() > 0);
        
        // With fewer bands, should have fewer vertices than default (32x32)
        auto defaultMesh = loader.createPrimitive(PrimitiveType::SPHERE);
        REQUIRE(meshData->vertices.size() < defaultMesh->vertices.size());
    }
    
    SECTION("Create plane with custom parameters") {
        PrimitiveParams params;
        params.set<float>("width", 5.0f);
        params.set<float>("depth", 3.0f);
        params.set<unsigned int>("width-segments", 5);
        params.set<unsigned int>("depth-segments", 3);
        
        auto meshData = loader.createPrimitive(PrimitiveType::PLANE, params);
        
        REQUIRE(meshData != nullptr);
        REQUIRE(meshData->vertices.size() > 0);
        REQUIRE(meshData->indices.size() > 0);
        
        // Verify plane dimensions
        float minX = std::numeric_limits<float>::max();
        float maxX = std::numeric_limits<float>::lowest();
        float minZ = std::numeric_limits<float>::max();
        float maxZ = std::numeric_limits<float>::lowest();
        
        for (const auto& vertex : meshData->vertices) {
            const auto& pos = vertex.getPosition();
            minX = std::min(minX, pos.x);
            maxX = std::max(maxX, pos.x);
            minZ = std::min(minZ, pos.z);
            maxZ = std::max(maxZ, pos.z);
        }
        
        float width = maxX - minX;
        float depth = maxZ - minZ;
        
        REQUIRE(std::abs(width - 5.0f) < 0.1f);
        REQUIRE(std::abs(depth - 3.0f) < 0.1f);
    }
    
    SECTION("Primitives without parameters use defaults") {
        // Test that primitives work without explicit parameters
        auto triangle = loader.createPrimitive(PrimitiveType::TRIANGLE);
        auto quad = loader.createPrimitive(PrimitiveType::QUAD);
        auto cube = loader.createPrimitive(PrimitiveType::CUBE);
        
        REQUIRE(triangle != nullptr);
        REQUIRE(quad != nullptr);
        REQUIRE(cube != nullptr);
        
        REQUIRE(triangle->vertices.size() == 3);
        REQUIRE(quad->vertices.size() == 4);
        REQUIRE(cube->vertices.size() == 36);
    }
}

TEST_CASE("MeshLoader error handling", "[assets][mesh_loader][errors]") {
    MockVFS mockVFS;
    MeshLoader loader(mockVFS);
    
    SECTION("Invalid primitive type throws exception") {
        // Cast to invalid enum value
        PrimitiveType invalidType = static_cast<PrimitiveType>(999);
        
        REQUIRE_THROWS_AS(loader.createPrimitive(invalidType), std::runtime_error);
    }
}

TEST_CASE("MeshLoader mesh data validation", "[assets][mesh_loader][validation]") {
    MockVFS mockVFS;
    MeshLoader loader(mockVFS);
    
    SECTION("All primitives have valid mesh data") {
        std::vector<PrimitiveType> primitiveTypes = {
            PrimitiveType::TRIANGLE,
            PrimitiveType::QUAD,
            PrimitiveType::CUBE,
            PrimitiveType::SPHERE,
            PrimitiveType::PLANE
        };
        
        for (auto type : primitiveTypes) {
            auto meshData = loader.createPrimitive(type);
            
            REQUIRE(meshData != nullptr);
            REQUIRE(meshData->vertices.size() > 0);
            REQUIRE(meshData->indices.size() > 0);
            
            // Verify all vertices have finite values
            for (const auto& vertex : meshData->vertices) {
                const auto& pos = vertex.getPosition();
                const auto& uv = vertex.getUV();
                const auto& normal = vertex.getNormal();
                const auto& tangent = vertex.getTangent();
                const auto& bitangent = vertex.getBitangent();
                
                REQUIRE(std::isfinite(pos.x));
                REQUIRE(std::isfinite(pos.y));
                REQUIRE(std::isfinite(pos.z));
                REQUIRE(std::isfinite(uv.x));
                REQUIRE(std::isfinite(uv.y));
                REQUIRE(std::isfinite(normal.x));
                REQUIRE(std::isfinite(normal.y));
                REQUIRE(std::isfinite(normal.z));
                // Tangents and bitangents may be NaN for degenerate cases
                // Just verify they exist and don't crash when accessed
                (void)tangent.x; (void)tangent.y; (void)tangent.z;
                (void)bitangent.x; (void)bitangent.y; (void)bitangent.z;
            }
            
            // Verify all indices are valid
            for (unsigned int index : meshData->indices) {
                REQUIRE(index < meshData->vertices.size());
            }
            
            // Verify indices form complete triangles
            REQUIRE(meshData->indices.size() % 3 == 0);
        }
    }
    
    SECTION("Tangents and bitangents are calculated") {
        auto meshData = loader.createPrimitive(PrimitiveType::CUBE);
        
        REQUIRE(meshData != nullptr);
        
        // Check that tangents and bitangents are not zero vectors
        bool hasTangents = false;
        bool hasBitangents = false;
        
        for (const auto& vertex : meshData->vertices) {
            const auto& tangent = vertex.getTangent();
            const auto& bitangent = vertex.getBitangent();
            
            if (glm::length(tangent) > 0.1f) hasTangents = true;
            if (glm::length(bitangent) > 0.1f) hasBitangents = true;
        }
        
        REQUIRE(hasTangents);
        REQUIRE(hasBitangents);
    }
}

TEST_CASE("MeshLoader memory management", "[assets][mesh_loader][memory]") {
    MockVFS mockVFS;
    MeshLoader loader(mockVFS);

    SECTION("Multiple mesh creation") {
        std::vector<std::unique_ptr<MeshData>> meshes;

        // Create multiple meshes of different types
        for (int i = 0; i < 5; ++i) {
            meshes.push_back(loader.createPrimitive(PrimitiveType::CUBE));
            meshes.push_back(loader.createPrimitive(PrimitiveType::SPHERE));
            meshes.push_back(loader.createPrimitive(PrimitiveType::PLANE));
        }

        REQUIRE(meshes.size() == 15);

        // Verify all meshes are valid
        for (const auto& mesh : meshes) {
            REQUIRE(mesh != nullptr);
            REQUIRE(mesh->vertices.size() > 0);
            REQUIRE(mesh->indices.size() > 0);
        }

        // Clear all meshes - should not cause memory issues
        meshes.clear();

        // Test passes if no memory errors occur
        REQUIRE(true);
    }

    SECTION("Mesh data independence") {
        auto mesh1 = loader.createPrimitive(PrimitiveType::CUBE);
        auto mesh2 = loader.createPrimitive(PrimitiveType::CUBE);

        REQUIRE(mesh1 != nullptr);
        REQUIRE(mesh2 != nullptr);
        REQUIRE(mesh1.get() != mesh2.get());

        // Modify one mesh's data (directly modify the data array since Vertex doesn't have setPosition)
        if (!mesh1->vertices.empty()) {
            auto originalPos = mesh1->vertices[0].getPosition();
            mesh1->vertices[0].data[0] = 999.0f; // x position
            mesh1->vertices[0].data[1] = 999.0f; // y position
            mesh1->vertices[0].data[2] = 999.0f; // z position

            // Verify the other mesh is unaffected
            REQUIRE(mesh2->vertices[0].getPosition() == originalPos);
        }
    }
}

TEST_CASE("MeshLoader parameter edge cases", "[assets][mesh_loader][edge_cases]") {
    MockVFS mockVFS;
    MeshLoader loader(mockVFS);

    SECTION("Sphere with minimal parameters") {
        PrimitiveParams params;
        params.set<int>("lat-bands", 3);  // Minimum for a recognizable sphere
        params.set<int>("long-bands", 3);

        auto meshData = loader.createPrimitive(PrimitiveType::SPHERE, params);

        REQUIRE(meshData != nullptr);
        REQUIRE(meshData->vertices.size() > 0);
        REQUIRE(meshData->indices.size() > 0);
    }

    SECTION("Sphere with large parameters") {
        PrimitiveParams params;
        params.set<int>("lat-bands", 64);
        params.set<int>("long-bands", 64);

        auto meshData = loader.createPrimitive(PrimitiveType::SPHERE, params);

        REQUIRE(meshData != nullptr);
        REQUIRE(meshData->vertices.size() > 0);
        REQUIRE(meshData->indices.size() > 0);

        // Should have significantly more vertices than default
        auto defaultMesh = loader.createPrimitive(PrimitiveType::SPHERE);
        REQUIRE(meshData->vertices.size() > defaultMesh->vertices.size());
    }

    SECTION("Plane with minimal segments") {
        PrimitiveParams params;
        params.set<float>("width", 1.0f);
        params.set<float>("depth", 1.0f);
        params.set<unsigned int>("width-segments", 1);
        params.set<unsigned int>("depth-segments", 1);

        auto meshData = loader.createPrimitive(PrimitiveType::PLANE, params);

        REQUIRE(meshData != nullptr);
        REQUIRE(meshData->vertices.size() >= 4); // At least 4 vertices for a plane
        REQUIRE(meshData->indices.size() >= 6);  // At least 2 triangles
    }

    SECTION("Plane with large dimensions") {
        PrimitiveParams params;
        params.set<float>("width", 100.0f);
        params.set<float>("depth", 50.0f);
        params.set<unsigned int>("width-segments", 20);
        params.set<unsigned int>("depth-segments", 10);

        auto meshData = loader.createPrimitive(PrimitiveType::PLANE, params);

        REQUIRE(meshData != nullptr);
        REQUIRE(meshData->vertices.size() > 0);
        REQUIRE(meshData->indices.size() > 0);

        // Verify large plane dimensions
        float minX = std::numeric_limits<float>::max();
        float maxX = std::numeric_limits<float>::lowest();
        float minZ = std::numeric_limits<float>::max();
        float maxZ = std::numeric_limits<float>::lowest();

        for (const auto& vertex : meshData->vertices) {
            const auto& pos = vertex.getPosition();
            minX = std::min(minX, pos.x);
            maxX = std::max(maxX, pos.x);
            minZ = std::min(minZ, pos.z);
            maxZ = std::max(maxZ, pos.z);
        }

        float width = maxX - minX;
        float depth = maxZ - minZ;

        REQUIRE(width >= 99.0f); // Allow some tolerance
        REQUIRE(depth >= 49.0f);
    }

    SECTION("Parameters with wrong types are ignored") {
        PrimitiveParams params;
        params.set<std::string>("lat-bands", "invalid"); // Wrong type
        params.set<bool>("long-bands", true);            // Wrong type

        // Should use default parameters and not crash
        auto meshData = loader.createPrimitive(PrimitiveType::SPHERE, params);

        REQUIRE(meshData != nullptr);
        REQUIRE(meshData->vertices.size() > 0);

        // Should be same as default sphere
        auto defaultMesh = loader.createPrimitive(PrimitiveType::SPHERE);
        REQUIRE(meshData->vertices.size() == defaultMesh->vertices.size());
    }
}

TEST_CASE("MeshLoader lifecycle", "[assets][mesh_loader][lifecycle]") {
    MockVFS mockVFS;

    SECTION("Loader lifecycle with mesh data") {
        std::unique_ptr<MeshData> meshData;

        {
            MeshLoader loader(mockVFS);
            meshData = loader.createPrimitive(PrimitiveType::CUBE);

            // loader goes out of scope here
        }

        // Test that MeshData survives loader destruction
        REQUIRE(meshData != nullptr);
        REQUIRE(meshData->vertices.size() > 0);
        REQUIRE(meshData->indices.size() > 0);

        // Verify mesh data is still valid
        for (const auto& vertex : meshData->vertices) {
            const auto& pos = vertex.getPosition();
            REQUIRE(std::isfinite(pos.x));
            REQUIRE(std::isfinite(pos.y));
            REQUIRE(std::isfinite(pos.z));
        }
    }

    SECTION("Multiple loaders with same VFS") {
        MeshLoader loader1(mockVFS);
        MeshLoader loader2(mockVFS);

        auto mesh1 = loader1.createPrimitive(PrimitiveType::TRIANGLE);
        auto mesh2 = loader2.createPrimitive(PrimitiveType::QUAD);

        REQUIRE(mesh1 != nullptr);
        REQUIRE(mesh2 != nullptr);
        REQUIRE(mesh1.get() != mesh2.get());

        // Different primitive types should have different vertex counts
        REQUIRE(mesh1->vertices.size() != mesh2->vertices.size()); // Triangle=3, Quad=4
    }
}
