#include <catch2/catch_test_macros.hpp>

#include "assets/loaders/font_loader.hpp"
#include "assets/asset_data_types.hpp"
#include "../../mocks/test_mocks.hpp"

#include <memory>
#include <string>
#include <vector>

using namespace IronFrost;

// Helper function to create minimal TTF font data
// This is a very basic TTF header - not a complete font, but enough for testing error handling
std::vector<unsigned char> createMockTTFData() {
    return {
        // TTF/OTF signature
        0x00, 0x01, 0x00, 0x00, // sfnt version
        0x00, 0x04,             // numTables
        0x00, 0x40,             // searchRange
        0x00, 0x02,             // entrySelector
        0x00, 0x00,             // rangeShift
        
        // Table directory entries (minimal)
        'c', 'm', 'a', 'p',     // cmap table
        0x00, 0x00, 0x00, 0x00, // checksum
        0x00, 0x00, 0x00, 0x6C, // offset
        0x00, 0x00, 0x00, 0x20, // length
        
        'h', 'e', 'a', 'd',     // head table
        0x00, 0x00, 0x00, 0x00, // checksum
        0x00, 0x00, 0x00, 0x8C, // offset
        0x00, 0x00, 0x00, 0x36, // length
        
        'h', 'h', 'e', 'a',     // hhea table
        0x00, 0x00, 0x00, 0x00, // checksum
        0x00, 0x00, 0x00, 0xC4, // offset
        0x00, 0x00, 0x00, 0x24, // length
        
        'h', 'm', 't', 'x',     // hmtx table
        0x00, 0x00, 0x00, 0x00, // checksum
        0x00, 0x00, 0x00, 0xE8, // offset
        0x00, 0x00, 0x00, 0x08, // length
        
        // Minimal table data (not complete, but enough for basic testing)
        // This would normally contain actual font data
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
    };
}

// Helper function to create invalid font data
std::vector<unsigned char> createInvalidFontData() {
    return {0x00, 0x01, 0x02, 0x03, 0x04, 0x05}; // Random bytes
}

TEST_CASE("FontLoader basic functionality", "[assets][font_loader][basic]") {
    MockVFS mockVFS;
    FontLoader loader(mockVFS);
    
    SECTION("Handle file not found") {
        mockVFS.setShouldReturnEmpty(true);
        
        auto fontData = loader.loadFont("nonexistent.ttf", 16);
        
        REQUIRE(fontData == nullptr);
    }
    
    SECTION("Handle empty file") {
        std::vector<unsigned char> emptyData;
        mockVFS.setMockData(emptyData);
        mockVFS.setExpectedPath("empty.ttf");
        
        auto fontData = loader.loadFont("empty.ttf", 16);
        
        REQUIRE(fontData == nullptr);
    }
    
    SECTION("Handle invalid font format") {
        std::vector<unsigned char> invalidData = createInvalidFontData();
        mockVFS.setMockData(invalidData);
        mockVFS.setExpectedPath("invalid.ttf");
        
        auto fontData = loader.loadFont("invalid.ttf", 16);
        
        REQUIRE(fontData == nullptr);
    }
    
    SECTION("Handle invalid font size") {
        std::vector<unsigned char> mockTTF = createMockTTFData();
        mockVFS.setMockData(mockTTF);
        mockVFS.setExpectedPath("test.ttf");
        
        auto fontData = loader.loadFont("test.ttf", 0);
        
        REQUIRE(fontData == nullptr);
    }
    
    SECTION("Test FontLoader interface with mock data") {
        std::vector<unsigned char> mockTTF = createMockTTFData();
        mockVFS.setMockData(mockTTF);
        mockVFS.setExpectedPath("test.ttf");
        
        auto fontData = loader.loadFont("test.ttf", 16);
        
        // The mock TTF data may not be complete enough for FreeType to parse
        // But we can test that the loader doesn't crash and handles the call properly
        if (fontData != nullptr) {
            REQUIRE(fontData->size == 16);
            REQUIRE(fontData->glyphs.size() == 128);
            REQUIRE(fontData->lineHeight > 0);
        }
        
        // Test passes if no crash occurs
        REQUIRE(true);
    }
}

TEST_CASE("FontLoader different font sizes", "[assets][font_loader][sizes]") {
    MockVFS mockVFS;
    FontLoader loader(mockVFS);
    
    SECTION("Test different font sizes") {
        std::vector<unsigned char> mockTTF = createMockTTFData();
        mockVFS.setMockData(mockTTF);
        
        std::vector<unsigned int> fontSizes = {8, 12, 16, 24, 32, 48, 64};
        
        for (unsigned int size : fontSizes) {
            std::string filename = "test_" + std::to_string(size) + ".ttf";
            mockVFS.setExpectedPath(filename);
            
            auto fontData = loader.loadFont(filename, size);
            
            // Test that different font sizes are handled consistently
            if (fontData != nullptr) {
                REQUIRE(fontData->size == size);
                REQUIRE(fontData->glyphs.size() == 128);
            }
        }
    }
}

TEST_CASE("FontLoader different file formats", "[assets][font_loader][formats]") {
    MockVFS mockVFS;
    FontLoader loader(mockVFS);
    
    SECTION("Test different font file extensions") {
        std::vector<unsigned char> mockTTF = createMockTTFData();
        mockVFS.setMockData(mockTTF);
        
        // Test different font file extensions
        std::vector<std::string> extensions = {"test.ttf", "test.otf", "test.woff"};
        
        for (const auto& filename : extensions) {
            mockVFS.setExpectedPath(filename);
            auto fontData = loader.loadFont(filename, 16);
            
            // Test that different extensions are handled consistently
            // Result depends on the data content, not the extension
            if (fontData != nullptr) {
                REQUIRE(fontData->size == 16);
                REQUIRE(fontData->glyphs.size() == 128);
            }
        }
    }
}

TEST_CASE("FontLoader memory management", "[assets][font_loader][memory]") {
    MockVFS mockVFS;
    FontLoader loader(mockVFS);
    
    SECTION("Test memory management interface") {
        std::vector<unsigned char> mockTTF = createMockTTFData();
        mockVFS.setMockData(mockTTF);
        mockVFS.setExpectedPath("memory_test.ttf");
        
        // Test that FontData objects can be created and destroyed safely
        auto fontData = loader.loadFont("memory_test.ttf", 16);
        
        // Even if the font loading fails, we test that the interface works
        // and doesn't cause memory issues
        if (fontData != nullptr) {
            REQUIRE(fontData->size == 16);
            REQUIRE(fontData->glyphs.size() == 128);
            
            // Test that glyph data is accessible
            for (size_t i = 32; i < 128; ++i) {
                const auto& glyph = fontData->glyphs[i];
                // Basic validation that glyph structure is initialized
                REQUIRE(glyph.size.x >= 0);
                REQUIRE(glyph.size.y >= 0);
                REQUIRE(glyph.advance >= 0);
            }
        }
        
        // Test passes if no memory errors occur
        REQUIRE(true);
    }
    
    SECTION("Multiple font loading interface") {
        std::vector<unsigned char> mockTTF = createMockTTFData();
        mockVFS.setMockData(mockTTF);
        
        std::vector<std::unique_ptr<FontData>> fonts;
        
        // Test loading multiple fonts through the interface
        for (int i = 0; i < 3; ++i) {
            std::string filename = "test" + std::to_string(i) + ".ttf";
            mockVFS.setExpectedPath(filename);
            
            auto fontData = loader.loadFont(filename, 16);
            
            // Store the result regardless of success/failure
            fonts.push_back(std::move(fontData));
        }
        
        // Test that we can handle multiple load attempts
        REQUIRE(fonts.size() == 3);
        
        // Clear all fonts - should not cause memory issues
        fonts.clear();
        
        // Test passes if no crashes occur
        REQUIRE(true);
    }
}

TEST_CASE("FontLoader edge cases", "[assets][font_loader][edge_cases]") {
    MockVFS mockVFS;
    FontLoader loader(mockVFS);

    SECTION("Very large font size") {
        std::vector<unsigned char> mockTTF = createMockTTFData();
        mockVFS.setMockData(mockTTF);
        mockVFS.setExpectedPath("large_size.ttf");

        // Test with very large font size
        auto fontData = loader.loadFont("large_size.ttf", 1000);

        // Should handle large sizes gracefully
        if (fontData != nullptr) {
            REQUIRE(fontData->size == 1000);
        }

        // Test passes if no crash occurs
        REQUIRE(true);
    }

    SECTION("Very small font size") {
        std::vector<unsigned char> mockTTF = createMockTTFData();
        mockVFS.setMockData(mockTTF);
        mockVFS.setExpectedPath("small_size.ttf");

        // Test with very small font size
        auto fontData = loader.loadFont("small_size.ttf", 1);

        // Should handle small sizes gracefully
        if (fontData != nullptr) {
            REQUIRE(fontData->size == 1);
        }

        // Test passes if no crash occurs
        REQUIRE(true);
    }

    SECTION("Path with special characters") {
        std::vector<std::string> specialPaths = {
            "font with spaces.ttf",
            "font-with-dashes.ttf",
            "font_with_underscores.ttf",
            "font.with.dots.ttf",
            "шрифт.ttf", // Unicode characters
            "font@#$%.ttf" // Special symbols
        };

        std::vector<unsigned char> mockTTF = createMockTTFData();
        mockVFS.setMockData(mockTTF);

        for (const auto& path : specialPaths) {
            mockVFS.setExpectedPath(path);
            auto fontData = loader.loadFont(path, 16);

            // Test that the loader handles special characters without crashing
            if (fontData != nullptr) {
                REQUIRE(fontData->size == 16);
            }
        }
    }

    SECTION("Very large file path") {
        std::string longPath(1000, 'a');
        longPath += ".ttf";

        mockVFS.setShouldReturnEmpty(true);
        mockVFS.setExpectedPath(longPath);

        auto fontData = loader.loadFont(longPath, 16);
        REQUIRE(fontData == nullptr);
    }

    SECTION("Corrupted font data handling") {
        std::vector<unsigned char> corruptedData = createMockTTFData();

        // Corrupt some bytes to test error handling
        if (corruptedData.size() > 20) {
            corruptedData[10] = 0xFF;
            corruptedData[11] = 0xFF;
            corruptedData[12] = 0xFF;
        }

        mockVFS.setMockData(corruptedData);
        mockVFS.setExpectedPath("corrupted.ttf");

        auto fontData = loader.loadFont("corrupted.ttf", 16);

        // FreeType should handle corruption gracefully
        // The result may be nullptr or a valid font depending on the corruption
        // The important thing is that it doesn't crash
        if (fontData != nullptr) {
            REQUIRE(fontData->size == 16);
        }

        // Test passes if no crash occurs
        REQUIRE(true);
    }
}

TEST_CASE("FontLoader VFS integration", "[assets][font_loader][vfs]") {
    MockVFS mockVFS;
    FontLoader loader(mockVFS);

    SECTION("VFS path handling") {
        std::vector<unsigned char> mockTTF = createMockTTFData();
        mockVFS.setMockData(mockTTF);

        // Test various path formats
        std::vector<std::string> paths = {
            "font.ttf",
            "./font.ttf",
            "fonts/font.ttf",
            "assets/fonts/font.ttf",
            "/absolute/path/font.ttf"
        };

        for (const auto& path : paths) {
            mockVFS.setExpectedPath(path);
            auto fontData = loader.loadFont(path, 16);

            // Test that different path formats are handled without crashing
            if (fontData != nullptr) {
                REQUIRE(fontData->size == 16);
            }
        }
    }

    SECTION("VFS error handling") {
        // Test when VFS returns empty data
        mockVFS.setShouldReturnEmpty(true);

        auto fontData = loader.loadFont("missing.ttf", 16);
        REQUIRE(fontData == nullptr);

        // Reset VFS to normal operation
        mockVFS.setShouldReturnEmpty(false);
        std::vector<unsigned char> mockTTF = createMockTTFData();
        mockVFS.setMockData(mockTTF);
        mockVFS.setExpectedPath("valid.ttf");

        fontData = loader.loadFont("valid.ttf", 16);
        // Result depends on data quality, but should not crash
        if (fontData != nullptr) {
            REQUIRE(fontData->size == 16);
        }
    }
}

TEST_CASE("FontLoader constructor and lifecycle", "[assets][font_loader][lifecycle]") {
    MockVFS mockVFS;

    SECTION("Constructor with VFS reference") {
        FontLoader loader(mockVFS);

        // Verify loader can be used immediately after construction
        std::vector<unsigned char> mockTTF = createMockTTFData();
        mockVFS.setMockData(mockTTF);
        mockVFS.setExpectedPath("constructor_test.ttf");

        auto fontData = loader.loadFont("constructor_test.ttf", 16);

        // Test that constructor works and loader is usable
        if (fontData != nullptr) {
            REQUIRE(fontData->size == 16);
        }

        // Test passes if constructor and basic usage work
        REQUIRE(true);
    }

    SECTION("Multiple loaders with same VFS") {
        FontLoader loader1(mockVFS);
        FontLoader loader2(mockVFS);

        std::vector<unsigned char> mockTTF = createMockTTFData();
        mockVFS.setMockData(mockTTF);

        // Test that multiple loaders can use the same VFS
        mockVFS.setExpectedPath("loader1.ttf");
        auto font1 = loader1.loadFont("loader1.ttf", 16);

        mockVFS.setExpectedPath("loader2.ttf");
        auto font2 = loader2.loadFont("loader2.ttf", 24);

        // Test that both loaders work independently
        if (font1 != nullptr && font2 != nullptr) {
            // Fonts should be independent instances
            REQUIRE(font1.get() != font2.get());
            REQUIRE(font1->size == 16);
            REQUIRE(font2->size == 24);
        }

        // Test passes if multiple loaders can coexist
        REQUIRE(true);
    }

    SECTION("Loader lifecycle with VFS") {
        std::vector<unsigned char> mockTTF = createMockTTFData();
        mockVFS.setMockData(mockTTF);

        std::unique_ptr<FontData> fontData;

        {
            FontLoader loader(mockVFS);
            mockVFS.setExpectedPath("lifecycle.ttf");
            fontData = loader.loadFont("lifecycle.ttf", 16);

            // loader goes out of scope here
        }

        // Test that FontData survives loader destruction
        if (fontData != nullptr) {
            REQUIRE(fontData->size == 16);
            REQUIRE(fontData->glyphs.size() == 128);
        }

        // Test passes if lifecycle management works correctly
        REQUIRE(true);
    }
}
