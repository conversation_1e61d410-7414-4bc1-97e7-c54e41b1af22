#include <catch2/catch_test_macros.hpp>

// C++ standard library
#include <cmath>

// Local includes
#include "assets/loaders/audio_loader.hpp"
#include "../../mocks/test_mocks.hpp"

using namespace IronFrost;
using namespace IronFrost::Mocks;

TEST_CASE("AudioLoader functionality", "[assets][audio_loader]") {
    MockVFS mockVFS;
    AudioLoader audioLoader(mockVFS);

    SECTION("Load non-existent file throws exception") {
        REQUIRE_THROWS_AS(audioLoader.loadAudioFile("nonexistent.wav"), std::runtime_error);
    }

    SECTION("AudioData structure validation") {
        // Test AudioData methods without loading actual files
        AudioData audioData;
        audioData.samples = {0.0f, 0.5f, -0.5f, 1.0f, -1.0f, 0.25f};  // 6 samples
        audioData.channels = 2;  // Stereo
        audioData.sampleRate = 44100;
        audioData.duration = 3.0f / 44100.0f;  // 3 frames at 44100 Hz

        REQUIRE(audioData.getFrameCount() == 3);  // 6 samples / 2 channels = 3 frames

        // Test int16 conversion (with tolerance for rounding)
        auto int16Samples = audioData.getInt16Samples();
        REQUIRE(int16Samples.size() == 6);
        REQUIRE(int16Samples[0] == 0);                    // 0.0f -> 0
        REQUIRE(std::abs(int16Samples[1] - 16383) <= 1);  // 0.5f -> ~16383
        REQUIRE(std::abs(int16Samples[2] + 16383) <= 1);  // -0.5f -> ~-16383
        REQUIRE(int16Samples[3] == 32767);                // 1.0f -> 32767 (max)
        REQUIRE(int16Samples[4] == -32767);               // -1.0f -> -32767 (min, clamped)
        REQUIRE(std::abs(int16Samples[5] - 8191) <= 1);   // 0.25f -> ~8191
    }

    SECTION("AudioData clamping test") {
        AudioData audioData;
        audioData.samples = {2.0f, -2.0f};  // Values outside [-1.0, 1.0] range
        audioData.channels = 1;

        auto int16Samples = audioData.getInt16Samples();
        REQUIRE(int16Samples.size() == 2);
        REQUIRE(int16Samples[0] == 32767);   // Clamped to max
        REQUIRE(int16Samples[1] == -32767);  // Clamped to min
    }
}

TEST_CASE("AudioData integration with asset system", "[assets][audio_data]") {
    SECTION("AudioData fits into asset data types") {
        // Test that AudioData can be used like other asset data types
        auto audioData = std::make_unique<AudioData>();
        audioData->sampleRate = 44100;
        audioData->channels = 2;
        audioData->duration = 1.0f;
        audioData->samples.resize(88200);  // 1 second of stereo audio

        REQUIRE(audioData->sampleRate == 44100);
        REQUIRE(audioData->channels == 2);
        REQUIRE(audioData->duration == 1.0f);
        REQUIRE(audioData->getFrameCount() == 44100);  // 88200 samples / 2 channels
    }
}
