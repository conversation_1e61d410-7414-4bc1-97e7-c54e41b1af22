#define GLM_ENABLE_EXPERIMENTAL

#include <catch2/catch_test_macros.hpp>
#include <catch2/catch_approx.hpp>

// Local includes
#include "scene/camera/camera.hpp"
#include "scene/camera/frustum.hpp"
#include "../../test_utils.hpp"

#include <glm/glm.hpp>
#include <glm/gtc/matrix_transform.hpp>

using namespace IronFrost;
using Catch::Approx;

TEST_CASE("Frustum construction", "[scene][camera][frustum][construction]") {
    SECTION("Standard perspective frustum") {
        // Create a standard perspective projection
        float fov = glm::radians(60.0f);
        float aspect = 16.0f / 9.0f;
        float near = 0.1f;
        float far = 100.0f;
        
        glm::mat4 projection = glm::perspective(fov, aspect, near, far);
        glm::mat4 view = glm::lookAt(
            glm::vec3(0.0f, 0.0f, 0.0f),  // eye
            glm::vec3(0.0f, 0.0f, -1.0f), // center
            glm::vec3(0.0f, 1.0f, 0.0f)   // up
        );
        glm::mat4 viewProj = projection * view;
        
        REQUIRE_NOTHROW(Frustum(viewProj));
    }
    
    SECTION("Orthographic frustum") {
        // Create an orthographic projection
        float left = -10.0f, right = 10.0f;
        float bottom = -10.0f, top = 10.0f;
        float near = 0.1f, far = 100.0f;
        
        glm::mat4 projection = glm::ortho(left, right, bottom, top, near, far);
        glm::mat4 view = glm::mat4(1.0f); // Identity view matrix
        glm::mat4 viewProj = projection * view;
        
        REQUIRE_NOTHROW(Frustum(viewProj));
    }
    
    SECTION("Identity matrix frustum") {
        glm::mat4 identity = glm::mat4(1.0f);
        REQUIRE_NOTHROW(Frustum(identity));
    }
}

TEST_CASE("Frustum AABB intersection - basic cases", "[scene][camera][frustum][intersection]") {
    // Create a very simple test case - just test that large AABBs intersect
    float fov = glm::radians(90.0f);
    float aspect = 1.0f;
    float near = 0.1f;
    float far = 100.0f;

    glm::mat4 projection = glm::perspective(fov, aspect, near, far);
    glm::mat4 view = glm::mat4(1.0f); // Identity view matrix
    glm::mat4 viewProj = projection * view;
    Frustum frustum(viewProj);

    SECTION("Very large AABB should always intersect") {
        // Huge AABB that definitely encompasses the frustum
        CollisionMath::AABB aabb(
            glm::vec3(-1000.0f, -1000.0f, -1000.0f),
            glm::vec3(1000.0f, 1000.0f, 1000.0f)
        );

        REQUIRE(frustum.intersects(aabb));
    }

    SECTION("AABB clearly outside should not intersect") {
        // AABB very far away in positive coordinates (clearly outside)
        CollisionMath::AABB aabb(
            glm::vec3(10000.0f, 10000.0f, 10000.0f),
            glm::vec3(10001.0f, 10001.0f, 10001.0f)
        );

        REQUIRE_FALSE(frustum.intersects(aabb));
    }

    SECTION("AABB at origin should intersect") {
        // Small AABB at the origin should be visible
        CollisionMath::AABB aabb(
            glm::vec3(-1.0f, -1.0f, -1.0f),
            glm::vec3(1.0f, 1.0f, 1.0f)
        );

        REQUIRE(frustum.intersects(aabb));
    }
}

TEST_CASE("Frustum AABB intersection - simple cases", "[scene][camera][frustum][intersection]") {
    // Create a very simple test case
    float fov = glm::radians(90.0f);
    float aspect = 1.0f;
    float near = 0.1f;
    float far = 100.0f;

    // Identity view matrix (camera at origin looking down -Z)
    glm::mat4 projection = glm::perspective(fov, aspect, near, far);
    glm::mat4 view = glm::mat4(1.0f);
    glm::mat4 viewProj = projection * view;
    Frustum frustum(viewProj);

    SECTION("Large AABB encompassing everything should intersect") {
        // Very large AABB that definitely encompasses the frustum
        CollisionMath::AABB aabb(
            glm::vec3(-1000.0f, -1000.0f, -1000.0f),
            glm::vec3(1000.0f, 1000.0f, 1000.0f)
        );

        REQUIRE(frustum.intersects(aabb));
    }
}

TEST_CASE("Frustum AABB intersection - edge cases", "[scene][camera][frustum][intersection][edge_cases]") {
    // Simple test with identity matrices
    glm::mat4 projection = glm::perspective(glm::radians(90.0f), 1.0f, 0.1f, 100.0f);
    glm::mat4 view = glm::mat4(1.0f);
    glm::mat4 viewProj = projection * view;
    Frustum frustum(viewProj);

    SECTION("Very large AABB encompassing frustum") {
        // Huge AABB that contains the entire frustum
        CollisionMath::AABB aabb(
            glm::vec3(-100.0f, -100.0f, -100.0f),
            glm::vec3(100.0f, 100.0f, 100.0f)
        );

        REQUIRE(frustum.intersects(aabb));
    }

    SECTION("Zero-volume AABB outside frustum") {
        // Point AABB clearly outside the frustum
        glm::vec3 point(1000.0f, 1000.0f, 1000.0f);
        CollisionMath::AABB aabb(point, point);

        REQUIRE_FALSE(frustum.intersects(aabb));
    }
}

TEST_CASE("Frustum basic functionality test", "[scene][camera][frustum][basic]") {
    // Very simple test to verify the frustum works at all
    glm::mat4 projection = glm::perspective(glm::radians(90.0f), 1.0f, 1.0f, 100.0f);
    glm::mat4 view = glm::mat4(1.0f); // Identity view matrix
    glm::mat4 viewProj = projection * view;
    Frustum frustum(viewProj);

    SECTION("Frustum construction should not crash") {
        // Just verify we can create a frustum without issues
        REQUIRE_NOTHROW(Frustum(viewProj));
    }

    SECTION("Intersection test should not crash") {
        CollisionMath::AABB aabb(
            glm::vec3(-1.0f, -1.0f, -5.0f),
            glm::vec3(1.0f, 1.0f, -3.0f)
        );

        // Just verify the intersection test doesn't crash
        REQUIRE_NOTHROW(frustum.intersects(aabb));
    }
}

TEST_CASE("Orthographic frustum basic test", "[scene][camera][frustum][orthographic]") {
    SECTION("Simple orthographic frustum") {
        float left = -10.0f, right = 10.0f;
        float bottom = -10.0f, top = 10.0f;
        float near = 1.0f, far = 100.0f;

        glm::mat4 projection = glm::ortho(left, right, bottom, top, near, far);
        glm::mat4 view = glm::mat4(1.0f); // Identity view
        glm::mat4 viewProj = projection * view;

        REQUIRE_NOTHROW(Frustum(viewProj));

        Frustum frustum(viewProj);

        // Large AABB should intersect
        CollisionMath::AABB aabbLarge(
            glm::vec3(-100.0f, -100.0f, -100.0f),
            glm::vec3(100.0f, 100.0f, 100.0f)
        );
        REQUIRE(frustum.intersects(aabbLarge));

        // Small AABB at origin should intersect
        CollisionMath::AABB aabbSmall(
            glm::vec3(-1.0f, -1.0f, -5.0f),
            glm::vec3(1.0f, 1.0f, -3.0f)
        );
        REQUIRE(frustum.intersects(aabbSmall));
    }
}

TEST_CASE("Frustum diagnostic tests", "[scene][camera][frustum][diagnostic]") {
    SECTION("Known good case - camera looking down -Z axis") {
        // Camera at (0,0,0) looking down -Z axis
        // Near plane at z=-1, far plane at z=-10
        // With 90 degree FOV, at z=-5 the frustum should extend from -5 to +5 in X and Y

        float fov = glm::radians(90.0f);
        float aspect = 1.0f;
        float near = 1.0f;
        float far = 10.0f;

        glm::mat4 projection = glm::perspective(fov, aspect, near, far);
        glm::mat4 view = glm::mat4(1.0f); // Identity - camera at origin looking down -Z
        glm::mat4 viewProj = projection * view;
        Frustum frustum(viewProj);

        // AABB clearly inside the frustum
        CollisionMath::AABB aabbInside(
            glm::vec3(-1.0f, -1.0f, -3.0f),
            glm::vec3(1.0f, 1.0f, -2.0f)
        );
        REQUIRE(frustum.intersects(aabbInside));

        // AABB clearly behind the camera (positive Z)
        CollisionMath::AABB aabbBehind(
            glm::vec3(-1.0f, -1.0f, 1.0f),
            glm::vec3(1.0f, 1.0f, 2.0f)
        );
        REQUIRE_FALSE(frustum.intersects(aabbBehind));

        // AABB clearly beyond far plane
        CollisionMath::AABB aabbTooFar(
            glm::vec3(-1.0f, -1.0f, -15.0f),
            glm::vec3(1.0f, 1.0f, -12.0f)
        );
        REQUIRE_FALSE(frustum.intersects(aabbTooFar));
    }

    SECTION("Test with translated camera") {
        // Camera at (0,0,5) looking at origin
        float fov = glm::radians(60.0f);
        float aspect = 1.0f;
        float near = 1.0f;
        float far = 10.0f;

        glm::mat4 projection = glm::perspective(fov, aspect, near, far);
        glm::mat4 view = glm::lookAt(
            glm::vec3(0.0f, 0.0f, 5.0f),  // Camera position
            glm::vec3(0.0f, 0.0f, 0.0f),  // Look at origin
            glm::vec3(0.0f, 1.0f, 0.0f)   // Up vector
        );
        glm::mat4 viewProj = projection * view;
        Frustum frustum(viewProj);

        // AABB at origin should be visible
        CollisionMath::AABB aabbAtOrigin(
            glm::vec3(-0.5f, -0.5f, -0.5f),
            glm::vec3(0.5f, 0.5f, 0.5f)
        );
        REQUIRE(frustum.intersects(aabbAtOrigin));

        // AABB behind camera should not be visible
        CollisionMath::AABB aabbBehindCamera(
            glm::vec3(-1.0f, -1.0f, 6.0f),
            glm::vec3(1.0f, 1.0f, 8.0f)
        );
        REQUIRE_FALSE(frustum.intersects(aabbBehindCamera));
    }
}

TEST_CASE("Frustum comprehensive validation", "[scene][camera][frustum][validation]") {
    SECTION("Perspective frustum culling behavior") {
        // Standard perspective camera
        float fov = glm::radians(60.0f);
        float aspect = 16.0f / 9.0f;
        float near = 0.1f;
        float far = 100.0f;

        glm::mat4 projection = glm::perspective(fov, aspect, near, far);
        glm::mat4 view = glm::mat4(1.0f); // Camera at origin looking down -Z
        glm::mat4 viewProj = projection * view;
        Frustum frustum(viewProj);

        // Object clearly in front of camera should be visible
        CollisionMath::AABB inFront(
            glm::vec3(-1.0f, -1.0f, -5.0f),
            glm::vec3(1.0f, 1.0f, -3.0f)
        );
        REQUIRE(frustum.intersects(inFront));

        // Object behind camera should not be visible
        CollisionMath::AABB behind(
            glm::vec3(-1.0f, -1.0f, 1.0f),
            glm::vec3(1.0f, 1.0f, 3.0f)
        );
        REQUIRE_FALSE(frustum.intersects(behind));

        // Object beyond far plane should not be visible
        CollisionMath::AABB tooFar(
            glm::vec3(-1.0f, -1.0f, -150.0f),
            glm::vec3(1.0f, 1.0f, -120.0f)
        );
        REQUIRE_FALSE(frustum.intersects(tooFar));

        // Object too far to the side should not be visible
        CollisionMath::AABB toSide(
            glm::vec3(50.0f, 0.0f, -10.0f),
            glm::vec3(52.0f, 2.0f, -8.0f)
        );
        REQUIRE_FALSE(frustum.intersects(toSide));
    }

    SECTION("Orthographic frustum culling behavior") {
        // Orthographic projection
        float left = -10.0f, right = 10.0f;
        float bottom = -10.0f, top = 10.0f;
        float near = 1.0f, far = 50.0f;

        glm::mat4 projection = glm::ortho(left, right, bottom, top, near, far);
        glm::mat4 view = glm::mat4(1.0f);
        glm::mat4 viewProj = projection * view;
        Frustum frustum(viewProj);

        // Object within orthographic bounds should be visible
        CollisionMath::AABB inside(
            glm::vec3(-5.0f, -5.0f, -25.0f),
            glm::vec3(5.0f, 5.0f, -15.0f)
        );
        REQUIRE(frustum.intersects(inside));

        // Object outside orthographic bounds should not be visible
        CollisionMath::AABB outside(
            glm::vec3(15.0f, 0.0f, -25.0f),
            glm::vec3(20.0f, 5.0f, -15.0f)
        );
        REQUIRE_FALSE(frustum.intersects(outside));
    }
}

TEST_CASE("Camera-Frustum integration", "[scene][camera][frustum][integration]") {
    SECTION("Camera getFrustum method works correctly") {
        // Create a camera with known parameters
        glm::vec3 position(0.0f, 0.0f, 5.0f);
        glm::vec3 direction(0.0f, 0.0f, -1.0f);
        glm::vec3 up(0.0f, 1.0f, 0.0f);
        float fov = 60.0f;
        float aspect = 1.0f;
        float near = 1.0f;
        float far = 100.0f;

        Camera camera(position, direction, up, fov, aspect, near, far);
        Frustum frustum = camera.getFrustum();

        // AABB at origin should be visible (camera is looking at it)
        CollisionMath::AABB aabbAtOrigin(
            glm::vec3(-0.5f, -0.5f, -0.5f),
            glm::vec3(0.5f, 0.5f, 0.5f)
        );
        REQUIRE(frustum.intersects(aabbAtOrigin));

        // AABB behind camera should not be visible
        CollisionMath::AABB aabbBehind(
            glm::vec3(-1.0f, -1.0f, 6.0f),
            glm::vec3(1.0f, 1.0f, 8.0f)
        );
        REQUIRE_FALSE(frustum.intersects(aabbBehind));
    }

    SECTION("Moving camera updates frustum correctly") {
        Camera camera;

        // Get initial frustum
        Frustum initialFrustum = camera.getFrustum();

        // Move camera
        camera.setPosition(glm::vec3(10.0f, 0.0f, 0.0f));

        // Get new frustum
        Frustum movedFrustum = camera.getFrustum();

        // AABB at origin should behave differently for moved camera
        CollisionMath::AABB aabbAtOrigin(
            glm::vec3(-0.5f, -0.5f, -0.5f),
            glm::vec3(0.5f, 0.5f, 0.5f)
        );

        // Both frustums should work without crashing
        REQUIRE_NOTHROW(initialFrustum.intersects(aabbAtOrigin));
        REQUIRE_NOTHROW(movedFrustum.intersects(aabbAtOrigin));
    }
}

TEST_CASE("Frustum performance test", "[scene][camera][frustum][performance]") {
    // Simple performance test
    glm::mat4 projection = glm::perspective(glm::radians(90.0f), 1.0f, 0.1f, 100.0f);
    glm::mat4 view = glm::mat4(1.0f);
    glm::mat4 viewProj = projection * view;
    Frustum frustum(viewProj);

    SECTION("Multiple intersection tests should not crash") {
        // Test multiple intersections without crashing
        for (int i = 0; i < 100; ++i) {
            CollisionMath::AABB aabb(
                glm::vec3(-1.0f, -1.0f, -5.0f),
                glm::vec3(1.0f, 1.0f, -3.0f)
            );

            REQUIRE_NOTHROW(frustum.intersects(aabb));
        }
    }
}
