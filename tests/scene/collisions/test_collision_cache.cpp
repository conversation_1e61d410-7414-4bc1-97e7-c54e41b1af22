#include <catch2/catch_test_macros.hpp>

#include "scene/collisions/collision_cache.hpp"
#include "scene/scene_graph/scene_node.hpp"
#include "../../test_utils.hpp"

using namespace IronFrost;

TEST_CASE("CollisionCache basic functionality", "[scene][collision][cache]") {
    CollisionCache cache;
    
    SECTION("Initial state") {
        REQUIRE(cache.getCandidates().empty());
    }
    
    SECTION("Add single candidate") {
        SceneNode node;
        cache.addCandidate(&node);
        
        const auto& candidates = cache.getCandidates();
        REQUIRE(candidates.size() == 1);
        REQUIRE(candidates[0] == &node);
    }
    
    SECTION("Add multiple candidates") {
        SceneNode node1, node2, node3;
        
        cache.addCandidate(&node1);
        cache.addCandidate(&node2);
        cache.addCandidate(&node3);
        
        const auto& candidates = cache.getCandidates();
        REQUIRE(candidates.size() == 3);
        REQUIRE(candidates[0] == &node1);
        REQUIRE(candidates[1] == &node2);
        REQUIRE(candidates[2] == &node3);
    }
    
    SECTION("Clear candidates") {
        SceneNode node1, node2;
        
        cache.addCandidate(&node1);
        cache.addCandidate(&node2);
        REQUIRE(cache.getCandidates().size() == 2);
        
        cache.clearCandidates();
        REQUIRE(cache.getCandidates().empty());
    }
    
    SECTION("Add same candidate multiple times") {
        SceneNode node;
        
        cache.addCandidate(&node);
        cache.addCandidate(&node);
        cache.addCandidate(&node);
        
        const auto& candidates = cache.getCandidates();
        REQUIRE(candidates.size() == 3); // Should allow duplicates
        REQUIRE(candidates[0] == &node);
        REQUIRE(candidates[1] == &node);
        REQUIRE(candidates[2] == &node);
    }
    
    SECTION("Add null candidate") {
        cache.addCandidate(nullptr);
        
        const auto& candidates = cache.getCandidates();
        REQUIRE(candidates.size() == 1);
        REQUIRE(candidates[0] == nullptr);
    }
}

TEST_CASE("CollisionCache usage patterns", "[scene][collision][cache][usage]") {
    CollisionCache cache;
    
    SECTION("Typical frame cycle") {
        SceneNode node1, node2, node3;
        
        // Frame 1: Add some candidates
        cache.addCandidate(&node1);
        cache.addCandidate(&node2);
        REQUIRE(cache.getCandidates().size() == 2);
        
        // Clear for next frame
        cache.clearCandidates();
        REQUIRE(cache.getCandidates().empty());
        
        // Frame 2: Add different candidates
        cache.addCandidate(&node2);
        cache.addCandidate(&node3);
        REQUIRE(cache.getCandidates().size() == 2);
        REQUIRE(cache.getCandidates()[0] == &node2);
        REQUIRE(cache.getCandidates()[1] == &node3);
    }
    
    SECTION("Iterating over candidates") {
        SceneNode nodes[5];
        
        // Add multiple candidates
        for (int i = 0; i < 5; ++i) {
            cache.addCandidate(&nodes[i]);
        }
        
        // Verify we can iterate over them
        const auto& candidates = cache.getCandidates();
        REQUIRE(candidates.size() == 5);
        
        for (size_t i = 0; i < candidates.size(); ++i) {
            REQUIRE(candidates[i] == &nodes[i]);
        }
        
        // Test range-based for loop compatibility
        int count = 0;
        for (const auto* candidate : candidates) {
            REQUIRE(candidate == &nodes[count]);
            ++count;
        }
        REQUIRE(count == 5);
    }
}

TEST_CASE("CollisionCache memory safety", "[scene][collision][cache][memory]") {
    CollisionCache cache;
    
    SECTION("Candidates remain valid after multiple operations") {
        SceneNode node1, node2;
        
        cache.addCandidate(&node1);
        const auto& candidates1 = cache.getCandidates();
        SceneNode* storedPtr1 = candidates1[0];
        
        cache.addCandidate(&node2);
        const auto& candidates2 = cache.getCandidates();
        
        // First candidate should still be valid
        REQUIRE(candidates2[0] == storedPtr1);
        REQUIRE(candidates2[0] == &node1);
        REQUIRE(candidates2[1] == &node2);
    }
    
    SECTION("Reference remains valid after clear and re-add") {
        SceneNode node;
        
        cache.addCandidate(&node);
        cache.clearCandidates();
        cache.addCandidate(&node);
        
        const auto& candidates = cache.getCandidates();
        REQUIRE(candidates.size() == 1);
        REQUIRE(candidates[0] == &node);
    }
}
