#include <catch2/catch_test_macros.hpp>
#include <catch2/catch_approx.hpp>

#include "scene/collisions/collision_handler.hpp"
#include "scene/collisions/collision_cache.hpp"
#include "scene/collisions/collision_shapes/collision_sphere.hpp"
#include "scene/collisions/collision_shapes/collision_aabb.hpp"
#include "scene/collisions/collision_shapes/collision_plane.hpp"
#include "scene/scene_graph/scene_node.hpp"
#include "scene/camera/camera.hpp"
#include "../../test_utils.hpp"

using namespace IronFrost;
using namespace IronFrost::CollisionMath;
using Catch::Approx;

// Helper function to create a camera with specific position and collision radius
Camera createTestCamera(const glm::vec3& position = glm::vec3(0.0f), float radius = 1.0f) {
    Camera camera;
    camera.setPosition(position);
    camera.setCollisionRadius(radius);
    return camera;
}

TEST_CASE("CollisionHandler construction", "[scene][collision][handler]") {
    CollisionCache cache;
    CollisionHandler handler(cache);
    
    // Constructor should not throw and handler should be usable
    Camera camera = createTestCamera();
    glm::vec3 result = handler.resolveCameraMovement(camera, glm::vec3(0.0f));
    REQUIRE(isVec3Equal(result, glm::vec3(0.0f)));
}

TEST_CASE("CollisionHandler camera movement without collisions", "[scene][collision][handler][no_collision]") {
    CollisionCache cache;
    CollisionHandler handler(cache);

    SECTION("Empty collision cache") {
        Camera camera = createTestCamera(glm::vec3(0.0f), 1.0f);
        glm::vec3 desiredPosition(5.0f, 3.0f, -2.0f);

        glm::vec3 result = handler.resolveCameraMovement(camera, desiredPosition);

        // Should return desired position unchanged when no collisions
        REQUIRE(isVec3Equal(result, desiredPosition));
    }

    SECTION("Collision candidates with no intersections") {
        Camera camera = createTestCamera(glm::vec3(0.0f), 1.0f);
        glm::vec3 desiredPosition(0.0f, 0.0f, 0.0f);

        // Create scene node with collision shape far away
        SceneNode node;
        auto sphere = std::make_unique<CollisionSphere>(Sphere(glm::vec3(10.0f, 10.0f, 10.0f), 1.0f));
        node.sceneCollisionShapes.push_back(std::move(sphere));

        cache.addCandidate(&node);

        glm::vec3 result = handler.resolveCameraMovement(camera, desiredPosition);

        // Should return desired position unchanged when no intersections
        REQUIRE(isVec3Equal(result, desiredPosition));
    }
}

TEST_CASE("CollisionHandler camera movement with sphere collisions", "[scene][collision][handler][sphere]") {
    CollisionCache cache;
    CollisionHandler handler(cache);
    
    SECTION("Single sphere collision") {
        Camera camera = createTestCamera(glm::vec3(0.0f), 1.0f);
        glm::vec3 desiredPosition(2.0f, 0.0f, 0.0f); // Moving towards sphere

        // Create scene node with collision sphere at (3, 0, 0) with radius 2
        SceneNode node;
        auto sphere = std::make_unique<CollisionSphere>(Sphere(glm::vec3(3.0f, 0.0f, 0.0f), 2.0f));
        node.sceneCollisionShapes.push_back(std::move(sphere));

        cache.addCandidate(&node);

        glm::vec3 result = handler.resolveCameraMovement(camera, desiredPosition);

        // Camera sphere (radius 1) at (2,0,0) would overlap with collision sphere
        // Should be pushed away from the collision sphere
        REQUIRE(result.x < desiredPosition.x); // Pushed back in negative X direction
        REQUIRE(isFloatEqual(result.y, desiredPosition.y)); // Y unchanged
        REQUIRE(isFloatEqual(result.z, desiredPosition.z)); // Z unchanged
    }

    SECTION("Multiple sphere collisions") {
        Camera camera = createTestCamera(glm::vec3(0.0f), 1.0f);
        glm::vec3 desiredPosition(0.0f, 0.0f, 0.0f); // At origin

        // Create scene nodes with collision spheres surrounding the origin
        SceneNode node1, node2;

        auto sphere1 = std::make_unique<CollisionSphere>(Sphere(glm::vec3(1.5f, 0.0f, 0.0f), 1.0f));
        node1.sceneCollisionShapes.push_back(std::move(sphere1));

        auto sphere2 = std::make_unique<CollisionSphere>(Sphere(glm::vec3(-1.5f, 0.0f, 0.0f), 1.0f));
        node2.sceneCollisionShapes.push_back(std::move(sphere2));

        cache.addCandidate(&node1);
        cache.addCandidate(&node2);

        glm::vec3 result = handler.resolveCameraMovement(camera, desiredPosition);

        // Should be pushed away from both spheres
        REQUIRE_FALSE(isVec3Equal(result, desiredPosition));
    }
}

TEST_CASE("CollisionHandler camera movement with AABB collisions", "[scene][collision][handler][aabb]") {
    CollisionCache cache;
    CollisionHandler handler(cache);
    
    SECTION("Single AABB collision") {
        Camera camera = createTestCamera(glm::vec3(0.0f), 1.0f);
        glm::vec3 desiredPosition(0.0f, 0.0f, 0.0f); // At origin

        // Create scene node with AABB that overlaps camera sphere
        SceneNode node;
        auto aabb = std::make_unique<CollisionAABB>(AABB(glm::vec3(-0.5f, -0.5f, -0.5f), glm::vec3(0.5f, 0.5f, 0.5f)));
        node.sceneCollisionShapes.push_back(std::move(aabb));

        cache.addCandidate(&node);

        glm::vec3 result = handler.resolveCameraMovement(camera, desiredPosition);

        // Should be pushed away from the AABB
        REQUIRE_FALSE(isVec3Equal(result, desiredPosition));
    }
}

TEST_CASE("CollisionHandler camera movement with plane collisions", "[scene][collision][handler][plane]") {
    CollisionCache cache;
    CollisionHandler handler(cache);
    
    SECTION("Ground plane collision") {
        Camera camera = createTestCamera(glm::vec3(0.0f), 1.0f);
        glm::vec3 desiredPosition(0.0f, 0.5f, 0.0f); // Slightly above ground

        // Create scene node with ground plane at Y=0
        SceneNode node;
        auto plane = std::make_unique<CollisionPlane>(Plane(glm::vec3(0.0f, 1.0f, 0.0f), 0.0f));
        node.sceneCollisionShapes.push_back(std::move(plane));

        cache.addCandidate(&node);

        glm::vec3 result = handler.resolveCameraMovement(camera, desiredPosition);

        // With the fixed plane collision resolution, camera should be pushed above ground
        REQUIRE(result.y >= 1.0f); // Should be at least camera radius above ground
    }
}

TEST_CASE("CollisionHandler iterative resolution", "[scene][collision][handler][iterative]") {
    CollisionCache cache;
    CollisionHandler handler(cache);
    
    SECTION("Multiple iterations needed") {
        Camera camera = createTestCamera(glm::vec3(0.0f), 0.5f);
        glm::vec3 desiredPosition(0.0f, 0.0f, 0.0f);

        // Create multiple overlapping collision shapes
        SceneNode node1, node2, node3;

        auto sphere1 = std::make_unique<CollisionSphere>(Sphere(glm::vec3(0.8f, 0.0f, 0.0f), 0.6f));
        node1.sceneCollisionShapes.push_back(std::move(sphere1));

        auto sphere2 = std::make_unique<CollisionSphere>(Sphere(glm::vec3(-0.8f, 0.0f, 0.0f), 0.6f));
        node2.sceneCollisionShapes.push_back(std::move(sphere2));

        auto sphere3 = std::make_unique<CollisionSphere>(Sphere(glm::vec3(0.0f, 0.8f, 0.0f), 0.6f));
        node3.sceneCollisionShapes.push_back(std::move(sphere3));

        cache.addCandidate(&node1);
        cache.addCandidate(&node2);
        cache.addCandidate(&node3);

        glm::vec3 result = handler.resolveCameraMovement(camera, desiredPosition);

        // Should resolve to a position that doesn't intersect any collision shapes
        // The exact position depends on the resolution algorithm, but it should be moved away
        REQUIRE_FALSE(isVec3Equal(result, desiredPosition));
    }

    SECTION("Max iterations limit") {
        Camera camera = createTestCamera(glm::vec3(0.0f), 1.0f);
        glm::vec3 desiredPosition(0.0f, 0.0f, 0.0f);

        // Create a scenario that might require many iterations
        SceneNode node;
        auto sphere = std::make_unique<CollisionSphere>(Sphere(glm::vec3(0.0f, 0.0f, 0.0f), 2.0f));
        node.sceneCollisionShapes.push_back(std::move(sphere));

        cache.addCandidate(&node);

        glm::vec3 result = handler.resolveCameraMovement(camera, desiredPosition);

        // Should complete within max iterations and return a valid position
        // The algorithm should not get stuck in an infinite loop
        REQUIRE_FALSE(isVec3Equal(result, desiredPosition));
    }
}

TEST_CASE("CollisionHandler edge cases", "[scene][collision][handler][edge_cases]") {
    CollisionCache cache;
    CollisionHandler handler(cache);
    
    SECTION("Zero radius camera") {
        Camera camera = createTestCamera(glm::vec3(0.0f), 0.0f);
        glm::vec3 desiredPosition(1.0f, 1.0f, 1.0f);

        SceneNode node;
        auto sphere = std::make_unique<CollisionSphere>(Sphere(glm::vec3(1.0f, 1.0f, 1.0f), 1.0f));
        node.sceneCollisionShapes.push_back(std::move(sphere));

        cache.addCandidate(&node);

        glm::vec3 result = handler.resolveCameraMovement(camera, desiredPosition);

        // Zero radius camera should not collide with anything
        REQUIRE(isVec3Equal(result, desiredPosition));
    }

    SECTION("Very small movement") {
        Camera camera = createTestCamera(glm::vec3(0.0f), 1.0f);
        glm::vec3 desiredPosition(0.0001f, 0.0f, 0.0f); // Very small movement

        SceneNode node;
        auto sphere = std::make_unique<CollisionSphere>(Sphere(glm::vec3(5.0f, 0.0f, 0.0f), 1.0f)); // Far enough away
        node.sceneCollisionShapes.push_back(std::move(sphere));

        cache.addCandidate(&node);

        glm::vec3 result = handler.resolveCameraMovement(camera, desiredPosition);

        // Small movement that doesn't cause collision should be preserved
        REQUIRE(isVec3Equal(result, desiredPosition));
    }
}
