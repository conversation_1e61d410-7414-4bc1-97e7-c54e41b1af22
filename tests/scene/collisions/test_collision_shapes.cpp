#include <catch2/catch_test_macros.hpp>
#include <catch2/catch_approx.hpp>

#include <glm/gtc/matrix_transform.hpp>

#include "scene/collisions/collision_shapes/collision_sphere.hpp"
#include "scene/collisions/collision_shapes/collision_aabb.hpp"
#include "scene/collisions/collision_shapes/collision_plane.hpp"
#include "../../test_utils.hpp"

using namespace IronFrost;
using namespace IronFrost::CollisionMath;
using Catch::Approx;

TEST_CASE("CollisionSphere construction and basic operations", "[scene][collision][sphere]") {
    SECTION("Construction from sphere") {
        Sphere localSphere(glm::vec3(1.0f, 2.0f, 3.0f), 2.5f);
        CollisionSphere collisionSphere(localSphere);
        
        REQUIRE(isVec3Equal(collisionSphere.getWorldSphere().center, localSphere.center));
        REQUIRE(collisionSphere.getWorldSphere().radius == Approx(localSphere.radius));
    }
    
    SECTION("Construction with transform") {
        Sphere localSphere(glm::vec3(1.0f, 0.0f, 0.0f), 1.0f);
        glm::mat4 transform = glm::translate(glm::mat4(1.0f), glm::vec3(5.0f, 0.0f, 0.0f));
        
        CollisionSphere collisionSphere(localSphere, transform);
        
        REQUIRE(isVec3Equal(collisionSphere.getWorldSphere().center, glm::vec3(6.0f, 0.0f, 0.0f)));
        REQUIRE(collisionSphere.getWorldSphere().radius == Approx(1.0f));
    }
    
    SECTION("Update with transform") {
        Sphere localSphere(glm::vec3(0.0f, 0.0f, 0.0f), 1.0f);
        CollisionSphere collisionSphere(localSphere);
        
        glm::mat4 transform = glm::scale(glm::mat4(1.0f), glm::vec3(2.0f, 2.0f, 2.0f));
        collisionSphere.update(transform);
        
        REQUIRE(isVec3Equal(collisionSphere.getWorldSphere().center, glm::vec3(0.0f)));
        REQUIRE(collisionSphere.getWorldSphere().radius == Approx(2.0f)); // Scaled by 2
    }
}

TEST_CASE("CollisionSphere world AABB calculation", "[scene][collision][sphere][aabb]") {
    SECTION("Sphere at origin") {
        Sphere localSphere(glm::vec3(0.0f, 0.0f, 0.0f), 2.0f);
        CollisionSphere collisionSphere(localSphere);
        
        AABB worldAABB = collisionSphere.getWorldAABB();
        
        REQUIRE(isVec3Equal(worldAABB.min, glm::vec3(-2.0f, -2.0f, -2.0f)));
        REQUIRE(isVec3Equal(worldAABB.max, glm::vec3(2.0f, 2.0f, 2.0f)));
    }
    
    SECTION("Sphere at offset position") {
        Sphere localSphere(glm::vec3(5.0f, -3.0f, 2.0f), 1.5f);
        CollisionSphere collisionSphere(localSphere);
        
        AABB worldAABB = collisionSphere.getWorldAABB();
        
        REQUIRE(isVec3Equal(worldAABB.min, glm::vec3(3.5f, -4.5f, 0.5f)));
        REQUIRE(isVec3Equal(worldAABB.max, glm::vec3(6.5f, -1.5f, 3.5f)));
    }
}

TEST_CASE("CollisionSphere intersection tests", "[scene][collision][sphere][intersection]") {
    CollisionSphere sphere1(Sphere(glm::vec3(0.0f, 0.0f, 0.0f), 2.0f));
    
    SECTION("Sphere-sphere intersection") {
        CollisionSphere sphere2(Sphere(glm::vec3(3.0f, 0.0f, 0.0f), 2.0f)); // Overlapping
        CollisionSphere sphere3(Sphere(glm::vec3(5.0f, 0.0f, 0.0f), 1.0f)); // Non-overlapping
        
        REQUIRE(sphere1.intersectsSphere(sphere2));
        REQUIRE_FALSE(sphere1.intersectsSphere(sphere3));
        
        // Test symmetry
        REQUIRE(sphere2.intersectsSphere(sphere1));
        REQUIRE_FALSE(sphere3.intersectsSphere(sphere1));
    }
    
    SECTION("Sphere-AABB intersection") {
        CollisionAABB aabb1(AABB(glm::vec3(-1.0f, -1.0f, -1.0f), glm::vec3(1.0f, 1.0f, 1.0f))); // Overlapping
        CollisionAABB aabb2(AABB(glm::vec3(5.0f, 5.0f, 5.0f), glm::vec3(6.0f, 6.0f, 6.0f))); // Non-overlapping
        
        REQUIRE(sphere1.intersectsAABB(aabb1));
        REQUIRE_FALSE(sphere1.intersectsAABB(aabb2));
    }
    
    SECTION("Sphere-plane intersection") {
        CollisionPlane plane1(Plane(glm::vec3(0.0f, 1.0f, 0.0f), 0.0f)); // XZ plane at Y=0, sphere overlaps
        CollisionPlane plane2(Plane(glm::vec3(0.0f, 1.0f, 0.0f), -5.0f)); // Plane at Y=-5, sphere is above and doesn't reach

        REQUIRE(sphere1.intersectsPlane(plane1)); // Sphere at origin intersects plane at Y=0
        REQUIRE_FALSE(sphere1.intersectsPlane(plane2)); // Sphere at origin (radius 2) doesn't reach plane at Y=-5
    }
}

TEST_CASE("CollisionSphere collision resolution", "[scene][collision][sphere][resolution]") {
    CollisionSphere sphere1(Sphere(glm::vec3(0.0f, 0.0f, 0.0f), 2.0f));
    
    SECTION("Sphere-sphere collision resolution") {
        Sphere movingSphere(glm::vec3(3.0f, 0.0f, 0.0f), 2.0f); // Overlapping with sphere1
        glm::vec3 resolved = sphere1.resolveSphereCollision(movingSphere);
        
        // Should move the moving sphere away from sphere1
        REQUIRE(resolved.x > movingSphere.center.x); // Moved further in positive X
        REQUIRE(isFloatEqual(resolved.y, movingSphere.center.y)); // Y unchanged
        REQUIRE(isFloatEqual(resolved.z, movingSphere.center.z)); // Z unchanged
    }
    
    SECTION("AABB-sphere collision resolution") {
        AABB movingAABB(glm::vec3(-0.5f, -0.5f, -0.5f), glm::vec3(0.5f, 0.5f, 0.5f)); // Overlapping with sphere1
        glm::vec3 resolved = sphere1.resolveAABBCollision(movingAABB);
        
        // Should move the AABB center away from sphere1
        REQUIRE_FALSE(isVec3Equal(resolved, movingAABB.getCenter()));
    }
}

TEST_CASE("CollisionAABB construction and basic operations", "[scene][collision][aabb]") {
    SECTION("Construction from AABB") {
        AABB localAABB(glm::vec3(-1.0f, -2.0f, -3.0f), glm::vec3(1.0f, 2.0f, 3.0f));
        CollisionAABB collisionAABB(localAABB);
        
        REQUIRE(collisionAABB.getWorldAABB() == localAABB);
    }
    
    SECTION("Construction with transform") {
        AABB localAABB(glm::vec3(-1.0f, -1.0f, -1.0f), glm::vec3(1.0f, 1.0f, 1.0f));
        glm::mat4 transform = glm::translate(glm::mat4(1.0f), glm::vec3(5.0f, 0.0f, 0.0f));
        
        CollisionAABB collisionAABB(localAABB, transform);
        
        AABB worldAABB = collisionAABB.getWorldAABB();
        REQUIRE(isVec3Equal(worldAABB.min, glm::vec3(4.0f, -1.0f, -1.0f)));
        REQUIRE(isVec3Equal(worldAABB.max, glm::vec3(6.0f, 1.0f, 1.0f)));
    }
    
    SECTION("Update with transform") {
        AABB localAABB(glm::vec3(-1.0f, -1.0f, -1.0f), glm::vec3(1.0f, 1.0f, 1.0f));
        CollisionAABB collisionAABB(localAABB);
        
        glm::mat4 transform = glm::scale(glm::mat4(1.0f), glm::vec3(2.0f, 2.0f, 2.0f));
        collisionAABB.update(transform);
        
        AABB worldAABB = collisionAABB.getWorldAABB();
        REQUIRE(isVec3Equal(worldAABB.min, glm::vec3(-2.0f, -2.0f, -2.0f)));
        REQUIRE(isVec3Equal(worldAABB.max, glm::vec3(2.0f, 2.0f, 2.0f)));
    }
}

TEST_CASE("CollisionAABB intersection tests", "[scene][collision][aabb][intersection]") {
    CollisionAABB aabb1(AABB(glm::vec3(-1.0f, -1.0f, -1.0f), glm::vec3(1.0f, 1.0f, 1.0f)));
    
    SECTION("AABB-AABB intersection") {
        CollisionAABB aabb2(AABB(glm::vec3(0.0f, 0.0f, 0.0f), glm::vec3(2.0f, 2.0f, 2.0f))); // Overlapping
        CollisionAABB aabb3(AABB(glm::vec3(5.0f, 5.0f, 5.0f), glm::vec3(6.0f, 6.0f, 6.0f))); // Non-overlapping
        
        REQUIRE(aabb1.intersectsAABB(aabb2));
        REQUIRE_FALSE(aabb1.intersectsAABB(aabb3));
        
        // Test symmetry
        REQUIRE(aabb2.intersectsAABB(aabb1));
        REQUIRE_FALSE(aabb3.intersectsAABB(aabb1));
    }
    
    SECTION("AABB-sphere intersection") {
        CollisionSphere sphere1(Sphere(glm::vec3(0.0f, 0.0f, 0.0f), 1.5f)); // Overlapping
        CollisionSphere sphere2(Sphere(glm::vec3(5.0f, 0.0f, 0.0f), 1.0f)); // Non-overlapping
        
        REQUIRE(aabb1.intersectsSphere(sphere1));
        REQUIRE_FALSE(aabb1.intersectsSphere(sphere2));
    }
    
    SECTION("AABB-plane intersection") {
        CollisionPlane plane1(Plane(glm::vec3(0.0f, 1.0f, 0.0f), 0.0f)); // XZ plane at Y=0, AABB straddles
        CollisionPlane plane2(Plane(glm::vec3(0.0f, 1.0f, 0.0f), 5.0f)); // Plane at Y=5, AABB doesn't reach
        
        REQUIRE(aabb1.intersectsPlane(plane1));
        REQUIRE_FALSE(aabb1.intersectsPlane(plane2));
    }
}

TEST_CASE("CollisionAABB collision resolution", "[scene][collision][aabb][resolution]") {
    CollisionAABB aabb1(AABB(glm::vec3(-1.0f, -1.0f, -1.0f), glm::vec3(1.0f, 1.0f, 1.0f)));
    
    SECTION("Sphere-AABB collision resolution") {
        Sphere movingSphere(glm::vec3(0.0f, 0.0f, 0.0f), 1.5f); // Overlapping with aabb1
        glm::vec3 resolved = aabb1.resolveSphereCollision(movingSphere);
        
        // Should move the sphere away from the AABB
        REQUIRE_FALSE(isVec3Equal(resolved, movingSphere.center));
    }
    
    SECTION("AABB-AABB collision resolution") {
        AABB movingAABB(glm::vec3(0.5f, 0.5f, 0.5f), glm::vec3(1.5f, 1.5f, 1.5f)); // Overlapping with aabb1
        glm::vec3 resolved = aabb1.resolveAABBCollision(movingAABB);
        
        // Should move the moving AABB away from aabb1
        REQUIRE_FALSE(isVec3Equal(resolved, movingAABB.getCenter()));
    }
}

TEST_CASE("CollisionPlane construction and basic operations", "[scene][collision][plane]") {
    SECTION("Construction from plane") {
        Plane localPlane(glm::vec3(0.0f, 1.0f, 0.0f), 5.0f);
        CollisionPlane collisionPlane(localPlane);

        REQUIRE(isVec3Equal(collisionPlane.getWorldPlane().normal, localPlane.normal));
        REQUIRE(collisionPlane.getWorldPlane().distance == Approx(localPlane.distance));
    }

    SECTION("Construction with transform") {
        Plane localPlane(glm::vec3(0.0f, 1.0f, 0.0f), 0.0f); // XZ plane at Y=0
        glm::mat4 transform = glm::translate(glm::mat4(1.0f), glm::vec3(0.0f, 3.0f, 0.0f));

        CollisionPlane collisionPlane(localPlane, transform);

        REQUIRE(isVec3Equal(collisionPlane.getWorldPlane().normal, glm::vec3(0.0f, 1.0f, 0.0f)));
        REQUIRE(collisionPlane.getWorldPlane().distance == Approx(3.0f)); // Moved up by 3
    }

    SECTION("Update with transform") {
        Plane localPlane(glm::vec3(0.0f, 1.0f, 0.0f), 0.0f);
        CollisionPlane collisionPlane(localPlane);

        glm::mat4 transform = glm::rotate(glm::mat4(1.0f), glm::radians(90.0f), glm::vec3(0.0f, 0.0f, 1.0f));
        collisionPlane.update(transform);

        // Y-up normal should become X-left normal after 90° Z rotation
        REQUIRE(isVec3Equal(collisionPlane.getWorldPlane().normal, glm::vec3(-1.0f, 0.0f, 0.0f)));
        REQUIRE(collisionPlane.getWorldPlane().distance == Approx(0.0f));
    }
}

TEST_CASE("CollisionPlane world AABB calculation", "[scene][collision][plane][aabb]") {
    SECTION("Plane at origin") {
        Plane localPlane(glm::vec3(0.0f, 1.0f, 0.0f), 0.0f);
        CollisionPlane collisionPlane(localPlane);

        AABB worldAABB = collisionPlane.getWorldAABB();

        // Should create an AABB centered at the plane's closest point to origin
        REQUIRE(isVec3Equal(worldAABB.getCenter(), glm::vec3(0.0f, 0.0f, 0.0f)));
        REQUIRE(isVec3Equal(worldAABB.getSize(), glm::vec3(1.0f, 1.0f, 1.0f)));
    }

    SECTION("Plane at distance") {
        Plane localPlane(glm::vec3(0.0f, 1.0f, 0.0f), 5.0f);
        CollisionPlane collisionPlane(localPlane);

        AABB worldAABB = collisionPlane.getWorldAABB();

        // Should create an AABB centered at the plane's closest point to origin
        REQUIRE(isVec3Equal(worldAABB.getCenter(), glm::vec3(0.0f, 5.0f, 0.0f)));
        REQUIRE(isVec3Equal(worldAABB.getSize(), glm::vec3(1.0f, 1.0f, 1.0f)));
    }
}

TEST_CASE("CollisionPlane intersection tests", "[scene][collision][plane][intersection]") {
    CollisionPlane plane1(Plane(glm::vec3(0.0f, 1.0f, 0.0f), 0.0f)); // XZ plane at Y=0

    SECTION("Plane-sphere intersection") {
        CollisionSphere sphere1(Sphere(glm::vec3(0.0f, 1.0f, 0.0f), 2.0f)); // Overlapping
        CollisionSphere sphere2(Sphere(glm::vec3(0.0f, 5.0f, 0.0f), 2.0f)); // Non-overlapping

        REQUIRE(plane1.intersectsSphere(sphere1));
        REQUIRE_FALSE(plane1.intersectsSphere(sphere2));
    }

    SECTION("Plane-AABB intersection") {
        CollisionAABB aabb1(AABB(glm::vec3(-1.0f, -1.0f, -1.0f), glm::vec3(1.0f, 1.0f, 1.0f))); // Straddles plane
        CollisionAABB aabb2(AABB(glm::vec3(-1.0f, 2.0f, -1.0f), glm::vec3(1.0f, 4.0f, 1.0f))); // Above plane

        REQUIRE(plane1.intersectsAABB(aabb1));
        REQUIRE_FALSE(plane1.intersectsAABB(aabb2));
    }

    SECTION("Plane-plane intersection") {
        CollisionPlane plane2(Plane(glm::vec3(1.0f, 0.0f, 0.0f), 0.0f)); // YZ plane at X=0
        CollisionPlane plane3(Plane(glm::vec3(0.0f, 1.0f, 0.0f), 5.0f)); // Parallel plane at Y=5

        REQUIRE(plane1.intersectsPlane(plane2)); // Perpendicular planes should intersect
        REQUIRE_FALSE(plane1.intersectsPlane(plane3)); // Parallel planes should not intersect
    }
}

TEST_CASE("CollisionPlane collision resolution", "[scene][collision][plane][resolution]") {
    CollisionPlane plane1(Plane(glm::vec3(0.0f, 1.0f, 0.0f), 0.0f)); // XZ plane at Y=0

    SECTION("Sphere-plane collision resolution") {
        Sphere movingSphere(glm::vec3(0.0f, -1.0f, 0.0f), 2.0f); // Penetrating plane from below
        glm::vec3 resolved = plane1.resolveSphereCollision(movingSphere);

        // Should move sphere to Y = 2.0f (radius above plane)
        REQUIRE(isVec3Equal(resolved, glm::vec3(0.0f, 2.0f, 0.0f)));
    }

    SECTION("AABB-plane collision resolution") {
        AABB movingAABB(glm::vec3(-1.0f, -2.0f, -1.0f), glm::vec3(1.0f, 1.0f, 1.0f)); // Penetrating plane
        glm::vec3 resolved = plane1.resolveAABBCollision(movingAABB);

        // Should move AABB center so that the AABB doesn't penetrate the plane
        // The AABB extends from Y=-2 to Y=1, so center is at Y=-0.5
        // The AABB extends 1.5 units above its center, so it should be moved to Y=1.5 + epsilon
        REQUIRE(resolved.y > 1.5f); // Should be above the plane
        REQUIRE(isFloatEqual(resolved.x, movingAABB.getCenter().x)); // X unchanged
        REQUIRE(isFloatEqual(resolved.z, movingAABB.getCenter().z)); // Z unchanged
    }
}
