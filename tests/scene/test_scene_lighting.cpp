#include <catch2/catch_test_macros.hpp>

// C++ standard library
#include <cmath>

// Third-party libraries
#include <glm/glm.hpp>
#include <glm/gtc/constants.hpp>

// Local includes
#include "scene/scene_lighting.hpp"
#include "../test_utils.hpp"

using namespace IronFrost;
using namespace IronFrost::TestUtils;

TEST_CASE("SceneLighting basic functionality", "[scene][lighting][basic]") {
    SECTION("Default constructor sets default ambient lighting") {
        SceneLighting lighting;
        
        // Check default ambient light color
        const auto& ambientColor = lighting.getAmbientLightColor();
        REQUIRE(ambientColor.r == 0.6f);
        REQUIRE(ambientColor.g == 0.5f);
        REQUIRE(ambientColor.b == 0.4f);
        
        // Check default ambient light intensity
        REQUIRE(lighting.getAmbientLightIntensity() == 0.6f);
        
        // Check that no directional lights exist initially
        const auto& directionalLights = lighting.getDirectionalLights();
        REQUIRE(directionalLights.empty());
        REQUIRE(directionalLights.size() == 0);
    }
}

TEST_CASE("SceneLighting ambient light management", "[scene][lighting][ambient]") {
    SECTION("Set and get ambient light color") {
        SceneLighting lighting;
        
        glm::vec3 newColor(0.8f, 0.7f, 0.9f);
        lighting.setAmbientLightColor(newColor);
        
        const auto& retrievedColor = lighting.getAmbientLightColor();
        REQUIRE(retrievedColor.r == 0.8f);
        REQUIRE(retrievedColor.g == 0.7f);
        REQUIRE(retrievedColor.b == 0.9f);
    }
    
    SECTION("Set and get ambient light intensity") {
        SceneLighting lighting;
        
        float newIntensity = 0.85f;
        lighting.setAmbientLightIntensity(newIntensity);
        
        REQUIRE(lighting.getAmbientLightIntensity() == 0.85f);
    }
    
    SECTION("Ambient light color edge cases") {
        SceneLighting lighting;
        
        // Test zero color (black)
        glm::vec3 blackColor(0.0f, 0.0f, 0.0f);
        lighting.setAmbientLightColor(blackColor);
        REQUIRE(lighting.getAmbientLightColor() == blackColor);
        
        // Test maximum color (white)
        glm::vec3 whiteColor(1.0f, 1.0f, 1.0f);
        lighting.setAmbientLightColor(whiteColor);
        REQUIRE(lighting.getAmbientLightColor() == whiteColor);
        
        // Test HDR colors (above 1.0)
        glm::vec3 hdrColor(2.0f, 1.5f, 3.0f);
        lighting.setAmbientLightColor(hdrColor);
        REQUIRE(lighting.getAmbientLightColor() == hdrColor);
        
        // Test negative colors
        glm::vec3 negativeColor(-0.5f, -1.0f, -0.25f);
        lighting.setAmbientLightColor(negativeColor);
        REQUIRE(lighting.getAmbientLightColor() == negativeColor);
    }
    
    SECTION("Ambient light intensity edge cases") {
        SceneLighting lighting;
        
        // Test zero intensity
        lighting.setAmbientLightIntensity(0.0f);
        REQUIRE(lighting.getAmbientLightIntensity() == 0.0f);
        
        // Test maximum intensity
        lighting.setAmbientLightIntensity(1.0f);
        REQUIRE(lighting.getAmbientLightIntensity() == 1.0f);
        
        // Test HDR intensity (above 1.0)
        lighting.setAmbientLightIntensity(2.5f);
        REQUIRE(lighting.getAmbientLightIntensity() == 2.5f);
        
        // Test negative intensity
        lighting.setAmbientLightIntensity(-0.5f);
        REQUIRE(lighting.getAmbientLightIntensity() == -0.5f);
        
        // Test very small intensity
        lighting.setAmbientLightIntensity(1e-6f);
        REQUIRE(isApproximatelyEqual(lighting.getAmbientLightIntensity(), 1e-6f));
        
        // Test very large intensity
        lighting.setAmbientLightIntensity(1e6f);
        REQUIRE(lighting.getAmbientLightIntensity() == 1e6f);
    }
}

TEST_CASE("SceneLighting directional light management", "[scene][lighting][directional]") {
    SECTION("Add directional light with struct") {
        SceneLighting lighting;
        
        DirectionalLight light;
        light.direction = glm::vec3(0.0f, -1.0f, 0.0f); // Downward
        light.color = glm::vec3(1.0f, 0.8f, 0.6f);      // Warm white
        light.intensity = 0.9f;
        
        lighting.addDirectionalLight(light);
        
        const auto& lights = lighting.getDirectionalLights();
        REQUIRE(lights.size() == 1);
        
        const auto& retrievedLight = lights[0];
        REQUIRE(retrievedLight.direction == light.direction);
        REQUIRE(retrievedLight.color == light.color);
        REQUIRE(retrievedLight.intensity == light.intensity);
    }
    
    SECTION("Add directional light with parameters") {
        SceneLighting lighting;
        
        glm::vec3 direction(1.0f, -1.0f, 0.5f);
        glm::vec3 color(0.9f, 0.9f, 1.0f); // Cool white
        float intensity = 0.75f;
        
        lighting.addDirectionalLight(direction, color, intensity);
        
        const auto& lights = lighting.getDirectionalLights();
        REQUIRE(lights.size() == 1);
        
        const auto& retrievedLight = lights[0];
        
        // Direction should be normalized
        glm::vec3 expectedDirection = glm::normalize(direction);
        REQUIRE(isApproximatelyEqual(retrievedLight.direction, expectedDirection));
        
        REQUIRE(retrievedLight.color == color);
        REQUIRE(retrievedLight.intensity == intensity);
    }
    
    SECTION("Add multiple directional lights") {
        SceneLighting lighting;
        
        // Add first light (sun)
        lighting.addDirectionalLight(
            glm::vec3(0.3f, -0.8f, 0.2f),  // Direction
            glm::vec3(1.0f, 0.95f, 0.8f),  // Warm sunlight
            1.0f                            // Full intensity
        );
        
        // Add second light (moon)
        lighting.addDirectionalLight(
            glm::vec3(-0.2f, -0.5f, -0.7f), // Direction
            glm::vec3(0.7f, 0.8f, 1.0f),    // Cool moonlight
            0.3f                             // Low intensity
        );
        
        // Add third light (fill light)
        DirectionalLight fillLight;
        fillLight.direction = glm::normalize(glm::vec3(0.5f, 0.2f, -0.3f));
        fillLight.color = glm::vec3(0.5f, 0.6f, 0.7f);
        fillLight.intensity = 0.4f;
        lighting.addDirectionalLight(fillLight);
        
        const auto& lights = lighting.getDirectionalLights();
        REQUIRE(lights.size() == 3);
        
        // Verify first light (sun)
        REQUIRE(isApproximatelyEqual(lights[0].direction, glm::normalize(glm::vec3(0.3f, -0.8f, 0.2f))));
        REQUIRE(lights[0].color == glm::vec3(1.0f, 0.95f, 0.8f));
        REQUIRE(lights[0].intensity == 1.0f);
        
        // Verify second light (moon)
        REQUIRE(isApproximatelyEqual(lights[1].direction, glm::normalize(glm::vec3(-0.2f, -0.5f, -0.7f))));
        REQUIRE(lights[1].color == glm::vec3(0.7f, 0.8f, 1.0f));
        REQUIRE(lights[1].intensity == 0.3f);
        
        // Verify third light (fill)
        REQUIRE(isApproximatelyEqual(lights[2].direction, fillLight.direction));
        REQUIRE(lights[2].color == fillLight.color);
        REQUIRE(lights[2].intensity == fillLight.intensity);
    }
    
    SECTION("Clear directional lights") {
        SceneLighting lighting;
        
        // Add some lights
        lighting.addDirectionalLight(glm::vec3(0.0f, -1.0f, 0.0f), glm::vec3(1.0f, 1.0f, 1.0f), 1.0f);
        lighting.addDirectionalLight(glm::vec3(1.0f, 0.0f, 0.0f), glm::vec3(0.5f, 0.5f, 0.5f), 0.5f);
        
        REQUIRE(lighting.getDirectionalLights().size() == 2);
        
        // Clear all lights
        lighting.clearDirectionalLights();
        
        const auto& lights = lighting.getDirectionalLights();
        REQUIRE(lights.empty());
        REQUIRE(lights.size() == 0);
    }
    
    SECTION("Direction normalization") {
        SceneLighting lighting;
        
        // Test various unnormalized directions
        std::vector<glm::vec3> testDirections = {
            glm::vec3(2.0f, 0.0f, 0.0f),      // Length 2
            glm::vec3(0.0f, 3.0f, 4.0f),      // Length 5
            glm::vec3(1.0f, 1.0f, 1.0f),      // Length sqrt(3)
            glm::vec3(0.1f, 0.2f, 0.3f),      // Small values
            glm::vec3(10.0f, -5.0f, 2.0f),    // Large values
            glm::vec3(-1.0f, -1.0f, -1.0f)    // Negative values
        };
        
        for (const auto& direction : testDirections) {
            lighting.clearDirectionalLights();
            lighting.addDirectionalLight(direction, glm::vec3(1.0f), 1.0f);
            
            const auto& lights = lighting.getDirectionalLights();
            REQUIRE(lights.size() == 1);
            
            const auto& normalizedDirection = lights[0].direction;
            
            // Check that direction is normalized (length ≈ 1)
            float length = glm::length(normalizedDirection);
            REQUIRE(isApproximatelyEqual(length, 1.0f, 1e-5f));
            
            // Check that direction points in the same general direction
            glm::vec3 expectedDirection = glm::normalize(direction);
            REQUIRE(isApproximatelyEqual(normalizedDirection, expectedDirection, 1e-5f));
        }
    }
}

TEST_CASE("SceneLighting edge cases", "[scene][lighting][edge_cases]") {
    SECTION("Zero direction vector") {
        SceneLighting lighting;
        
        // Adding a zero direction vector should still work (glm::normalize handles it)
        glm::vec3 zeroDirection(0.0f, 0.0f, 0.0f);
        
        // This might result in undefined behavior with glm::normalize, but shouldn't crash
        REQUIRE_NOTHROW(lighting.addDirectionalLight(zeroDirection, glm::vec3(1.0f), 1.0f));
        
        const auto& lights = lighting.getDirectionalLights();
        REQUIRE(lights.size() == 1);
        
        // The result of normalizing a zero vector is implementation-defined
        // but should not crash the application
    }
    
    SECTION("Very small direction vector") {
        SceneLighting lighting;
        
        glm::vec3 tinyDirection(1e-10f, 1e-10f, 1e-10f);
        
        REQUIRE_NOTHROW(lighting.addDirectionalLight(tinyDirection, glm::vec3(1.0f), 1.0f));
        
        const auto& lights = lighting.getDirectionalLights();
        REQUIRE(lights.size() == 1);
        
        // Should still produce a normalized vector
        float length = glm::length(lights[0].direction);
        REQUIRE(isApproximatelyEqual(length, 1.0f, 1e-3f)); // More tolerance for very small vectors
    }
    
    SECTION("Maximum number of directional lights") {
        SceneLighting lighting;
        
        // Add more lights than the maximum
        for (int i = 0; i < MAX_DIRECTIONAL_LIGHTS + 5; ++i) {
            glm::vec3 direction(
                static_cast<float>(i),
                static_cast<float>(i + 1),
                static_cast<float>(i + 2)
            );
            lighting.addDirectionalLight(direction, glm::vec3(1.0f), 1.0f);
        }
        
        const auto& lights = lighting.getDirectionalLights();
        
        // Should have added all lights (no automatic limiting in the current implementation)
        REQUIRE(lights.size() == MAX_DIRECTIONAL_LIGHTS + 5);
        
        // Note: The MAX_DIRECTIONAL_LIGHTS constant is for shader limitations,
        // not enforced by the SceneLighting class itself
    }
    
    SECTION("Extreme color and intensity values") {
        SceneLighting lighting;
        
        // Test extreme values
        struct TestCase {
            glm::vec3 color;
            float intensity;
            std::string description;
        };
        
        std::vector<TestCase> testCases = {
            {{0.0f, 0.0f, 0.0f}, 0.0f, "zero color and intensity"},
            {{1e6f, 1e6f, 1e6f}, 1e6f, "very large values"},
            {{-1e3f, -1e3f, -1e3f}, -1e3f, "very negative values"},
            {{1e-6f, 1e-6f, 1e-6f}, 1e-6f, "very small positive values"},
            {{std::numeric_limits<float>::max(), 0.0f, 0.0f}, 1.0f, "float max color"},
            {{1.0f, 1.0f, 1.0f}, std::numeric_limits<float>::max(), "float max intensity"}
        };
        
        for (const auto& testCase : testCases) {
            lighting.clearDirectionalLights();
            
            REQUIRE_NOTHROW(lighting.addDirectionalLight(
                glm::vec3(0.0f, -1.0f, 0.0f), // Standard direction
                testCase.color,
                testCase.intensity
            ));
            
            const auto& lights = lighting.getDirectionalLights();
            REQUIRE(lights.size() == 1);
            
            REQUIRE(lights[0].color == testCase.color);
            REQUIRE(lights[0].intensity == testCase.intensity);
        }
    }
}

TEST_CASE("SceneLighting real-world scenarios", "[scene][lighting][real_world]") {
    SECTION("Daytime outdoor lighting setup") {
        SceneLighting lighting;

        // Set bright ambient lighting for outdoor scene
        lighting.setAmbientLightColor(glm::vec3(0.4f, 0.6f, 0.8f)); // Sky blue tint
        lighting.setAmbientLightIntensity(0.3f); // Moderate ambient

        // Add main sun light
        lighting.addDirectionalLight(
            glm::vec3(0.3f, -0.8f, 0.2f),  // Sun angle (morning/afternoon)
            glm::vec3(1.0f, 0.95f, 0.8f),  // Warm sunlight
            1.2f                            // Bright intensity
        );

        // Verify setup
        REQUIRE(lighting.getAmbientLightColor() == glm::vec3(0.4f, 0.6f, 0.8f));
        REQUIRE(lighting.getAmbientLightIntensity() == 0.3f);

        const auto& lights = lighting.getDirectionalLights();
        REQUIRE(lights.size() == 1);
        REQUIRE(lights[0].intensity == 1.2f);
        REQUIRE(isApproximatelyEqual(lights[0].color, glm::vec3(1.0f, 0.95f, 0.8f)));
    }

    SECTION("Nighttime indoor lighting setup") {
        SceneLighting lighting;

        // Set dark ambient lighting for indoor scene
        lighting.setAmbientLightColor(glm::vec3(0.1f, 0.1f, 0.15f)); // Dark blue tint
        lighting.setAmbientLightIntensity(0.1f); // Very low ambient

        // Add artificial lights

        // Main ceiling light
        lighting.addDirectionalLight(
            glm::vec3(0.0f, -1.0f, 0.0f),  // Straight down
            glm::vec3(1.0f, 0.9f, 0.7f),   // Warm white
            0.8f                            // Medium intensity
        );

        // Side lamp
        lighting.addDirectionalLight(
            glm::vec3(0.7f, -0.5f, 0.3f),  // Angled from side
            glm::vec3(1.0f, 0.8f, 0.6f),   // Warmer light
            0.4f                            // Lower intensity
        );

        // Window moonlight
        lighting.addDirectionalLight(
            glm::vec3(-0.3f, -0.2f, 0.8f), // From window
            glm::vec3(0.6f, 0.7f, 1.0f),   // Cool moonlight
            0.2f                            // Very low intensity
        );

        // Verify setup
        REQUIRE(lighting.getAmbientLightIntensity() == 0.1f);

        const auto& lights = lighting.getDirectionalLights();
        REQUIRE(lights.size() == 3);

        // Check ceiling light
        REQUIRE(isApproximatelyEqual(lights[0].direction, glm::vec3(0.0f, -1.0f, 0.0f)));
        REQUIRE(lights[0].intensity == 0.8f);

        // Check side lamp
        REQUIRE(lights[1].intensity == 0.4f);

        // Check moonlight
        REQUIRE(lights[2].intensity == 0.2f);
    }

    SECTION("Sunset/sunrise lighting transition") {
        SceneLighting lighting;

        // Simulate sunset lighting progression
        struct SunsetPhase {
            std::string name;
            glm::vec3 ambientColor;
            float ambientIntensity;
            glm::vec3 sunDirection;
            glm::vec3 sunColor;
            float sunIntensity;
        };

        std::vector<SunsetPhase> phases = {
            {
                "Noon",
                glm::vec3(0.4f, 0.6f, 0.8f),   // Blue sky
                0.4f,
                glm::vec3(0.0f, -1.0f, 0.0f),  // Sun overhead
                glm::vec3(1.0f, 1.0f, 0.95f),  // White sun
                1.5f
            },
            {
                "Afternoon",
                glm::vec3(0.5f, 0.6f, 0.7f),   // Slightly warmer
                0.35f,
                glm::vec3(0.5f, -0.7f, 0.3f),  // Sun lower
                glm::vec3(1.0f, 0.95f, 0.85f), // Slightly warm
                1.2f
            },
            {
                "Golden Hour",
                glm::vec3(0.8f, 0.6f, 0.4f),   // Golden ambient
                0.3f,
                glm::vec3(0.8f, -0.3f, 0.2f),  // Sun very low
                glm::vec3(1.0f, 0.8f, 0.5f),   // Golden sun
                1.0f
            },
            {
                "Sunset",
                glm::vec3(0.9f, 0.5f, 0.3f),   // Orange/red ambient
                0.2f,
                glm::vec3(0.9f, -0.1f, 0.1f),  // Sun on horizon
                glm::vec3(1.0f, 0.6f, 0.3f),   // Orange/red sun
                0.8f
            },
            {
                "Dusk",
                glm::vec3(0.3f, 0.2f, 0.4f),   // Purple ambient
                0.1f,
                glm::vec3(0.8f, 0.1f, 0.0f),   // Sun below horizon
                glm::vec3(0.8f, 0.4f, 0.6f),   // Purple glow
                0.3f
            }
        };

        for (const auto& phase : phases) {
            lighting.clearDirectionalLights();

            // Set ambient lighting
            lighting.setAmbientLightColor(phase.ambientColor);
            lighting.setAmbientLightIntensity(phase.ambientIntensity);

            // Set sun lighting
            lighting.addDirectionalLight(phase.sunDirection, phase.sunColor, phase.sunIntensity);

            // Verify the phase
            REQUIRE(lighting.getAmbientLightColor() == phase.ambientColor);
            REQUIRE(lighting.getAmbientLightIntensity() == phase.ambientIntensity);

            const auto& lights = lighting.getDirectionalLights();
            REQUIRE(lights.size() == 1);
            REQUIRE(lights[0].color == phase.sunColor);
            REQUIRE(lights[0].intensity == phase.sunIntensity);

            // Verify direction is normalized
            float dirLength = glm::length(lights[0].direction);
            REQUIRE(isApproximatelyEqual(dirLength, 1.0f, 1e-5f));
        }
    }

    SECTION("Multi-light studio setup") {
        SceneLighting lighting;

        // Professional 3-point lighting setup

        // Low ambient for controlled lighting
        lighting.setAmbientLightColor(glm::vec3(0.1f, 0.1f, 0.1f));
        lighting.setAmbientLightIntensity(0.05f);

        // Key light (main light)
        lighting.addDirectionalLight(
            glm::vec3(0.5f, -0.7f, 0.5f),  // 45 degrees from subject
            glm::vec3(1.0f, 0.95f, 0.9f),  // Slightly warm
            1.0f                            // Full intensity
        );

        // Fill light (soften shadows)
        lighting.addDirectionalLight(
            glm::vec3(-0.3f, -0.5f, 0.8f), // Opposite side, lower
            glm::vec3(0.8f, 0.85f, 1.0f),  // Slightly cool
            0.4f                            // Lower intensity
        );

        // Back light (rim lighting)
        lighting.addDirectionalLight(
            glm::vec3(-0.2f, 0.3f, -0.9f), // From behind and above
            glm::vec3(1.0f, 1.0f, 1.0f),   // Pure white
            0.6f                            // Medium intensity
        );

        // Background light
        lighting.addDirectionalLight(
            glm::vec3(0.0f, 0.8f, -0.6f),  // From behind, upward
            glm::vec3(0.7f, 0.8f, 1.0f),   // Cool background
            0.3f                            // Low intensity
        );

        // Verify studio setup
        const auto& lights = lighting.getDirectionalLights();
        REQUIRE(lights.size() == 4);

        // Verify key light (strongest)
        REQUIRE(lights[0].intensity == 1.0f);

        // Verify fill light
        REQUIRE(lights[1].intensity == 0.4f);

        // Verify back light
        REQUIRE(lights[2].intensity == 0.6f);

        // Verify background light (weakest)
        REQUIRE(lights[3].intensity == 0.3f);

        // All directions should be normalized
        for (const auto& light : lights) {
            float length = glm::length(light.direction);
            REQUIRE(isApproximatelyEqual(length, 1.0f, 1e-5f));
        }
    }

    SECTION("Dynamic lighting changes") {
        SceneLighting lighting;

        // Start with basic setup
        lighting.setAmbientLightColor(glm::vec3(0.2f, 0.2f, 0.2f));
        lighting.setAmbientLightIntensity(0.2f);
        lighting.addDirectionalLight(glm::vec3(0.0f, -1.0f, 0.0f), glm::vec3(1.0f), 1.0f);

        REQUIRE(lighting.getDirectionalLights().size() == 1);

        // Add more lights dynamically
        for (int i = 0; i < 3; ++i) {
            float angle = i * 2.0f * glm::pi<float>() / 3.0f; // 120 degrees apart
            glm::vec3 direction(
                std::cos(angle),
                -0.5f,
                std::sin(angle)
            );

            glm::vec3 color(
                0.5f + 0.5f * std::cos(angle),
                0.5f + 0.5f * std::cos(angle + 2.0f * glm::pi<float>() / 3.0f),
                0.5f + 0.5f * std::cos(angle + 4.0f * glm::pi<float>() / 3.0f)
            );

            lighting.addDirectionalLight(direction, color, 0.5f);
        }

        REQUIRE(lighting.getDirectionalLights().size() == 4); // 1 original + 3 new

        // Modify ambient lighting
        lighting.setAmbientLightColor(glm::vec3(0.1f, 0.3f, 0.5f));
        lighting.setAmbientLightIntensity(0.4f);

        REQUIRE(lighting.getAmbientLightColor() == glm::vec3(0.1f, 0.3f, 0.5f));
        REQUIRE(lighting.getAmbientLightIntensity() == 0.4f);

        // Clear and rebuild
        lighting.clearDirectionalLights();
        REQUIRE(lighting.getDirectionalLights().empty());

        // Add new lighting scheme
        lighting.addDirectionalLight(glm::vec3(1.0f, 0.0f, 0.0f), glm::vec3(1.0f, 0.0f, 0.0f), 0.8f);
        lighting.addDirectionalLight(glm::vec3(0.0f, 1.0f, 0.0f), glm::vec3(0.0f, 1.0f, 0.0f), 0.8f);
        lighting.addDirectionalLight(glm::vec3(0.0f, 0.0f, 1.0f), glm::vec3(0.0f, 0.0f, 1.0f), 0.8f);

        const auto& finalLights = lighting.getDirectionalLights();
        REQUIRE(finalLights.size() == 3);

        // Verify RGB lighting setup
        REQUIRE(finalLights[0].color == glm::vec3(1.0f, 0.0f, 0.0f)); // Red
        REQUIRE(finalLights[1].color == glm::vec3(0.0f, 1.0f, 0.0f)); // Green
        REQUIRE(finalLights[2].color == glm::vec3(0.0f, 0.0f, 1.0f)); // Blue
    }
}
