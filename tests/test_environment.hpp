#ifndef __IF__TEST_ENVIRONMENT_HPP
#define __IF__TEST_ENVIRONMENT_HPP

// C++ standard library
#include <cstdlib>
#include <string>

namespace IronFrost {
namespace TestEnvironment {

/**
 * @brief Check if we're running in a headless environment (CI, no display)
 * @return true if headless, false if graphics are available
 */
inline bool isHeadless() {
    // Check common environment variables that indicate headless environment
    const char* display = std::getenv("DISPLAY");
    const char* ci = std::getenv("CI");
    const char* github_actions = std::getenv("GITHUB_ACTIONS");
    const char* headless = std::getenv("HEADLESS");
    const char* skip_graphics = std::getenv("SKIP_GRAPHICS_TESTS");

    // If explicitly set to headless or skip graphics tests
    if (headless && std::string(headless) == "1") {
        return true;
    }

    if (skip_graphics && std::string(skip_graphics) == "1") {
        return true;
    }

    // If running in CI
    if (ci && std::string(ci) == "true") {
        return true;
    }

    if (github_actions && std::string(github_actions) == "true") {
        return true;
    }

    // For now, assume we're always in headless mode for tests
    // This prevents segfaults from graphics-dependent tests
    // TODO: Add proper graphics context detection
    return true;
}

/**
 * @brief Check if graphics/OpenGL context is available
 * @return true if graphics are available, false otherwise
 */
inline bool hasGraphicsContext() {
    return !isHeadless();
}

} // namespace TestEnvironment
} // namespace IronFrost

// Convenience macros for skipping tests in headless environments
#define SKIP_IF_HEADLESS() \
    if (IronFrost::TestEnvironment::isHeadless()) { \
        SKIP("Test requires graphics context - skipping in headless environment"); \
    }

#define SKIP_IF_NO_GRAPHICS() \
    if (!IronFrost::TestEnvironment::hasGraphicsContext()) { \
        SKIP("Test requires graphics context - skipping"); \
    }

#endif // __IF__TEST_ENVIRONMENT_HPP
