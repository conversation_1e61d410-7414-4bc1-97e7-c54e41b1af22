#include <catch2/catch_test_macros.hpp>

// C++ standard library
#include <chrono>
#include <functional>
#include <thread>
#include <vector>
#include <atomic>

// Local includes
#include "scripts/scheduled_task_manager.hpp"

using namespace IronFrost;
using namespace std::chrono_literals;

TEST_CASE("ScheduledTaskManager basic functionality", "[scripts][scheduled_task_manager][basic]") {
    ScheduledTaskManager manager;

    SECTION("Empty manager has no tasks to execute") {
        // Should not crash when no tasks are scheduled
        REQUIRE_NOTHROW(manager.executeScheduledTasks());
    }

    SECTION("Single task execution") {
        bool taskExecuted = false;
        auto now = std::chrono::steady_clock::now();
        auto pastTime = now - 1s;

        manager.scheduleTask([&taskExecuted]() {
            taskExecuted = true;
        }, pastTime);

        REQUIRE(taskExecuted == false);
        manager.executeScheduledTasks();
        REQUIRE(taskExecuted == true);
    }

    SECTION("Task scheduled for future is not executed") {
        bool taskExecuted = false;
        auto now = std::chrono::steady_clock::now();
        auto futureTime = now + 1s;

        manager.scheduleTask([&taskExecuted]() {
            taskExecuted = true;
        }, futureTime);

        manager.executeScheduledTasks();
        REQUIRE(taskExecuted == false);
    }

    SECTION("Multiple tasks with different execution times") {
        std::vector<bool> tasksExecuted(3, false);
        auto now = std::chrono::steady_clock::now();

        // Schedule tasks: past, future, now
        manager.scheduleTask([&tasksExecuted]() {
            tasksExecuted[0] = true;
        }, now - 1s);

        manager.scheduleTask([&tasksExecuted]() {
            tasksExecuted[1] = true;
        }, now + 1s);

        manager.scheduleTask([&tasksExecuted]() {
            tasksExecuted[2] = true;
        }, now);

        manager.executeScheduledTasks();

        // Only past and current time tasks should execute
        REQUIRE(tasksExecuted[0] == true);
        REQUIRE(tasksExecuted[1] == false);
        REQUIRE(tasksExecuted[2] == true);
    }
}

TEST_CASE("ScheduledTaskManager task ordering", "[scripts][scheduled_task_manager][ordering]") {
    ScheduledTaskManager manager;

    SECTION("Tasks execute in chronological order") {
        std::vector<int> executionOrder;
        auto now = std::chrono::steady_clock::now();

        // Schedule tasks in reverse chronological order
        manager.scheduleTask([&executionOrder]() {
            executionOrder.push_back(3);
        }, now - 1s);

        manager.scheduleTask([&executionOrder]() {
            executionOrder.push_back(1);
        }, now - 3s);

        manager.scheduleTask([&executionOrder]() {
            executionOrder.push_back(2);
        }, now - 2s);

        manager.executeScheduledTasks();

        // Should execute in chronological order: 1, 2, 3
        REQUIRE(executionOrder.size() == 3);
        REQUIRE(executionOrder[0] == 1);
        REQUIRE(executionOrder[1] == 2);
        REQUIRE(executionOrder[2] == 3);
    }

    SECTION("Tasks with same execution time") {
        std::vector<int> executionOrder;
        auto now = std::chrono::steady_clock::now();
        auto sameTime = now - 1s;

        // Schedule multiple tasks for the same time
        for (int i = 1; i <= 5; ++i) {
            manager.scheduleTask([&executionOrder, i]() {
                executionOrder.push_back(i);
            }, sameTime);
        }

        manager.executeScheduledTasks();

        // All tasks should execute
        REQUIRE(executionOrder.size() == 5);
        
        // Order may vary for same-time tasks, but all should be present
        std::sort(executionOrder.begin(), executionOrder.end());
        for (int i = 1; i <= 5; ++i) {
            REQUIRE(executionOrder[i-1] == i);
        }
    }
}

TEST_CASE("ScheduledTaskManager task parameters and return values", "[scripts][scheduled_task_manager][parameters]") {
    ScheduledTaskManager manager;

    SECTION("Tasks can capture variables by reference") {
        int counter = 0;
        auto now = std::chrono::steady_clock::now();

        manager.scheduleTask([&counter]() {
            counter += 10;
        }, now - 1s);

        manager.scheduleTask([&counter]() {
            counter *= 2;
        }, now - 500ms);

        REQUIRE(counter == 0);
        manager.executeScheduledTasks();
        REQUIRE(counter == 20); // (0 + 10) * 2
    }

    SECTION("Tasks can capture variables by value") {
        int originalValue = 42;
        int result = 0;
        auto now = std::chrono::steady_clock::now();

        manager.scheduleTask([originalValue, &result]() {
            result = originalValue * 2;
        }, now - 1s);

        originalValue = 100; // Change original after scheduling

        manager.executeScheduledTasks();
        REQUIRE(result == 84); // Should use captured value (42), not current (100)
    }

    SECTION("Tasks can call member functions") {
        struct TestObject {
            int value = 0;
            void increment() { value++; }
            void multiply(int factor) { value *= factor; }
        };

        TestObject obj;
        auto now = std::chrono::steady_clock::now();

        manager.scheduleTask([&obj]() {
            obj.increment();
        }, now - 2s);

        manager.scheduleTask([&obj]() {
            obj.multiply(5);
        }, now - 1s);

        REQUIRE(obj.value == 0);
        manager.executeScheduledTasks();
        REQUIRE(obj.value == 5); // (0 + 1) * 5
    }
}

TEST_CASE("ScheduledTaskManager queue management", "[scripts][scheduled_task_manager][queue]") {
    ScheduledTaskManager manager;

    SECTION("clearQueue removes all tasks") {
        std::vector<bool> tasksExecuted(3, false);
        auto now = std::chrono::steady_clock::now();

        // Schedule multiple tasks
        for (int i = 0; i < 3; ++i) {
            manager.scheduleTask([&tasksExecuted, i]() {
                tasksExecuted[i] = true;
            }, now - 1s);
        }

        // Clear queue before execution
        manager.clearQueue();
        manager.executeScheduledTasks();

        // No tasks should execute
        for (bool executed : tasksExecuted) {
            REQUIRE(executed == false);
        }
    }

    SECTION("Tasks can be scheduled after clearQueue") {
        bool taskExecuted = false;
        auto now = std::chrono::steady_clock::now();

        // Schedule and clear
        manager.scheduleTask([]() {}, now - 1s);
        manager.clearQueue();

        // Schedule new task
        manager.scheduleTask([&taskExecuted]() {
            taskExecuted = true;
        }, now - 1s);

        manager.executeScheduledTasks();
        REQUIRE(taskExecuted == true);
    }
}

TEST_CASE("ScheduledTaskManager timing precision", "[scripts][scheduled_task_manager][timing]") {
    ScheduledTaskManager manager;

    SECTION("Tasks execute at precise times") {
        std::vector<std::chrono::steady_clock::time_point> executionTimes;
        auto now = std::chrono::steady_clock::now();

        // Schedule tasks at specific intervals
        for (int i = 0; i < 3; ++i) {
            auto scheduledTime = now - std::chrono::milliseconds(100 * (3 - i));
            manager.scheduleTask([&executionTimes]() {
                executionTimes.push_back(std::chrono::steady_clock::now());
            }, scheduledTime);
        }

        auto executionStart = std::chrono::steady_clock::now();
        manager.executeScheduledTasks();

        // All tasks should execute (they were scheduled in the past)
        REQUIRE(executionTimes.size() == 3);
        
        // All execution times should be after the execution start
        for (const auto& time : executionTimes) {
            REQUIRE(time >= executionStart);
        }
    }

    SECTION("Future tasks wait for their time") {
        bool taskExecuted = false;
        auto now = std::chrono::steady_clock::now();
        auto futureTime = now + 50ms;

        manager.scheduleTask([&taskExecuted]() {
            taskExecuted = true;
        }, futureTime);

        // Execute immediately - task should not run
        manager.executeScheduledTasks();
        REQUIRE(taskExecuted == false);

        // Wait for the scheduled time and try again
        std::this_thread::sleep_for(60ms);
        manager.executeScheduledTasks();
        REQUIRE(taskExecuted == true);
    }
}

TEST_CASE("ScheduledTaskManager stress testing", "[scripts][scheduled_task_manager][stress]") {
    ScheduledTaskManager manager;

    SECTION("Large number of tasks") {
        const int numTasks = 1000;
        std::atomic<int> executedCount{0};
        auto now = std::chrono::steady_clock::now();

        // Schedule many tasks
        for (int i = 0; i < numTasks; ++i) {
            manager.scheduleTask([&executedCount]() {
                executedCount++;
            }, now - 1s);
        }

        manager.executeScheduledTasks();
        REQUIRE(executedCount.load() == numTasks);
    }

    SECTION("Tasks with varying execution times") {
        const int numTasks = 100;
        std::vector<int> executionOrder;
        auto now = std::chrono::steady_clock::now();

        // Schedule tasks with random delays
        for (int i = 0; i < numTasks; ++i) {
            auto delay = std::chrono::milliseconds(i * 10);
            manager.scheduleTask([&executionOrder, i]() {
                executionOrder.push_back(i);
            }, now - delay);
        }

        manager.executeScheduledTasks();

        // All tasks should execute
        REQUIRE(executionOrder.size() == numTasks);

        // Should execute in reverse order (largest delay first)
        for (int i = 0; i < numTasks; ++i) {
            REQUIRE(executionOrder[i] == (numTasks - 1 - i));
        }
    }

    SECTION("Rapid scheduling and execution") {
        std::atomic<int> executedCount{0};
        auto now = std::chrono::steady_clock::now();

        // Rapidly schedule and execute tasks
        for (int cycle = 0; cycle < 10; ++cycle) {
            // Schedule 10 tasks
            for (int i = 0; i < 10; ++i) {
                manager.scheduleTask([&executedCount]() {
                    executedCount++;
                }, now - 1s);
            }

            // Execute them
            manager.executeScheduledTasks();
        }

        REQUIRE(executedCount.load() == 100);
    }
}

TEST_CASE("ScheduledTaskManager edge cases", "[scripts][scheduled_task_manager][edge_cases]") {
    ScheduledTaskManager manager;

    SECTION("Empty lambda tasks") {
        auto now = std::chrono::steady_clock::now();

        // Schedule empty tasks
        manager.scheduleTask([]() {}, now - 1s);
        manager.scheduleTask([]() {}, now - 1s);
        manager.scheduleTask([]() {}, now - 1s);

        // Should not crash
        REQUIRE_NOTHROW(manager.executeScheduledTasks());
    }

    SECTION("Tasks that throw exceptions") {
        bool taskAfterExceptionExecuted = false;
        auto now = std::chrono::steady_clock::now();

        // Schedule task that throws
        manager.scheduleTask([]() {
            throw std::runtime_error("Test exception");
        }, now - 2s);

        // Schedule task after the throwing one
        manager.scheduleTask([&taskAfterExceptionExecuted]() {
            taskAfterExceptionExecuted = true;
        }, now - 1s);

        // Execution should handle exceptions gracefully
        // Note: Current implementation doesn't handle exceptions, so this will throw
        REQUIRE_THROWS(manager.executeScheduledTasks());

        // The second task won't execute due to the exception
        REQUIRE(taskAfterExceptionExecuted == false);
    }

    SECTION("Tasks scheduled at exact same time point") {
        std::vector<int> executionOrder;
        auto now = std::chrono::steady_clock::now();
        auto exactTime = now - 1s;

        // Schedule multiple tasks at exactly the same time
        for (int i = 1; i <= 10; ++i) {
            manager.scheduleTask([&executionOrder, i]() {
                executionOrder.push_back(i);
            }, exactTime);
        }

        manager.executeScheduledTasks();

        // All tasks should execute
        REQUIRE(executionOrder.size() == 10);

        // All numbers 1-10 should be present (order may vary)
        std::sort(executionOrder.begin(), executionOrder.end());
        for (int i = 1; i <= 10; ++i) {
            REQUIRE(executionOrder[i-1] == i);
        }
    }

    SECTION("Very distant future and past times") {
        std::vector<bool> tasksExecuted(3, false);
        auto now = std::chrono::steady_clock::now();

        // Very distant past
        manager.scheduleTask([&tasksExecuted]() {
            tasksExecuted[0] = true;
        }, now - std::chrono::hours(24));

        // Very distant future
        manager.scheduleTask([&tasksExecuted]() {
            tasksExecuted[1] = true;
        }, now + std::chrono::hours(24));

        // Current time
        manager.scheduleTask([&tasksExecuted]() {
            tasksExecuted[2] = true;
        }, now);

        manager.executeScheduledTasks();

        // Only past and current should execute
        REQUIRE(tasksExecuted[0] == true);
        REQUIRE(tasksExecuted[1] == false);
        REQUIRE(tasksExecuted[2] == true);
    }
}

TEST_CASE("ScheduledTaskManager lifecycle and cleanup", "[scripts][scheduled_task_manager][lifecycle]") {
    SECTION("Destructor clears queue") {
        bool taskExecuted = false;
        auto now = std::chrono::steady_clock::now();

        {
            ScheduledTaskManager manager;
            manager.scheduleTask([&taskExecuted]() {
                taskExecuted = true;
            }, now + 1s); // Future task

            // Manager goes out of scope here
        }

        // Task should not execute after manager destruction
        REQUIRE(taskExecuted == false);
    }

    SECTION("Multiple managers are independent") {
        ScheduledTaskManager manager1;
        ScheduledTaskManager manager2;

        bool task1Executed = false;
        bool task2Executed = false;
        auto now = std::chrono::steady_clock::now();

        manager1.scheduleTask([&task1Executed]() {
            task1Executed = true;
        }, now - 1s);

        manager2.scheduleTask([&task2Executed]() {
            task2Executed = true;
        }, now - 1s);

        // Execute only manager1
        manager1.executeScheduledTasks();
        REQUIRE(task1Executed == true);
        REQUIRE(task2Executed == false);

        // Execute manager2
        manager2.executeScheduledTasks();
        REQUIRE(task2Executed == true);
    }

    SECTION("clearQueue can be called multiple times") {
        ScheduledTaskManager manager;
        auto now = std::chrono::steady_clock::now();

        // Schedule some tasks
        manager.scheduleTask([]() {}, now - 1s);
        manager.scheduleTask([]() {}, now - 1s);

        // Clear multiple times
        REQUIRE_NOTHROW(manager.clearQueue());
        REQUIRE_NOTHROW(manager.clearQueue());
        REQUIRE_NOTHROW(manager.clearQueue());

        // Should still work normally
        bool taskExecuted = false;
        manager.scheduleTask([&taskExecuted]() {
            taskExecuted = true;
        }, now - 1s);

        manager.executeScheduledTasks();
        REQUIRE(taskExecuted == true);
    }
}

TEST_CASE("ScheduledTaskManager real-world scenarios", "[scripts][scheduled_task_manager][real_world]") {
    ScheduledTaskManager manager;

    SECTION("Game loop with delayed actions") {
        struct GameState {
            int health = 100;
            int score = 0;
            bool powerUpActive = false;
        };

        GameState game;
        auto now = std::chrono::steady_clock::now();

        // Schedule damage over time
        manager.scheduleTask([&game]() {
            game.health -= 10;
        }, now + 100ms);

        manager.scheduleTask([&game]() {
            game.health -= 10;
        }, now + 200ms);

        // Schedule power-up activation
        manager.scheduleTask([&game]() {
            game.powerUpActive = true;
        }, now + 150ms);

        // Schedule score bonus (after power-up)
        manager.scheduleTask([&game]() {
            if (game.powerUpActive) {
                game.score += 100;
            }
        }, now + 250ms);

        // Simulate game loop
        for (int frame = 0; frame < 10; ++frame) {
            std::this_thread::sleep_for(30ms);
            manager.executeScheduledTasks();
        }

        // Verify final state
        REQUIRE(game.health == 80); // 100 - 10 - 10
        REQUIRE(game.powerUpActive == true);
        REQUIRE(game.score == 100);
    }

    SECTION("Animation system with keyframes") {
        struct AnimationState {
            float position = 0.0f;
            float scale = 1.0f;
            float rotation = 0.0f;
        };

        AnimationState anim;
        auto now = std::chrono::steady_clock::now();

        // Schedule keyframes for a 300ms animation
        manager.scheduleTask([&anim]() {
            anim.position = 25.0f;
            anim.scale = 1.2f;
        }, now + 100ms);

        manager.scheduleTask([&anim]() {
            anim.position = 75.0f;
            anim.rotation = 45.0f;
        }, now + 200ms);

        manager.scheduleTask([&anim]() {
            anim.position = 100.0f;
            anim.scale = 1.0f;
            anim.rotation = 90.0f;
        }, now + 300ms);

        // Simulate animation loop
        std::this_thread::sleep_for(350ms);
        manager.executeScheduledTasks();

        // Verify final animation state
        REQUIRE(anim.position == 100.0f);
        REQUIRE(anim.scale == 1.0f);
        REQUIRE(anim.rotation == 90.0f);
    }

    SECTION("Event scheduling system") {
        std::vector<std::string> eventLog;
        auto now = std::chrono::steady_clock::now();

        // Schedule a sequence of events
        manager.scheduleTask([&eventLog]() {
            eventLog.push_back("Player spawned");
        }, now + 50ms);

        manager.scheduleTask([&eventLog]() {
            eventLog.push_back("Enemy appeared");
        }, now + 100ms);

        manager.scheduleTask([&eventLog]() {
            eventLog.push_back("Combat started");
        }, now + 150ms);

        manager.scheduleTask([&eventLog]() {
            eventLog.push_back("Player victory");
        }, now + 200ms);

        manager.scheduleTask([&eventLog]() {
            eventLog.push_back("Rewards given");
        }, now + 250ms);

        // Execute events
        std::this_thread::sleep_for(300ms);
        manager.executeScheduledTasks();

        // Verify event sequence
        REQUIRE(eventLog.size() == 5);
        REQUIRE(eventLog[0] == "Player spawned");
        REQUIRE(eventLog[1] == "Enemy appeared");
        REQUIRE(eventLog[2] == "Combat started");
        REQUIRE(eventLog[3] == "Player victory");
        REQUIRE(eventLog[4] == "Rewards given");
    }
}

TEST_CASE("ScheduledTaskManager performance characteristics", "[scripts][scheduled_task_manager][performance]") {
    ScheduledTaskManager manager;

    SECTION("Task execution performance") {
        const int numTasks = 10000;
        std::atomic<int> executedCount{0};
        auto now = std::chrono::steady_clock::now();

        // Schedule many tasks
        auto scheduleStart = std::chrono::high_resolution_clock::now();
        for (int i = 0; i < numTasks; ++i) {
            manager.scheduleTask([&executedCount]() {
                executedCount++;
            }, now - 1s);
        }
        auto scheduleEnd = std::chrono::high_resolution_clock::now();

        // Execute all tasks
        auto executeStart = std::chrono::high_resolution_clock::now();
        manager.executeScheduledTasks();
        auto executeEnd = std::chrono::high_resolution_clock::now();

        // Verify all tasks executed
        REQUIRE(executedCount.load() == numTasks);

        // Performance should be reasonable (these are loose bounds)
        auto scheduleDuration = std::chrono::duration_cast<std::chrono::milliseconds>(scheduleEnd - scheduleStart);
        auto executeDuration = std::chrono::duration_cast<std::chrono::milliseconds>(executeEnd - executeStart);

        // Should complete within reasonable time (adjust if needed)
        REQUIRE(scheduleDuration.count() < 1000); // Less than 1 second to schedule 10k tasks
        REQUIRE(executeDuration.count() < 1000);  // Less than 1 second to execute 10k tasks
    }

    SECTION("Memory usage with many tasks") {
        const int numTasks = 1000;
        auto now = std::chrono::steady_clock::now();

        // Schedule many tasks for the future (they won't execute)
        for (int i = 0; i < numTasks; ++i) {
            manager.scheduleTask([i]() {
                // Capture some data to test memory usage
                volatile int data = i * i;
                (void)data; // Suppress unused variable warning
            }, now + std::chrono::hours(1));
        }

        // Clear queue to test memory cleanup
        manager.clearQueue();

        // Should be able to schedule new tasks after clearing
        bool taskExecuted = false;
        manager.scheduleTask([&taskExecuted]() {
            taskExecuted = true;
        }, now - 1s);

        manager.executeScheduledTasks();
        REQUIRE(taskExecuted == true);
    }

    SECTION("Repeated execution cycles") {
        std::atomic<int> totalExecuted{0};
        auto now = std::chrono::steady_clock::now();

        // Perform many schedule-execute cycles
        for (int cycle = 0; cycle < 100; ++cycle) {
            // Schedule 10 tasks per cycle
            for (int i = 0; i < 10; ++i) {
                manager.scheduleTask([&totalExecuted]() {
                    totalExecuted++;
                }, now - 1s);
            }

            // Execute tasks
            manager.executeScheduledTasks();
        }

        REQUIRE(totalExecuted.load() == 1000);
    }
}

TEST_CASE("ScheduledTaskManager Task struct functionality", "[scripts][scheduled_task_manager][task_struct]") {
    SECTION("Task comparison operator") {
        auto now = std::chrono::steady_clock::now();

        Task task1{[](){}, now + 1s};
        Task task2{[](){}, now + 2s};
        Task task3{[](){}, now + 1s};

        // Earlier tasks should be "greater" (for min-heap behavior in priority_queue)
        REQUIRE(task2 < task1); // task2 is later, so task2 < task1
        REQUIRE_FALSE(task1 < task2); // task1 is earlier, so NOT task1 < task2
        REQUIRE_FALSE(task1 < task3); // Same time, so neither is less than the other
        REQUIRE_FALSE(task3 < task1);
    }

    SECTION("Task with complex lambda") {
        struct ComplexData {
            std::vector<int> numbers;
            std::string message;

            void process() {
                for (auto& num : numbers) {
                    num *= 2;
                }
                message += " processed";
            }
        };

        ComplexData data{{1, 2, 3, 4, 5}, "Data"};
        auto now = std::chrono::steady_clock::now();

        ScheduledTaskManager manager;
        manager.scheduleTask([&data]() {
            data.process();
        }, now - 1s);

        manager.executeScheduledTasks();

        REQUIRE(data.numbers == std::vector<int>{2, 4, 6, 8, 10});
        REQUIRE(data.message == "Data processed");
    }
}
