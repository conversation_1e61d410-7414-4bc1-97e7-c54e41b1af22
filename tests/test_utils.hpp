#ifndef __IF__TEST_UTILS_HPP
#define __IF__TEST_UTILS_HPP

#include <glm/glm.hpp>
#include <glm/gtc/quaternion.hpp>
#include <cmath>

namespace IronFrost::TestUtils {

// Helper function to compare GLM vec2 with tolerance
inline bool isVec2Equal(const glm::vec2& a, const glm::vec2& b, float tolerance = 0.001f) {
    return glm::length(a - b) < tolerance;
}

// Helper function to compare GLM vec3 with tolerance
inline bool isVec3Equal(const glm::vec3& a, const glm::vec3& b, float tolerance = 0.001f) {
    return glm::length(a - b) < tolerance;
}

// Helper function to compare GLM vec4 with tolerance
inline bool isVec4Equal(const glm::vec4& a, const glm::vec4& b, float tolerance = 0.001f) {
    return glm::length(a - b) < tolerance;
}

// Helper function to compare GLM quaternions with tolerance
inline bool isQuatEqual(const glm::quat& a, const glm::quat& b, float tolerance = 0.001f) {
    return glm::length(a - b) < tolerance || glm::length(a + b) < tolerance; // Account for quaternion double cover
}

// Helper function to compare GLM mat3 with tolerance
inline bool isMat3Equal(const glm::mat3& a, const glm::mat3& b, float tolerance = 0.001f) {
    for (int i = 0; i < 3; ++i) {
        for (int j = 0; j < 3; ++j) {
            if (std::abs(a[i][j] - b[i][j]) > tolerance) {
                return false;
            }
        }
    }
    return true;
}

// Helper function to compare GLM mat4 with tolerance
inline bool isMat4Equal(const glm::mat4& a, const glm::mat4& b, float tolerance = 0.001f) {
    for (int i = 0; i < 4; ++i) {
        for (int j = 0; j < 4; ++j) {
            if (std::abs(a[i][j] - b[i][j]) > tolerance) {
                return false;
            }
        }
    }
    return true;
}

// Alias for backward compatibility and common usage
inline bool isApproxEqual(const glm::vec3& a, const glm::vec3& b, float tolerance = 0.001f) {
    return isVec3Equal(a, b, tolerance);
}

// Helper function to check if a float is approximately equal to another
inline bool isFloatEqual(float a, float b, float tolerance = 0.001f) {
    return std::abs(a - b) < tolerance;
}

// Alias for isFloatEqual for consistency with other test files
inline bool isApproximatelyEqual(float a, float b, float tolerance = 1e-6f) {
    return std::abs(a - b) < tolerance;
}

// Overload for vec2 comparison
inline bool isApproximatelyEqual(const glm::vec2& a, const glm::vec2& b, float tolerance = 1e-6f) {
    return isVec2Equal(a, b, tolerance);
}

// Overload for vec3 comparison
inline bool isApproximatelyEqual(const glm::vec3& a, const glm::vec3& b, float tolerance = 1e-6f) {
    return isVec3Equal(a, b, tolerance);
}

// Overload for vec4 comparison
inline bool isApproximatelyEqual(const glm::vec4& a, const glm::vec4& b, float tolerance = 1e-6f) {
    return isVec4Equal(a, b, tolerance);
}

// Helper function to check if a value is approximately zero
inline bool isApproxZero(float value, float tolerance = 0.001f) {
    return std::abs(value) < tolerance;
}

// Helper function to check if a vector is approximately zero
inline bool isVec3ApproxZero(const glm::vec3& vec, float tolerance = 0.001f) {
    return glm::length(vec) < tolerance;
}

/**
 * @brief Common widget property verification utilities
 * Note: These are simple macros to avoid template/forward declaration issues
 */

// Macro to check basic widget properties
#define VERIFY_BASIC_WIDGET_PROPERTIES(widget, expectedPos, expectedSize, expectedType, expectedVisible) \
    do { \
        REQUIRE((widget).getPosition() == (expectedPos)); \
        REQUIRE((widget).getSize() == (expectedSize)); \
        REQUIRE((widget).getType() == (expectedType)); \
        REQUIRE((widget).isVisible() == (expectedVisible)); \
    } while(0)

// Macro to check default widget state
#define VERIFY_DEFAULT_WIDGET_STATE(widget) \
    do { \
        REQUIRE((widget).getPosition() == glm::vec2(0.0f, 0.0f)); \
        REQUIRE((widget).getSize() == glm::vec2(10.0f, 10.0f)); \
        REQUIRE((widget).getRotation() == 0.0f); \
        REQUIRE((widget).isVisible() == true); \
        REQUIRE((widget).isDirty() == true); \
        REQUIRE_FALSE((widget).isHovered()); \
        REQUIRE_FALSE((widget).isPressed()); \
        REQUIRE_FALSE((widget).isFocused()); \
    } while(0)

// Macro to check default colorable properties
#define VERIFY_DEFAULT_COLORABLE_PROPERTIES(widget) \
    do { \
        const auto& color = (widget).getColor(); \
        REQUIRE(color.r == 1.0f); \
        REQUIRE(color.g == 1.0f); \
        REQUIRE(color.b == 1.0f); \
        REQUIRE((widget).getAlpha() == 1.0f); \
    } while(0)

// Helper function to check if a vector is normalized (length ≈ 1)
inline bool isVec3Normalized(const glm::vec3& vec, float tolerance = 0.001f) {
    return isFloatEqual(glm::length(vec), 1.0f, tolerance);
}

// Helper function to check if a quaternion is normalized (length ≈ 1)
inline bool isQuatNormalized(const glm::quat& quat, float tolerance = 0.001f) {
    return isFloatEqual(glm::length(quat), 1.0f, tolerance);
}

// Helper function to check if two vectors are orthogonal (dot product ≈ 0)
inline bool areVec3Orthogonal(const glm::vec3& a, const glm::vec3& b, float tolerance = 0.001f) {
    float dot = glm::dot(a, b);
    return isApproxZero(dot, tolerance);
}

} // namespace IronFrost::TestUtils

// Bring commonly used functions into global scope for convenience
using IronFrost::TestUtils::isVec2Equal;
using IronFrost::TestUtils::isVec3Equal;
using IronFrost::TestUtils::isVec4Equal;
using IronFrost::TestUtils::isQuatEqual;
using IronFrost::TestUtils::isMat3Equal;
using IronFrost::TestUtils::isMat4Equal;
using IronFrost::TestUtils::isApproxEqual;
using IronFrost::TestUtils::isFloatEqual;
using IronFrost::TestUtils::isVec3Normalized;
using IronFrost::TestUtils::areVec3Orthogonal;
using IronFrost::TestUtils::isApproximatelyEqual;

// Widget testing macros are available globally
// VERIFY_DEFAULT_WIDGET_STATE(widget)
// VERIFY_DEFAULT_COLORABLE_PROPERTIES(widget)
// VERIFY_BASIC_WIDGET_PROPERTIES(widget, pos, size, type, visible)

#endif // __IF__TEST_UTILS_HPP
