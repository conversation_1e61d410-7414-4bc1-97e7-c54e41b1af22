#ifndef __IF__MOCK_INPUT_HPP
#define __IF__MOCK_INPUT_HPP

// Local includes
#include "utils/game_context.hpp"
#include "window/window.hpp"

namespace IronFrost {
namespace Mocks {

/**
 * @brief Mock implementation of IKeyboard for testing
 * 
 * Provides a simple mock keyboard that can be used in unit tests.
 * By default, all keys are considered up/not pressed.
 */
class MockKeyboard : public IKeyboard {
public:
    MockKeyboard() = default;
    
    bool isKeyDown(KeyType key) const override {
        return false; // Simple implementation - all keys are up
    }
    
    bool isKeyUp(KeyType key) const override {
        return true; // Simple implementation - all keys are up
    }
};

/**
 * @brief Mock implementation of IMouse for testing
 * 
 * Provides a controllable mock mouse that can simulate mouse position
 * and button states for testing GUI interactions.
 */
class MockMouse : public IMouse {
private:
    double m_xpos{0.0};
    double m_ypos{0.0};
    double m_relative_xpos{0.0};
    double m_relative_ypos{0.0};
    bool m_leftPressed{false};
    bool m_rightPressed{false};
    bool m_middlePressed{false};

public:
    MockMouse() = default;
    
    /**
     * @brief Set the mock mouse position
     * @param x X coordinate
     * @param y Y coordinate
     */
    void setPosition(double x, double y) {
        // Calculate relative movement
        m_relative_xpos += x - m_xpos;
        m_relative_ypos += y - m_ypos;

        // Update absolute position
        m_xpos = x;
        m_ypos = y;
    }

    /**
     * @brief Set the mock mouse relative position directly (for testing)
     * @param x Relative X movement
     * @param y Relative Y movement
     */
    void setRelativePosition(double x, double y) {
        m_relative_xpos = x;
        m_relative_ypos = y;
    }
    
    /**
     * @brief Set the state of a mouse button
     * @param button The mouse button to set
     * @param pressed Whether the button is pressed (true) or released (false)
     */
    void setButtonPressed(MouseButtonType button, bool pressed) {
        switch (button) {
            case MOUSE_BUTTON_LEFT:
                m_leftPressed = pressed;
                break;
            case MOUSE_BUTTON_RIGHT:
                m_rightPressed = pressed;
                break;
            case MOUSE_BUTTON_MIDDLE:
                m_middlePressed = pressed;
                break;
            default:
                break;
        }
    }
    
    // IMouse interface implementation
    double getXPos() const override {
        return m_xpos;
    }
    
    double getYPos() const override {
        return m_ypos;
    }

    double getRelativeXPos() const override {
        return m_relative_xpos;
    }

    double getRelativeYPos() const override {
        return m_relative_ypos;
    }

    void resetRelativePosition() override {
        m_relative_xpos = 0.0;
        m_relative_ypos = 0.0;
    }

    bool isButtonPressed(MouseButtonType button) const override {
        switch (button) {
            case MOUSE_BUTTON_LEFT:
                return m_leftPressed;
            case MOUSE_BUTTON_RIGHT:
                return m_rightPressed;
            case MOUSE_BUTTON_MIDDLE:
                return m_middlePressed;
            default:
                return false;
        }
    }
    
    bool isButtonReleased(MouseButtonType button) const override {
        return !isButtonPressed(button);
    }

    void hideCursor() override {
        // Mock implementation - no-op
    }

    void showCursor() override {
        // Mock implementation - no-op
    }

    void toggleCursor() override {
        // Mock implementation - no-op
    }
};

/**
 * @brief Helper function to create a test GameContext with mock input devices
 * @param keyboard Reference to a mock keyboard
 * @param mouse Reference to a mock mouse
 * @return GameContext configured with the mock devices
 */
inline GameContext createTestGameContext(MockKeyboard& keyboard, MockMouse& mouse) {
    return GameContext{keyboard, mouse};
}

} // namespace Mocks
} // namespace IronFrost

#endif // __IF__MOCK_INPUT_HPP
