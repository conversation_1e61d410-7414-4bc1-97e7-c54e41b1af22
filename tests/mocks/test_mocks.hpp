#ifndef __IF__TEST_MOCKS_HPP
#define __IF__TEST_MOCKS_HPP

// Include all mock classes for easy access
#include "mock_input.hpp"
#include "mock_vfs.hpp"

namespace IronFrost {
namespace TestMocks {

// Re-export all mock classes for convenience
using Mocks::MockKeyboard;
using Mocks::MockMouse;
using Mocks::createTestGameContext;

// Additional test utilities that work with mocks
namespace Utils {

/**
 * @brief Create a standard test setup with mock input devices
 * @return Tuple of (MockKeyboard, MockMouse, GameContext)
 */
inline auto createStandardTestSetup() {
    auto keyboard = MockKeyboard{};
    auto mouse = MockMouse{};
    auto context = createTestGameContext(keyboard, mouse);
    return std::make_tuple(std::move(keyboard), std::move(mouse), std::move(context));
}

/**
 * @brief Setup mouse for widget interaction testing
 * @param mouse Mock mouse to configure
 * @param position Mouse position
 * @param leftPressed Whether left button is pressed
 * @param rightPressed Whether right button is pressed
 */
inline void setupMouseForTesting(MockMouse& mouse, 
                               const glm::vec2& position, 
                               bool leftPressed = false, 
                               bool rightPressed = false) {
    mouse.setPosition(position.x, position.y);
    mouse.setButtonPressed(MOUSE_BUTTON_LEFT, leftPressed);
    mouse.setButtonPressed(MOUSE_BUTTON_RIGHT, rightPressed);
}

/**
 * @brief Setup mouse to hover over a widget's center
 * @param mouse Mock mouse to configure
 * @param widgetPos Widget position
 * @param widgetSize Widget size
 */
inline void setupMouseHoverWidget(MockMouse& mouse, 
                                const glm::vec2& widgetPos, 
                                const glm::vec2& widgetSize) {
    glm::vec2 center = widgetPos + (widgetSize * 0.5f);
    mouse.setPosition(center.x, center.y);
}

/**
 * @brief Setup mouse to click on a widget's center
 * @param mouse Mock mouse to configure
 * @param widgetPos Widget position
 * @param widgetSize Widget size
 * @param button Mouse button to press (default: left)
 */
inline void setupMouseClickWidget(MockMouse& mouse, 
                                const glm::vec2& widgetPos, 
                                const glm::vec2& widgetSize,
                                MouseButtonType button = MOUSE_BUTTON_LEFT) {
    setupMouseHoverWidget(mouse, widgetPos, widgetSize);
    mouse.setButtonPressed(button, true);
}

} // namespace Utils

} // namespace TestMocks
} // namespace IronFrost

// Bring commonly used mock classes into global scope for convenience
using IronFrost::TestMocks::MockKeyboard;
using IronFrost::TestMocks::MockMouse;
using IronFrost::TestMocks::createTestGameContext;
using IronFrost::TestMocks::Utils::createStandardTestSetup;
using IronFrost::TestMocks::Utils::setupMouseForTesting;
using IronFrost::TestMocks::Utils::setupMouseHoverWidget;
using IronFrost::TestMocks::Utils::setupMouseClickWidget;

#endif // __IF__TEST_MOCKS_HPP
