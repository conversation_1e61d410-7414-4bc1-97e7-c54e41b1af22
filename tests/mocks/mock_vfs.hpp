#ifndef __IF__TESTS_MOCK_VFS_HPP
#define __IF__TESTS_MOCK_VFS_HPP

#include "vfs/vfs.hpp"

#include <string>
#include <vector>

namespace IronFrost {

/**
 * Mock VFS implementation for testing asset loaders.
 * Provides configurable behavior for testing various scenarios.
 */
class MockVFS : public IVFS {
private:
    std::vector<unsigned char> mockData_;
    bool shouldReturnEmpty_;
    std::string expectedPath_;
    
public:
    MockVFS() : shouldReturnEmpty_(false) {}
    
    /**
     * Set the mock binary data that will be returned by readFileBinary()
     */
    void setMockData(const std::vector<unsigned char>& data) {
        mockData_ = data;
    }
    
    /**
     * Configure whether readFileBinary() should return empty data
     */
    void setShouldReturnEmpty(bool empty) {
        shouldReturnEmpty_ = empty;
    }
    
    /**
     * Set the expected file path for validation
     * If set, readFileBinary() will only return data for this exact path
     */
    void setExpectedPath(const std::string& path) {
        expectedPath_ = path;
    }
    
    /**
     * Clear all configuration and reset to default state
     */
    void reset() {
        mockData_.clear();
        shouldReturnEmpty_ = false;
        expectedPath_.clear();
    }
    
    // IVFS interface implementation
    bool mount(const std::string&, const std::string&, bool) override { 
        return true; 
    }
    
    bool unmount(const std::string&) override { 
        return true; 
    }
    
    bool exists(const std::string&) const override { 
        return !shouldReturnEmpty_; 
    }
    
    std::string readFile(const std::string&) const override { 
        return ""; 
    }
    
    bool writeFile(const std::string&, const std::string&) override {
        return true;
    }

    bool writeFileBinary(const std::string&, const std::vector<unsigned char>&) override {
        return true;
    }
    
    std::vector<std::string> listFiles(const std::string&) const override { 
        return {}; 
    }
    
    bool setWriteDirectory(const std::string&) override { 
        return true; 
    }
    
    std::vector<unsigned char> readFileBinary(const std::string& path) const override {
        // Check if path matches expected path (if set)
        if (!expectedPath_.empty() && path != expectedPath_) {
            return {}; // Wrong path
        }
        
        // Return empty data if configured to do so
        if (shouldReturnEmpty_) {
            return {};
        }
        
        // Return the mock data
        return mockData_;
    }
};

} // namespace IronFrost

#endif // __IF__TESTS_MOCK_VFS_HPP
