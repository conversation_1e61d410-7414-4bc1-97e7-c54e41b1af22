#include <catch2/catch_test_macros.hpp>

// Local includes
#include "test_mocks.hpp"

using namespace IronFrost;

TEST_CASE("MockMouse basic functionality", "[mocks][mouse][basic]") {
    MockMouse mouse;

    SECTION("Initial state") {
        REQUIRE(mouse.getXPos() == 0.0);
        REQUIRE(mouse.getYPos() == 0.0);
        REQUIRE(mouse.getRelativeXPos() == 0.0);
        REQUIRE(mouse.getRelativeYPos() == 0.0);
        
        REQUIRE(mouse.isButtonReleased(MOUSE_BUTTON_LEFT) == true);
        REQUIRE(mouse.isButtonReleased(MOUSE_BUTTON_RIGHT) == true);
        REQUIRE(mouse.isButtonReleased(MOUSE_BUTTON_MIDDLE) == true);
        
        REQUIRE(mouse.isButtonPressed(MOUSE_BUTTON_LEFT) == false);
        REQUIRE(mouse.isButtonPressed(MOUSE_BUTTON_RIGHT) == false);
        REQUIRE(mouse.isButtonPressed(MOUSE_BUTTON_MIDDLE) == false);
    }

    SECTION("Position setting") {
        mouse.setPosition(100.0, 200.0);
        
        REQUIRE(mouse.getXPos() == 100.0);
        REQUIRE(mouse.getYPos() == 200.0);
    }

    SECTION("Button state management") {
        // Test left button
        mouse.setButtonPressed(MOUSE_BUTTON_LEFT, true);
        REQUIRE(mouse.isButtonPressed(MOUSE_BUTTON_LEFT) == true);
        REQUIRE(mouse.isButtonReleased(MOUSE_BUTTON_LEFT) == false);
        
        mouse.setButtonPressed(MOUSE_BUTTON_LEFT, false);
        REQUIRE(mouse.isButtonPressed(MOUSE_BUTTON_LEFT) == false);
        REQUIRE(mouse.isButtonReleased(MOUSE_BUTTON_LEFT) == true);
        
        // Test right button
        mouse.setButtonPressed(MOUSE_BUTTON_RIGHT, true);
        REQUIRE(mouse.isButtonPressed(MOUSE_BUTTON_RIGHT) == true);
        REQUIRE(mouse.isButtonReleased(MOUSE_BUTTON_RIGHT) == false);
        
        // Test middle button
        mouse.setButtonPressed(MOUSE_BUTTON_MIDDLE, true);
        REQUIRE(mouse.isButtonPressed(MOUSE_BUTTON_MIDDLE) == true);
        REQUIRE(mouse.isButtonReleased(MOUSE_BUTTON_MIDDLE) == false);
    }
}

TEST_CASE("MockMouse relative position functionality", "[mocks][mouse][relative]") {
    MockMouse mouse;

    SECTION("Initial relative position is zero") {
        REQUIRE(mouse.getRelativeXPos() == 0.0);
        REQUIRE(mouse.getRelativeYPos() == 0.0);
    }

    SECTION("Relative position tracks movement") {
        // Start at origin
        REQUIRE(mouse.getXPos() == 0.0);
        REQUIRE(mouse.getYPos() == 0.0);
        REQUIRE(mouse.getRelativeXPos() == 0.0);
        REQUIRE(mouse.getRelativeYPos() == 0.0);
        
        // Move to (10, 20)
        mouse.setPosition(10.0, 20.0);
        REQUIRE(mouse.getXPos() == 10.0);
        REQUIRE(mouse.getYPos() == 20.0);
        REQUIRE(mouse.getRelativeXPos() == 10.0);  // Moved 10 units right
        REQUIRE(mouse.getRelativeYPos() == 20.0);  // Moved 20 units down
        
        // Move to (5, 15) - should be relative to previous position
        mouse.setPosition(5.0, 15.0);
        REQUIRE(mouse.getXPos() == 5.0);
        REQUIRE(mouse.getYPos() == 15.0);
        REQUIRE(mouse.getRelativeXPos() == 5.0);   // 10 + (-5) = 5
        REQUIRE(mouse.getRelativeYPos() == 15.0);  // 20 + (-5) = 15
    }

    SECTION("Reset relative position") {
        // Move mouse and accumulate relative movement
        mouse.setPosition(50.0, 100.0);
        mouse.setPosition(75.0, 125.0);
        
        // Should have accumulated relative movement
        REQUIRE(mouse.getRelativeXPos() == 75.0);  // 50 + 25 = 75
        REQUIRE(mouse.getRelativeYPos() == 125.0); // 100 + 25 = 125
        
        // Reset relative position
        mouse.resetRelativePosition();
        REQUIRE(mouse.getRelativeXPos() == 0.0);
        REQUIRE(mouse.getRelativeYPos() == 0.0);
        
        // Absolute position should remain unchanged
        REQUIRE(mouse.getXPos() == 75.0);
        REQUIRE(mouse.getYPos() == 125.0);
    }

    SECTION("Direct relative position setting") {
        // Set relative position directly (for testing purposes)
        mouse.setRelativePosition(42.0, 84.0);
        
        REQUIRE(mouse.getRelativeXPos() == 42.0);
        REQUIRE(mouse.getRelativeYPos() == 84.0);
        
        // Absolute position should remain at origin
        REQUIRE(mouse.getXPos() == 0.0);
        REQUIRE(mouse.getYPos() == 0.0);
    }

    SECTION("Complex movement sequence") {
        // Simulate a complex movement pattern
        mouse.setPosition(0.0, 0.0);    // Start at origin
        REQUIRE(mouse.getRelativeXPos() == 0.0);
        REQUIRE(mouse.getRelativeYPos() == 0.0);
        
        mouse.setPosition(10.0, 5.0);   // Move right and down
        REQUIRE(mouse.getRelativeXPos() == 10.0);
        REQUIRE(mouse.getRelativeYPos() == 5.0);
        
        mouse.setPosition(8.0, 12.0);   // Move left and down
        REQUIRE(mouse.getRelativeXPos() == 8.0);   // 10 + (-2) = 8
        REQUIRE(mouse.getRelativeYPos() == 12.0);  // 5 + 7 = 12
        
        mouse.setPosition(15.0, 3.0);   // Move right and up
        REQUIRE(mouse.getRelativeXPos() == 15.0);  // 8 + 7 = 15
        REQUIRE(mouse.getRelativeYPos() == 3.0);   // 12 + (-9) = 3
        
        // Reset and verify
        mouse.resetRelativePosition();
        REQUIRE(mouse.getRelativeXPos() == 0.0);
        REQUIRE(mouse.getRelativeYPos() == 0.0);
        REQUIRE(mouse.getXPos() == 15.0);  // Absolute position unchanged
        REQUIRE(mouse.getYPos() == 3.0);
    }
}

TEST_CASE("MockMouse interface compliance", "[mocks][mouse][interface]") {
    MockMouse mouse;
    
    SECTION("IMouse interface methods work correctly") {
        // Test through the interface
        IMouse& iMouse = mouse;
        
        // Position methods
        REQUIRE(iMouse.getXPos() == 0.0);
        REQUIRE(iMouse.getYPos() == 0.0);
        REQUIRE(iMouse.getRelativeXPos() == 0.0);
        REQUIRE(iMouse.getRelativeYPos() == 0.0);
        
        // Button methods
        REQUIRE(iMouse.isButtonReleased(MOUSE_BUTTON_LEFT) == true);
        REQUIRE(iMouse.isButtonPressed(MOUSE_BUTTON_LEFT) == false);
        
        // Update state through concrete class
        mouse.setPosition(25.0, 50.0);
        mouse.setButtonPressed(MOUSE_BUTTON_LEFT, true);
        
        // Verify through interface
        REQUIRE(iMouse.getXPos() == 25.0);
        REQUIRE(iMouse.getYPos() == 50.0);
        REQUIRE(iMouse.getRelativeXPos() == 25.0);
        REQUIRE(iMouse.getRelativeYPos() == 50.0);
        REQUIRE(iMouse.isButtonPressed(MOUSE_BUTTON_LEFT) == true);
        REQUIRE(iMouse.isButtonReleased(MOUSE_BUTTON_LEFT) == false);
        
        // Test reset through interface
        iMouse.resetRelativePosition();
        REQUIRE(iMouse.getRelativeXPos() == 0.0);
        REQUIRE(iMouse.getRelativeYPos() == 0.0);
    }
}
