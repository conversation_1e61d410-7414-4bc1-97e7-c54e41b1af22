#include <catch2/catch_test_macros.hpp>
#include <catch2/catch_approx.hpp>

#include "utils/delta_time_calculator.hpp"

#include <chrono>
#include <thread>

using namespace IronFrost;

TEST_CASE("DeltaTimeCalculator basic functionality", "[utils][delta_time]") {
    DeltaTimeCalculator calculator;
    
    SECTION("Initial FPS should be 0") {
        REQUIRE(calculator.getFPS() == 0);
    }
    
    SECTION("Update callback receives delta time") {
        double receivedDeltaTime = 0.0;
        bool callbackCalled = false;
        
        calculator.update([&](double deltaTime) {
            receivedDeltaTime = deltaTime;
            callbackCalled = true;
        });
        
        REQUIRE(callbackCalled);
        REQUIRE(receivedDeltaTime > 0.0);
        REQUIRE(receivedDeltaTime < 1.0); // Should be a reasonable delta time
    }
    
    SECTION("Multiple updates provide reasonable delta times") {
        std::vector<double> deltaTimes;
        
        // First update
        calculator.update([&](double deltaTime) {
            deltaTimes.push_back(deltaTime);
        });
        
        // Small delay
        std::this_thread::sleep_for(std::chrono::milliseconds(10));
        
        // Second update
        calculator.update([&](double deltaTime) {
            deltaTimes.push_back(deltaTime);
        });
        
        REQUIRE(deltaTimes.size() == 2);
        REQUIRE(deltaTimes[0] > 0.0);
        REQUIRE(deltaTimes[1] > 0.0);
        
        // Second delta time should be roughly 10ms (0.01 seconds) or more
        REQUIRE(deltaTimes[1] >= 0.008); // Allow some tolerance
    }
}

TEST_CASE("DeltaTimeCalculator FPS calculation", "[utils][delta_time][fps]") {
    DeltaTimeCalculator calculator;

    SECTION("FPS calculation after one second") {
        int initialFPS = calculator.getFPS();
        REQUIRE(initialFPS == 0);

        // Simulate updates at roughly 60 FPS for just over 1 second
        const int targetFPS = 60;
        const double frameTime = 1.0 / targetFPS; // ~16.67ms per frame
        const int totalFrames = targetFPS + 5; // Run for slightly over 1 second

        int callbackCount = 0;
        double totalDeltaTime = 0.0;

        auto startTime = std::chrono::high_resolution_clock::now();

        for (int i = 0; i < totalFrames; ++i) {
            calculator.update([&](double deltaTime) {
                callbackCount++;
                totalDeltaTime += deltaTime;
            });

            // Sleep to simulate frame timing (slightly less than target to account for processing time)
            std::this_thread::sleep_for(std::chrono::milliseconds(static_cast<int>(frameTime * 1000 * 0.9)));
        }

        auto endTime = std::chrono::high_resolution_clock::now();
        auto actualDuration = std::chrono::duration<double>(endTime - startTime).count();

        // Verify we ran for over 1 second
        REQUIRE(actualDuration > 1.0);

        // Verify callback was called for each update
        REQUIRE(callbackCount == totalFrames);

        // After running for over 1 second, FPS should be calculated and non-zero
        int finalFPS = calculator.getFPS();
        REQUIRE(finalFPS > 0);

        // FPS should be reasonable (not wildly off from our target)
        // Allow for some variance due to timing precision and test environment
        REQUIRE(finalFPS >= 55); // At least 55 FPS
        REQUIRE(finalFPS <= 65); // At most 65 FPS (reasonable upper bound)

        // Total delta time should roughly match actual elapsed time
        REQUIRE(totalDeltaTime > 0.9);  // Should be close to actual duration
        REQUIRE(totalDeltaTime < 1.2);  // But not wildly off
    }

    SECTION("FPS remains 0 before one second threshold") {
        int initialFPS = calculator.getFPS();
        REQUIRE(initialFPS == 0);

        // Run for less than 1 second
        for (int i = 0; i < 10; ++i) {
            calculator.update([](double deltaTime) {});

            // Short delay - total will be less than 1 second
            std::this_thread::sleep_for(std::chrono::milliseconds(50));
        }

        // FPS should still be 0 since we haven't hit the 1-second threshold
        REQUIRE(calculator.getFPS() == 0);
    }
}
