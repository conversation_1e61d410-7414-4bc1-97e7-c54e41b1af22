#include <catch2/catch_test_macros.hpp>
#include <catch2/catch_approx.hpp>

#include "utils/assimp_glm_utils.hpp"

#include <assimp/matrix4x4.h>
#include <assimp/vector3.h>
#include <glm/glm.hpp>
#include <glm/gtc/matrix_transform.hpp>

using namespace IronFrost;
using Catch::Approx;

TEST_CASE("convertAssimpMatrix functionality", "[utils][assimp][glm][matrix]") {
    
    SECTION("Identity matrix conversion") {
        aiMatrix4x4 assimpIdentity;
        // aiMatrix4x4 constructor creates identity matrix by default
        
        glm::mat4 glmResult = convertAssimpMatrix(assimpIdentity);
        glm::mat4 glmIdentity = glm::mat4(1.0f);
        
        // Check each element of the identity matrix
        for (int i = 0; i < 4; ++i) {
            for (int j = 0; j < 4; ++j) {
                REQUIRE(glmResult[i][j] == Approx(glmIdentity[i][j]).epsilon(0.0001f));
            }
        }
    }
    
    SECTION("Custom matrix conversion") {
        aiMatrix4x4 assimpMatrix(
            1.0f, 2.0f, 3.0f, 4.0f,
            5.0f, 6.0f, 7.0f, 8.0f,
            9.0f, 10.0f, 11.0f, 12.0f,
            13.0f, 14.0f, 15.0f, 16.0f
        );
        
        glm::mat4 glmResult = convertAssimpMatrix(assimpMatrix);
        
        // Assimp matrices are row-major, GLM matrices are column-major
        // The function should transpose the matrix during conversion
        // Expected result after transpose:
        glm::mat4 expected(
            1.0f, 5.0f, 9.0f, 13.0f,   // First column
            2.0f, 6.0f, 10.0f, 14.0f,  // Second column
            3.0f, 7.0f, 11.0f, 15.0f,  // Third column
            4.0f, 8.0f, 12.0f, 16.0f   // Fourth column
        );
        
        for (int i = 0; i < 4; ++i) {
            for (int j = 0; j < 4; ++j) {
                REQUIRE(glmResult[i][j] == Approx(expected[i][j]).epsilon(0.0001f));
            }
        }
    }
    
    SECTION("Translation matrix conversion") {
        aiMatrix4x4 assimpTranslation;
        assimpTranslation.Translation(aiVector3D(10.0f, 20.0f, 30.0f), assimpTranslation);
        
        glm::mat4 glmResult = convertAssimpMatrix(assimpTranslation);
        glm::mat4 glmTranslation = glm::translate(glm::mat4(1.0f), glm::vec3(10.0f, 20.0f, 30.0f));
        
        // Check translation components (last column in GLM)
        REQUIRE(glmResult[3][0] == Approx(glmTranslation[3][0]).epsilon(0.0001f));
        REQUIRE(glmResult[3][1] == Approx(glmTranslation[3][1]).epsilon(0.0001f));
        REQUIRE(glmResult[3][2] == Approx(glmTranslation[3][2]).epsilon(0.0001f));
        REQUIRE(glmResult[3][3] == Approx(glmTranslation[3][3]).epsilon(0.0001f));
    }
    
    SECTION("Scaling matrix conversion") {
        aiMatrix4x4 assimpScaling;
        assimpScaling.Scaling(aiVector3D(2.0f, 3.0f, 4.0f), assimpScaling);
        
        glm::mat4 glmResult = convertAssimpMatrix(assimpScaling);
        glm::mat4 glmScaling = glm::scale(glm::mat4(1.0f), glm::vec3(2.0f, 3.0f, 4.0f));
        
        // Check diagonal elements (scaling factors)
        REQUIRE(glmResult[0][0] == Approx(glmScaling[0][0]).epsilon(0.0001f));
        REQUIRE(glmResult[1][1] == Approx(glmScaling[1][1]).epsilon(0.0001f));
        REQUIRE(glmResult[2][2] == Approx(glmScaling[2][2]).epsilon(0.0001f));
        REQUIRE(glmResult[3][3] == Approx(glmScaling[3][3]).epsilon(0.0001f));
    }
    
    SECTION("Zero matrix conversion") {
        aiMatrix4x4 assimpZero(
            0.0f, 0.0f, 0.0f, 0.0f,
            0.0f, 0.0f, 0.0f, 0.0f,
            0.0f, 0.0f, 0.0f, 0.0f,
            0.0f, 0.0f, 0.0f, 0.0f
        );
        
        glm::mat4 glmResult = convertAssimpMatrix(assimpZero);
        
        // All elements should be zero
        for (int i = 0; i < 4; ++i) {
            for (int j = 0; j < 4; ++j) {
                REQUIRE(glmResult[i][j] == Approx(0.0f).epsilon(0.0001f));
            }
        }
    }
}

TEST_CASE("convertAssimpVector functionality", "[utils][assimp][glm][vector]") {
    
    SECTION("Basic vector conversion") {
        aiVector3D assimpVec(1.0f, 2.0f, 3.0f);
        glm::vec3 glmResult = convertAssimpVector(assimpVec);
        
        REQUIRE(glmResult.x == Approx(1.0f).epsilon(0.0001f));
        REQUIRE(glmResult.y == Approx(2.0f).epsilon(0.0001f));
        REQUIRE(glmResult.z == Approx(3.0f).epsilon(0.0001f));
    }
    
    SECTION("Zero vector conversion") {
        aiVector3D assimpZero(0.0f, 0.0f, 0.0f);
        glm::vec3 glmResult = convertAssimpVector(assimpZero);
        
        REQUIRE(glmResult.x == Approx(0.0f).epsilon(0.0001f));
        REQUIRE(glmResult.y == Approx(0.0f).epsilon(0.0001f));
        REQUIRE(glmResult.z == Approx(0.0f).epsilon(0.0001f));
    }
    
    SECTION("Negative values conversion") {
        aiVector3D assimpNegative(-5.5f, -10.25f, -15.75f);
        glm::vec3 glmResult = convertAssimpVector(assimpNegative);
        
        REQUIRE(glmResult.x == Approx(-5.5f).epsilon(0.0001f));
        REQUIRE(glmResult.y == Approx(-10.25f).epsilon(0.0001f));
        REQUIRE(glmResult.z == Approx(-15.75f).epsilon(0.0001f));
    }
    
    SECTION("Large values conversion") {
        aiVector3D assimpLarge(1000000.0f, -999999.0f, 500000.5f);
        glm::vec3 glmResult = convertAssimpVector(assimpLarge);
        
        REQUIRE(glmResult.x == Approx(1000000.0f).epsilon(0.1f));
        REQUIRE(glmResult.y == Approx(-999999.0f).epsilon(0.1f));
        REQUIRE(glmResult.z == Approx(500000.5f).epsilon(0.1f));
    }
    
    SECTION("Unit vectors conversion") {
        // Test X unit vector
        aiVector3D assimpUnitX(1.0f, 0.0f, 0.0f);
        glm::vec3 glmUnitX = convertAssimpVector(assimpUnitX);
        REQUIRE(glmUnitX.x == Approx(1.0f).epsilon(0.0001f));
        REQUIRE(glmUnitX.y == Approx(0.0f).epsilon(0.0001f));
        REQUIRE(glmUnitX.z == Approx(0.0f).epsilon(0.0001f));
        
        // Test Y unit vector
        aiVector3D assimpUnitY(0.0f, 1.0f, 0.0f);
        glm::vec3 glmUnitY = convertAssimpVector(assimpUnitY);
        REQUIRE(glmUnitY.x == Approx(0.0f).epsilon(0.0001f));
        REQUIRE(glmUnitY.y == Approx(1.0f).epsilon(0.0001f));
        REQUIRE(glmUnitY.z == Approx(0.0f).epsilon(0.0001f));
        
        // Test Z unit vector
        aiVector3D assimpUnitZ(0.0f, 0.0f, 1.0f);
        glm::vec3 glmUnitZ = convertAssimpVector(assimpUnitZ);
        REQUIRE(glmUnitZ.x == Approx(0.0f).epsilon(0.0001f));
        REQUIRE(glmUnitZ.y == Approx(0.0f).epsilon(0.0001f));
        REQUIRE(glmUnitZ.z == Approx(1.0f).epsilon(0.0001f));
    }
}

TEST_CASE("Matrix-Vector integration tests", "[utils][assimp][glm][integration]") {
    
    SECTION("Transform vector using converted matrix") {
        // Create a translation matrix in Assimp
        aiMatrix4x4 assimpTransform;
        assimpTransform.Translation(aiVector3D(5.0f, 10.0f, 15.0f), assimpTransform);
        
        // Convert to GLM
        glm::mat4 glmTransform = convertAssimpMatrix(assimpTransform);
        
        // Create a test vector
        aiVector3D assimpVec(1.0f, 2.0f, 3.0f);
        glm::vec3 glmVec = convertAssimpVector(assimpVec);
        
        // Transform the vector
        glm::vec4 transformedVec = glmTransform * glm::vec4(glmVec, 1.0f);
        
        // Expected result: original vector + translation
        REQUIRE(transformedVec.x == Approx(6.0f).epsilon(0.0001f));  // 1 + 5
        REQUIRE(transformedVec.y == Approx(12.0f).epsilon(0.0001f)); // 2 + 10
        REQUIRE(transformedVec.z == Approx(18.0f).epsilon(0.0001f)); // 3 + 15
        REQUIRE(transformedVec.w == Approx(1.0f).epsilon(0.0001f));
    }
}
