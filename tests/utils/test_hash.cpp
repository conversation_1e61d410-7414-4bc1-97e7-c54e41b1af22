#include <catch2/catch_test_macros.hpp>

#include "utils/hash.hpp"
#include "../test_utils.hpp"

#include <unordered_set>
#include <vector>

TEST_CASE("hash_combine basic functionality", "[utils][hash][combine]") {
    SECTION("Combining different values produces different results") {
        std::size_t seed1 = 0;
        std::size_t seed2 = 0;
        
        std::hash_combine(seed1, std::hash<int>()(42));
        std::hash_combine(seed2, std::hash<int>()(84));
        
        REQUIRE(seed1 != seed2);
        REQUIRE(seed1 != 0); // Should have changed from initial value
        REQUIRE(seed2 != 0); // Should have changed from initial value
    }
    
    SECTION("Same values produce same results") {
        std::size_t seed1 = 0;
        std::size_t seed2 = 0;
        
        std::hash_combine(seed1, std::hash<int>()(42));
        std::hash_combine(seed2, std::hash<int>()(42));
        
        REQUIRE(seed1 == seed2);
    }
    
    SECTION("Different initial seeds produce different results") {
        std::size_t seed1 = 123;
        std::size_t seed2 = 456;
        
        std::hash_combine(seed1, std::hash<int>()(42));
        std::hash_combine(seed2, std::hash<int>()(42));
        
        REQUIRE(seed1 != seed2);
    }
    
    SECTION("Order matters") {
        std::size_t seed1 = 0;
        std::size_t seed2 = 0;
        
        // Combine in different orders
        std::hash_combine(seed1, std::hash<int>()(42));
        std::hash_combine(seed1, std::hash<int>()(84));
        
        std::hash_combine(seed2, std::hash<int>()(84));
        std::hash_combine(seed2, std::hash<int>()(42));
        
        REQUIRE(seed1 != seed2);
    }
}

TEST_CASE("hash_combine with different types", "[utils][hash][combine][types]") {
    SECTION("Combining integers") {
        std::size_t seed = 0;
        std::hash_combine(seed, std::hash<int>()(42));
        std::hash_combine(seed, std::hash<int>()(-17));
        std::hash_combine(seed, std::hash<int>()(0));
        
        REQUIRE(seed != 0);
    }
    
    SECTION("Combining strings") {
        std::size_t seed = 0;
        std::hash_combine(seed, std::hash<std::string>()("hello"));
        std::hash_combine(seed, std::hash<std::string>()("world"));
        
        REQUIRE(seed != 0);
    }
    
    SECTION("Combining mixed types") {
        std::size_t seed = 0;
        std::hash_combine(seed, std::hash<int>()(42));
        std::hash_combine(seed, std::hash<std::string>()("test"));
        std::hash_combine(seed, std::hash<double>()(3.14));
        
        REQUIRE(seed != 0);
    }
    
    SECTION("Combining floating point values") {
        std::size_t seed1 = 0;
        std::size_t seed2 = 0;
        
        std::hash_combine(seed1, std::hash<float>()(3.14f));
        std::hash_combine(seed2, std::hash<float>()(2.71f));
        
        REQUIRE(seed1 != seed2);
        REQUIRE(seed1 != 0);
        REQUIRE(seed2 != 0);
    }
}

TEST_CASE("hash_combine multiple combinations", "[utils][hash][combine][multiple]") {
    SECTION("Multiple sequential combinations") {
        std::size_t seed = 0;
        std::vector<int> values = {1, 2, 3, 4, 5};
        
        for (int value : values) {
            std::hash_combine(seed, std::hash<int>()(value));
        }
        
        REQUIRE(seed != 0);
        
        // Same sequence should produce same result
        std::size_t seed2 = 0;
        for (int value : values) {
            std::hash_combine(seed2, std::hash<int>()(value));
        }
        
        REQUIRE(seed == seed2);
    }
    
    SECTION("Empty combinations") {
        std::size_t seed = 42;
        std::size_t originalSeed = seed;
        
        // Combining with hash of 0 should still change the seed
        std::hash_combine(seed, 0);
        
        REQUIRE(seed != originalSeed);
    }
}

TEST_CASE("hash_combine practical usage", "[utils][hash][combine][practical]") {
    // Example of using hash_combine to create a hash for a pair-like structure
    struct Point {
        int x, y;
        
        Point(int x, int y) : x(x), y(y) {}
        
        bool operator==(const Point& other) const {
            return x == other.x && y == other.y;
        }
    };
    
    // Custom hash function using hash_combine
    struct PointHash {
        std::size_t operator()(const Point& p) const {
            std::size_t seed = 0;
            std::hash_combine(seed, std::hash<int>()(p.x));
            std::hash_combine(seed, std::hash<int>()(p.y));
            return seed;
        }
    };
    
    SECTION("Using hash_combine in unordered_set") {
        std::unordered_set<Point, PointHash> pointSet;
        
        Point p1(1, 2);
        Point p2(3, 4);
        Point p3(1, 2); // Same as p1
        
        pointSet.insert(p1);
        pointSet.insert(p2);
        pointSet.insert(p3); // Should not increase size due to duplicate
        
        REQUIRE(pointSet.size() == 2);
        REQUIRE(pointSet.count(p1) == 1);
        REQUIRE(pointSet.count(p2) == 1);
        REQUIRE(pointSet.count(p3) == 1); // Same as p1
    }
    
    SECTION("Hash consistency") {
        Point p1(10, 20);
        Point p2(10, 20);
        
        PointHash hasher;
        REQUIRE(hasher(p1) == hasher(p2));
    }
    
    SECTION("Hash distribution") {
        PointHash hasher;
        std::unordered_set<std::size_t> hashes;
        
        // Generate hashes for different points
        for (int x = 0; x < 10; ++x) {
            for (int y = 0; y < 10; ++y) {
                Point p(x, y);
                hashes.insert(hasher(p));
            }
        }
        
        // Should have good distribution (most hashes should be unique)
        // With 100 points, we expect close to 100 unique hashes
        REQUIRE(hashes.size() > 90); // Allow some collisions but expect good distribution
    }
}

TEST_CASE("hash_combine edge cases", "[utils][hash][combine][edge_cases]") {
    SECTION("Maximum size_t values") {
        std::size_t seed = std::numeric_limits<std::size_t>::max();
        std::size_t originalSeed = seed;
        
        std::hash_combine(seed, std::numeric_limits<std::size_t>::max());
        
        REQUIRE(seed != originalSeed); // Should handle overflow gracefully
    }
    
    SECTION("Zero values") {
        std::size_t seed1 = 0;
        std::size_t seed2 = 0;
        
        std::hash_combine(seed1, 0);
        std::hash_combine(seed2, 0);
        
        REQUIRE(seed1 == seed2);
        REQUIRE(seed1 != 0); // Should change even with zero input
    }
    
    SECTION("Repeated combinations with same value") {
        std::size_t seed = 0;
        std::size_t value = std::hash<int>()(42);
        
        std::hash_combine(seed, value);
        std::size_t afterFirst = seed;
        
        std::hash_combine(seed, value);
        std::size_t afterSecond = seed;
        
        std::hash_combine(seed, value);
        std::size_t afterThird = seed;
        
        // Each combination should produce different results
        REQUIRE(afterFirst != afterSecond);
        REQUIRE(afterSecond != afterThird);
        REQUIRE(afterFirst != afterThird);
    }
}
