#include <catch2/catch_test_macros.hpp>
#include <catch2/catch_approx.hpp>
#include <catch2/matchers/catch_matchers_exception.hpp>

#include "utils/json_serializers.hpp"

#include <glm/glm.hpp>
#include <nlohmann/json.hpp>
#include <stdexcept>

using namespace nlohmann;
using Catch::Approx;

TEST_CASE("glm::vec4 JSON deserialization", "[utils][json][glm][vec4]") {
    
    SECTION("Valid vec4 deserialization") {
        json j = {1.0f, 2.5f, -3.75f, 4.25f};
        glm::vec4 result = j.get<glm::vec4>();
        
        REQUIRE(result.x == Approx(1.0f).epsilon(0.0001f));
        REQUIRE(result.y == Approx(2.5f).epsilon(0.0001f));
        REQUIRE(result.z == Approx(-3.75f).epsilon(0.0001f));
        REQUIRE(result.w == Approx(4.25f).epsilon(0.0001f));
    }
    
    SECTION("Zero vec4 deserialization") {
        json j = {0.0f, 0.0f, 0.0f, 0.0f};
        glm::vec4 result = j.get<glm::vec4>();
        
        REQUIRE(result.x == Approx(0.0f).epsilon(0.0001f));
        REQUIRE(result.y == Approx(0.0f).epsilon(0.0001f));
        REQUIRE(result.z == Approx(0.0f).epsilon(0.0001f));
        REQUIRE(result.w == Approx(0.0f).epsilon(0.0001f));
    }
    
    SECTION("Large values vec4 deserialization") {
        json j = {1000000.0f, -999999.5f, 500000.25f, -250000.125f};
        glm::vec4 result = j.get<glm::vec4>();
        
        REQUIRE(result.x == Approx(1000000.0f).epsilon(0.1f));
        REQUIRE(result.y == Approx(-999999.5f).epsilon(0.1f));
        REQUIRE(result.z == Approx(500000.25f).epsilon(0.1f));
        REQUIRE(result.w == Approx(-250000.125f).epsilon(0.1f));
    }
    
    SECTION("Integer values converted to float") {
        json j = {1, 2, 3, 4};
        glm::vec4 result = j.get<glm::vec4>();
        
        REQUIRE(result.x == Approx(1.0f).epsilon(0.0001f));
        REQUIRE(result.y == Approx(2.0f).epsilon(0.0001f));
        REQUIRE(result.z == Approx(3.0f).epsilon(0.0001f));
        REQUIRE(result.w == Approx(4.0f).epsilon(0.0001f));
    }
    
    SECTION("Error: Wrong array size (too few elements)") {
        json j = {1.0f, 2.0f, 3.0f}; // Only 3 elements

        REQUIRE_THROWS_AS(j.get<glm::vec4>(), std::runtime_error);

        try {
            j.get<glm::vec4>();
            FAIL("Expected exception was not thrown");
        } catch (const std::runtime_error& e) {
            REQUIRE(std::string(e.what()) == "Expected JSON array of size 4 for glm::vec4");
        }
    }

    SECTION("Error: Wrong array size (too many elements)") {
        json j = {1.0f, 2.0f, 3.0f, 4.0f, 5.0f}; // 5 elements

        REQUIRE_THROWS_AS(j.get<glm::vec4>(), std::runtime_error);

        try {
            j.get<glm::vec4>();
            FAIL("Expected exception was not thrown");
        } catch (const std::runtime_error& e) {
            REQUIRE(std::string(e.what()) == "Expected JSON array of size 4 for glm::vec4");
        }
    }

    SECTION("Error: Not an array") {
        json j = "not an array";

        REQUIRE_THROWS_AS(j.get<glm::vec4>(), std::runtime_error);

        try {
            j.get<glm::vec4>();
            FAIL("Expected exception was not thrown");
        } catch (const std::runtime_error& e) {
            REQUIRE(std::string(e.what()) == "Expected JSON array of size 4 for glm::vec4");
        }
    }

    SECTION("Error: Empty array") {
        json j = json::array();

        REQUIRE_THROWS_AS(j.get<glm::vec4>(), std::runtime_error);

        try {
            j.get<glm::vec4>();
            FAIL("Expected exception was not thrown");
        } catch (const std::runtime_error& e) {
            REQUIRE(std::string(e.what()) == "Expected JSON array of size 4 for glm::vec4");
        }
    }
}

TEST_CASE("glm::vec3 JSON deserialization", "[utils][json][glm][vec3]") {
    
    SECTION("Valid vec3 deserialization") {
        json j = {1.5f, -2.25f, 3.75f};
        glm::vec3 result = j.get<glm::vec3>();
        
        REQUIRE(result.x == Approx(1.5f).epsilon(0.0001f));
        REQUIRE(result.y == Approx(-2.25f).epsilon(0.0001f));
        REQUIRE(result.z == Approx(3.75f).epsilon(0.0001f));
    }
    
    SECTION("Zero vec3 deserialization") {
        json j = {0.0f, 0.0f, 0.0f};
        glm::vec3 result = j.get<glm::vec3>();
        
        REQUIRE(result.x == Approx(0.0f).epsilon(0.0001f));
        REQUIRE(result.y == Approx(0.0f).epsilon(0.0001f));
        REQUIRE(result.z == Approx(0.0f).epsilon(0.0001f));
    }
    
    SECTION("Unit vectors deserialization") {
        // X unit vector
        json jx = {1.0f, 0.0f, 0.0f};
        glm::vec3 unitX = jx.get<glm::vec3>();
        REQUIRE(unitX.x == Approx(1.0f).epsilon(0.0001f));
        REQUIRE(unitX.y == Approx(0.0f).epsilon(0.0001f));
        REQUIRE(unitX.z == Approx(0.0f).epsilon(0.0001f));
        
        // Y unit vector
        json jy = {0.0f, 1.0f, 0.0f};
        glm::vec3 unitY = jy.get<glm::vec3>();
        REQUIRE(unitY.x == Approx(0.0f).epsilon(0.0001f));
        REQUIRE(unitY.y == Approx(1.0f).epsilon(0.0001f));
        REQUIRE(unitY.z == Approx(0.0f).epsilon(0.0001f));
        
        // Z unit vector
        json jz = {0.0f, 0.0f, 1.0f};
        glm::vec3 unitZ = jz.get<glm::vec3>();
        REQUIRE(unitZ.x == Approx(0.0f).epsilon(0.0001f));
        REQUIRE(unitZ.y == Approx(0.0f).epsilon(0.0001f));
        REQUIRE(unitZ.z == Approx(1.0f).epsilon(0.0001f));
    }
    
    SECTION("Error: Wrong array size (too few elements)") {
        json j = {1.0f, 2.0f}; // Only 2 elements

        REQUIRE_THROWS_AS(j.get<glm::vec3>(), std::runtime_error);

        try {
            j.get<glm::vec3>();
            FAIL("Expected exception was not thrown");
        } catch (const std::runtime_error& e) {
            REQUIRE(std::string(e.what()) == "Expected JSON array of size 3 for glm::vec3");
        }
    }

    SECTION("Error: Wrong array size (too many elements)") {
        json j = {1.0f, 2.0f, 3.0f, 4.0f}; // 4 elements

        REQUIRE_THROWS_AS(j.get<glm::vec3>(), std::runtime_error);

        try {
            j.get<glm::vec3>();
            FAIL("Expected exception was not thrown");
        } catch (const std::runtime_error& e) {
            REQUIRE(std::string(e.what()) == "Expected JSON array of size 3 for glm::vec3");
        }
    }

    SECTION("Error: Not an array") {
        json j = json::object({{"x", 1.0f}, {"y", 2.0f}, {"z", 3.0f}}); // Object instead of array

        REQUIRE_THROWS_AS(j.get<glm::vec3>(), std::runtime_error);

        try {
            j.get<glm::vec3>();
            FAIL("Expected exception was not thrown");
        } catch (const std::runtime_error& e) {
            REQUIRE(std::string(e.what()) == "Expected JSON array of size 3 for glm::vec3");
        }
    }
}

TEST_CASE("glm::vec2 JSON deserialization", "[utils][json][glm][vec2]") {
    
    SECTION("Valid vec2 deserialization") {
        json j = {-5.5f, 10.25f};
        glm::vec2 result = j.get<glm::vec2>();
        
        REQUIRE(result.x == Approx(-5.5f).epsilon(0.0001f));
        REQUIRE(result.y == Approx(10.25f).epsilon(0.0001f));
    }
    
    SECTION("Zero vec2 deserialization") {
        json j = {0.0f, 0.0f};
        glm::vec2 result = j.get<glm::vec2>();
        
        REQUIRE(result.x == Approx(0.0f).epsilon(0.0001f));
        REQUIRE(result.y == Approx(0.0f).epsilon(0.0001f));
    }
    
    SECTION("Texture coordinates deserialization") {
        json j = {0.5f, 0.75f}; // Common UV coordinates
        glm::vec2 result = j.get<glm::vec2>();
        
        REQUIRE(result.x == Approx(0.5f).epsilon(0.0001f));
        REQUIRE(result.y == Approx(0.75f).epsilon(0.0001f));
    }
    
    SECTION("Error: Wrong array size (too few elements)") {
        json j = {1.0f}; // Only 1 element

        REQUIRE_THROWS_AS(j.get<glm::vec2>(), std::runtime_error);

        try {
            j.get<glm::vec2>();
            FAIL("Expected exception was not thrown");
        } catch (const std::runtime_error& e) {
            REQUIRE(std::string(e.what()) == "Expected JSON array of size 2 for glm::vec2");
        }
    }

    SECTION("Error: Wrong array size (too many elements)") {
        json j = {1.0f, 2.0f, 3.0f}; // 3 elements

        REQUIRE_THROWS_AS(j.get<glm::vec2>(), std::runtime_error);

        try {
            j.get<glm::vec2>();
            FAIL("Expected exception was not thrown");
        } catch (const std::runtime_error& e) {
            REQUIRE(std::string(e.what()) == "Expected JSON array of size 2 for glm::vec2");
        }
    }

    SECTION("Error: Not an array") {
        json j = 42; // Number instead of array

        REQUIRE_THROWS_AS(j.get<glm::vec2>(), std::runtime_error);

        try {
            j.get<glm::vec2>();
            FAIL("Expected exception was not thrown");
        } catch (const std::runtime_error& e) {
            REQUIRE(std::string(e.what()) == "Expected JSON array of size 2 for glm::vec2");
        }
    }
}

TEST_CASE("JSON serializers integration tests", "[utils][json][glm][integration]") {
    
    SECTION("Parse JSON string with multiple vectors") {
        std::string jsonStr = R"({
            "position": [1.0, 2.0, 3.0],
            "color": [0.5, 0.75, 1.0, 0.8],
            "texCoord": [0.25, 0.5]
        })";
        
        json j = json::parse(jsonStr);
        
        glm::vec3 position = j["position"].get<glm::vec3>();
        glm::vec4 color = j["color"].get<glm::vec4>();
        glm::vec2 texCoord = j["texCoord"].get<glm::vec2>();
        
        // Verify position
        REQUIRE(position.x == Approx(1.0f).epsilon(0.0001f));
        REQUIRE(position.y == Approx(2.0f).epsilon(0.0001f));
        REQUIRE(position.z == Approx(3.0f).epsilon(0.0001f));
        
        // Verify color
        REQUIRE(color.x == Approx(0.5f).epsilon(0.0001f));
        REQUIRE(color.y == Approx(0.75f).epsilon(0.0001f));
        REQUIRE(color.z == Approx(1.0f).epsilon(0.0001f));
        REQUIRE(color.w == Approx(0.8f).epsilon(0.0001f));
        
        // Verify texture coordinates
        REQUIRE(texCoord.x == Approx(0.25f).epsilon(0.0001f));
        REQUIRE(texCoord.y == Approx(0.5f).epsilon(0.0001f));
    }
    
    SECTION("Array of vectors deserialization") {
        json j = {
            {1.0f, 2.0f, 3.0f},
            {4.0f, 5.0f, 6.0f},
            {7.0f, 8.0f, 9.0f}
        };
        
        std::vector<glm::vec3> vectors = j.get<std::vector<glm::vec3>>();
        
        REQUIRE(vectors.size() == 3);
        
        // First vector
        REQUIRE(vectors[0].x == Approx(1.0f).epsilon(0.0001f));
        REQUIRE(vectors[0].y == Approx(2.0f).epsilon(0.0001f));
        REQUIRE(vectors[0].z == Approx(3.0f).epsilon(0.0001f));
        
        // Second vector
        REQUIRE(vectors[1].x == Approx(4.0f).epsilon(0.0001f));
        REQUIRE(vectors[1].y == Approx(5.0f).epsilon(0.0001f));
        REQUIRE(vectors[1].z == Approx(6.0f).epsilon(0.0001f));
        
        // Third vector
        REQUIRE(vectors[2].x == Approx(7.0f).epsilon(0.0001f));
        REQUIRE(vectors[2].y == Approx(8.0f).epsilon(0.0001f));
        REQUIRE(vectors[2].z == Approx(9.0f).epsilon(0.0001f));
    }
}
