#include <catch2/catch_test_macros.hpp>
#include <catch2/catch_approx.hpp>

#include "utils/containers_set.hpp"
#include "../test_utils.hpp"

using Catch::Approx;

using namespace IronFrost;

// Test classes for containers set testing
class TestClassA {
public:
    int value;
    TestClassA(int v) : value(v) {}

    // Make it copyable for ValueContainer tests
    TestClassA(const TestClassA&) = default;
    TestClassA& operator=(const TestClassA&) = default;
    TestClassA(TestClassA&&) = default;
    TestClassA& operator=(TestClassA&&) = default;

    bool operator==(const TestClassA& other) const {
        return value == other.value;
    }
};

class TestClassB {
public:
    std::string name;
    TestClassB(const std::string& n) : name(n) {}

    // Make it non-copyable for PointerContainer
    TestClassB(const TestClassB&) = delete;
    TestClassB& operator=(const TestClassB&) = delete;
    TestClassB(TestClassB&&) = default;
    TestClassB& operator=(TestClassB&&) = default;

    bool operator==(const TestClassB& other) const {
        return name == other.name;
    }
};

class TestClassC {
public:
    float data;
    TestClassC(float d) : data(d) {}
    
    bool operator==(const TestClassC& other) const {
        return std::abs(data - other.data) < 1e-6f;
    }
};

// Hash specializations for ValueContainer reverse lookup
namespace std {
    template<>
    struct hash<TestClassA> {
        size_t operator()(const TestClassA& obj) const {
            return hash<int>()(obj.value);
        }
    };

    template<>
    struct hash<TestClassB> {
        size_t operator()(const TestClassB& obj) const {
            return hash<string>()(obj.name);
        }
    };

    template<>
    struct hash<TestClassC> {
        size_t operator()(const TestClassC& obj) const {
            return hash<float>()(obj.data);
        }
    };
}

// Non-copyable test classes for PointerContainer
class NonCopyableTestClassA {
public:
    int value;
    NonCopyableTestClassA(int v) : value(v) {}

    // Non-copyable for PointerContainer
    NonCopyableTestClassA(const NonCopyableTestClassA&) = delete;
    NonCopyableTestClassA& operator=(const NonCopyableTestClassA&) = delete;
    NonCopyableTestClassA(NonCopyableTestClassA&&) = default;
    NonCopyableTestClassA& operator=(NonCopyableTestClassA&&) = default;
};

class NonCopyableTestClassB {
public:
    std::string name;
    NonCopyableTestClassB(const std::string& n) : name(n) {}

    // Non-copyable for PointerContainer
    NonCopyableTestClassB(const NonCopyableTestClassB&) = delete;
    NonCopyableTestClassB& operator=(const NonCopyableTestClassB&) = delete;
    NonCopyableTestClassB(NonCopyableTestClassB&&) = default;
    NonCopyableTestClassB& operator=(NonCopyableTestClassB&&) = default;
};

TEST_CASE("PointerContainersSet basic operations", "[utils][containers_set][pointer]") {
    using TestContainersSet = PointerContainersSet<NonCopyableTestClassA, NonCopyableTestClassB>;
    TestContainersSet containersSet;
    
    StringID idA1 = StringID("testA1");
    StringID idB1 = StringID("testB1");
    
    SECTION("Get individual containers") {
        auto& containerA = containersSet.getContainer<NonCopyableTestClassA>();
        auto& containerB = containersSet.getContainer<NonCopyableTestClassB>();

        // Verify they are different containers
        REQUIRE(containerA.size() == 0);
        REQUIRE(containerB.size() == 0);

        // Insert into containerA
        containerA.insert(idA1, std::make_unique<NonCopyableTestClassA>(42));

        REQUIRE(containerA.size() == 1);
        REQUIRE(containerB.size() == 0); // Should remain unchanged
    }

    SECTION("Const access to containers") {
        const TestContainersSet& constContainersSet = containersSet;

        const auto& containerA = constContainersSet.getContainer<NonCopyableTestClassA>();
        const auto& containerB = constContainersSet.getContainer<NonCopyableTestClassB>();

        REQUIRE(containerA.size() == 0);
        REQUIRE(containerB.size() == 0);
    }

    SECTION("Multiple container operations") {
        auto& containerA = containersSet.getContainer<NonCopyableTestClassA>();
        auto& containerB = containersSet.getContainer<NonCopyableTestClassB>();

        // Insert into both containers
        containerA.insert(idA1, std::make_unique<NonCopyableTestClassA>(100));
        containerB.insert(idB1, std::make_unique<NonCopyableTestClassB>("test"));

        REQUIRE(containerA.size() == 1);
        REQUIRE(containerB.size() == 1);

        REQUIRE(containerA.get(idA1).value == 100);
        REQUIRE(containerB.get(idB1).name == "test");

        // Remove from one container
        containerA.remove(idA1);

        REQUIRE(containerA.size() == 0);
        REQUIRE(containerB.size() == 1); // Should remain unchanged
    }
}

// Copyable test classes for ValueContainer
class CopyableTestClassB {
public:
    std::string name;
    CopyableTestClassB(const std::string& n) : name(n) {}

    bool operator==(const CopyableTestClassB& other) const {
        return name == other.name;
    }
};

class CopyableTestClassC {
public:
    float data;
    CopyableTestClassC(float d) : data(d) {}

    bool operator==(const CopyableTestClassC& other) const {
        return std::abs(data - other.data) < 1e-6f;
    }
};

// Hash specializations for copyable classes
namespace std {
    template<>
    struct hash<CopyableTestClassB> {
        size_t operator()(const CopyableTestClassB& obj) const {
            return hash<string>()(obj.name);
        }
    };

    template<>
    struct hash<CopyableTestClassC> {
        size_t operator()(const CopyableTestClassC& obj) const {
            return hash<float>()(obj.data);
        }
    };
}

TEST_CASE("ValueContainersSet basic operations", "[utils][containers_set][value]") {
    using TestContainersSet = ValueContainersSet<CopyableTestClassB, CopyableTestClassC>;
    TestContainersSet containersSet;
    
    StringID idB1 = StringID("valueB1");
    StringID idC1 = StringID("valueC1");
    
    SECTION("Get individual containers") {
        auto& containerB = containersSet.getContainer<CopyableTestClassB>();
        auto& containerC = containersSet.getContainer<CopyableTestClassC>();

        // Verify they are different containers
        REQUIRE(containerB.size() == 0);
        REQUIRE(containerC.size() == 0);

        // Insert into containerB
        containerB.insert(idB1, CopyableTestClassB("hello"));

        REQUIRE(containerB.size() == 1);
        REQUIRE(containerC.size() == 0); // Should remain unchanged
    }

    SECTION("Multiple container operations") {
        auto& containerB = containersSet.getContainer<CopyableTestClassB>();
        auto& containerC = containersSet.getContainer<CopyableTestClassC>();

        // Insert into both containers
        containerB.insert(idB1, CopyableTestClassB("world"));
        containerC.insert(idC1, CopyableTestClassC(3.14f));

        REQUIRE(containerB.size() == 1);
        REQUIRE(containerC.size() == 1);

        REQUIRE(containerB.get(idB1).name == "world");
        REQUIRE(containerC.get(idC1).data == Approx(3.14f));

        // Test reverse lookup
        auto foundIdB = containerB.findID(CopyableTestClassB("world"));
        auto foundIdC = containerC.findID(CopyableTestClassC(3.14f));

        REQUIRE(foundIdB.has_value());
        REQUIRE(foundIdC.has_value());
        REQUIRE(foundIdB.value() == idB1);
        REQUIRE(foundIdC.value() == idC1);
    }
}

TEST_CASE("ContainersSet with three types", "[utils][containers_set][multiple]") {
    using TestContainersSet = ValueContainersSet<TestClassA, CopyableTestClassB, CopyableTestClassC>;
    TestContainersSet containersSet;

    StringID idA = StringID("multiA");
    StringID idB = StringID("multiB");
    StringID idC = StringID("multiC");

    SECTION("Access all three containers") {
        auto& containerA = containersSet.getContainer<TestClassA>();
        auto& containerB = containersSet.getContainer<CopyableTestClassB>();
        auto& containerC = containersSet.getContainer<CopyableTestClassC>();

        REQUIRE(containerA.size() == 0);
        REQUIRE(containerB.size() == 0);
        REQUIRE(containerC.size() == 0);

        // Insert into each container
        containerA.insert(idA, TestClassA(42));
        containerB.insert(idB, CopyableTestClassB("multi"));
        containerC.insert(idC, CopyableTestClassC(2.71f));

        REQUIRE(containerA.size() == 1);
        REQUIRE(containerB.size() == 1);
        REQUIRE(containerC.size() == 1);

        REQUIRE(containerA.get(idA).value == 42);
        REQUIRE(containerB.get(idB).name == "multi");
        REQUIRE(containerC.get(idC).data == Approx(2.71f));
    }
}

TEST_CASE("ContainersSet type safety", "[utils][containers_set][type_safety]") {
    using TestContainersSet = PointerContainersSet<NonCopyableTestClassA, NonCopyableTestClassB>;
    TestContainersSet containersSet;

    SECTION("Different calls return different container types") {
        auto& containerA1 = containersSet.getContainer<NonCopyableTestClassA>();
        auto& containerA2 = containersSet.getContainer<NonCopyableTestClassA>();
        auto& containerB = containersSet.getContainer<NonCopyableTestClassB>();

        // Same type should return same container instance
        REQUIRE(&containerA1 == &containerA2);

        // Different types should return different container instances
        REQUIRE(&containerA1 != reinterpret_cast<void*>(&containerB));
    }
}

TEST_CASE("ContainersSet const correctness", "[utils][containers_set][const]") {
    using TestContainersSet = ValueContainersSet<CopyableTestClassB, CopyableTestClassC>;
    TestContainersSet containersSet;

    StringID idB = StringID("constB");

    // Add some data
    auto& containerB = containersSet.getContainer<CopyableTestClassB>();
    containerB.insert(idB, CopyableTestClassB("const_test"));

    SECTION("Const access") {
        const TestContainersSet& constContainersSet = containersSet;
        const auto& constContainerB = constContainersSet.getContainer<CopyableTestClassB>();

        REQUIRE(constContainerB.size() == 1);
        REQUIRE(constContainerB.has(idB));
        REQUIRE(constContainerB.get(idB).name == "const_test");

        // Verify we can't modify through const reference
        // (This is enforced by the compiler, not testable at runtime)
    }
}

TEST_CASE("ContainersSet edge cases", "[utils][containers_set][edge_cases]") {
    SECTION("Single type containers set") {
        using SingleTypeSet = ValueContainersSet<CopyableTestClassB>;
        SingleTypeSet singleSet;

        auto& container = singleSet.getContainer<CopyableTestClassB>();
        REQUIRE(container.size() == 0);

        StringID id = StringID("single");
        container.insert(id, CopyableTestClassB("single_type"));

        REQUIRE(container.size() == 1);
        REQUIRE(container.get(id).name == "single_type");
    }

    SECTION("Empty operations") {
        using EmptySet = ValueContainersSet<CopyableTestClassB>;
        EmptySet emptySet;

        const auto& container = emptySet.getContainer<CopyableTestClassB>();
        REQUIRE(container.size() == 0);
        REQUIRE_FALSE(container.has(StringID("nonexistent")));
    }
}
