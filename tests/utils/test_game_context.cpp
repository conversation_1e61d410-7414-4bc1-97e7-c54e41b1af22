#include <catch2/catch_test_macros.hpp>

#include "utils/game_context.hpp"
#include "../test_utils.hpp"

using namespace IronFrost;

// Mock classes for testing GameContext
class MockKeyboard : public IKeyboard {
public:
    bool m_keyPressed = false;
    
    bool isKeyDown(KeyType key) const override {
        return m_keyPressed;
    }
    
    bool isKeyUp(KeyType key) const override {
        return !m_keyPressed;
    }
    
    bool wasKeyPressed(KeyType key) const override {
        return m_keyPressed;
    }
    
    bool wasKeyReleased(KeyType key) const override {
        return !m_keyPressed;
    }
    
    void update() override {
        // Mock implementation
    }
    
    void setKeyPressed(bool pressed) {
        m_keyPressed = pressed;
    }
};

class MockMouse : public IMouse {
public:
    bool m_buttonPressed = false;
    glm::vec2 m_position{0.0f, 0.0f};
    
    bool isButtonDown(MouseButton button) const override {
        return m_buttonPressed;
    }
    
    bool isButtonUp(MouseButton button) const override {
        return !m_buttonPressed;
    }
    
    bool wasButtonPressed(MouseButton button) const override {
        return m_buttonPressed;
    }
    
    bool wasButtonReleased(MouseButton button) const override {
        return !m_buttonPressed;
    }
    
    glm::vec2 getPosition() const override {
        return m_position;
    }
    
    glm::vec2 getDelta() const override {
        return glm::vec2(0.0f, 0.0f);
    }
    
    float getScrollDelta() const override {
        return 0.0f;
    }
    
    void update() override {
        // Mock implementation
    }
    
    void setButtonPressed(bool pressed) {
        m_buttonPressed = pressed;
    }
    
    void setPosition(const glm::vec2& pos) {
        m_position = pos;
    }
};

TEST_CASE("GameContext construction and basic usage", "[utils][game_context][basic]") {
    MockKeyboard keyboard;
    MockMouse mouse;
    
    SECTION("GameContext construction") {
        GameContext context{keyboard, mouse};
        
        // Verify references are correctly stored
        REQUIRE(&context.keyboard == &keyboard);
        REQUIRE(&context.mouse == &mouse);
    }
    
    SECTION("GameContext member access") {
        GameContext context{keyboard, mouse};
        
        // Test keyboard access
        keyboard.setKeyPressed(true);
        REQUIRE(context.keyboard.isKeyDown(KeyType::W));
        
        keyboard.setKeyPressed(false);
        REQUIRE_FALSE(context.keyboard.isKeyDown(KeyType::W));
        
        // Test mouse access
        mouse.setButtonPressed(true);
        REQUIRE(context.mouse.isButtonDown(MouseButton::Left));
        
        mouse.setButtonPressed(false);
        REQUIRE_FALSE(context.mouse.isButtonDown(MouseButton::Left));
    }
}

TEST_CASE("GameContext reference semantics", "[utils][game_context][references]") {
    MockKeyboard keyboard;
    MockMouse mouse;
    GameContext context{keyboard, mouse};
    
    SECTION("Modifications through context affect original objects") {
        // Modify through context reference
        keyboard.setKeyPressed(true);
        mouse.setPosition(glm::vec2(100.0f, 200.0f));
        
        // Verify changes are reflected
        REQUIRE(context.keyboard.isKeyDown(KeyType::W));
        REQUIRE(isVec2Equal(context.mouse.getPosition(), glm::vec2(100.0f, 200.0f)));
        
        // Verify original objects are also changed
        REQUIRE(keyboard.isKeyDown(KeyType::W));
        REQUIRE(isVec2Equal(mouse.getPosition(), glm::vec2(100.0f, 200.0f)));
    }
    
    SECTION("Context maintains references after copy") {
        GameContext context2 = context;
        
        // Both contexts should reference the same objects
        REQUIRE(&context.keyboard == &context2.keyboard);
        REQUIRE(&context.mouse == &context2.mouse);
        REQUIRE(&context.keyboard == &keyboard);
        REQUIRE(&context.mouse == &mouse);
    }
}

TEST_CASE("GameContext with different input states", "[utils][game_context][input_states]") {
    MockKeyboard keyboard;
    MockMouse mouse;
    GameContext context{keyboard, mouse};
    
    SECTION("Keyboard state changes") {
        // Initial state
        REQUIRE_FALSE(context.keyboard.isKeyDown(KeyType::W));
        REQUIRE(context.keyboard.isKeyUp(KeyType::W));
        
        // Press key
        keyboard.setKeyPressed(true);
        REQUIRE(context.keyboard.isKeyDown(KeyType::W));
        REQUIRE_FALSE(context.keyboard.isKeyUp(KeyType::W));
        
        // Release key
        keyboard.setKeyPressed(false);
        REQUIRE_FALSE(context.keyboard.isKeyDown(KeyType::W));
        REQUIRE(context.keyboard.isKeyUp(KeyType::W));
    }
    
    SECTION("Mouse state changes") {
        // Initial state
        REQUIRE_FALSE(context.mouse.isButtonDown(MouseButton::Left));
        REQUIRE(context.mouse.isButtonUp(MouseButton::Left));
        REQUIRE(isVec2Equal(context.mouse.getPosition(), glm::vec2(0.0f, 0.0f)));
        
        // Press button and move mouse
        mouse.setButtonPressed(true);
        mouse.setPosition(glm::vec2(50.0f, 75.0f));
        
        REQUIRE(context.mouse.isButtonDown(MouseButton::Left));
        REQUIRE_FALSE(context.mouse.isButtonUp(MouseButton::Left));
        REQUIRE(isVec2Equal(context.mouse.getPosition(), glm::vec2(50.0f, 75.0f)));
        
        // Release button
        mouse.setButtonPressed(false);
        REQUIRE_FALSE(context.mouse.isButtonDown(MouseButton::Left));
        REQUIRE(context.mouse.isButtonUp(MouseButton::Left));
        // Position should remain
        REQUIRE(isVec2Equal(context.mouse.getPosition(), glm::vec2(50.0f, 75.0f)));
    }
}

TEST_CASE("GameContext usage patterns", "[utils][game_context][usage]") {
    MockKeyboard keyboard;
    MockMouse mouse;
    GameContext context{keyboard, mouse};
    
    SECTION("Typical game input handling") {
        // Simulate typical game input scenario
        keyboard.setKeyPressed(true);  // Player presses W
        mouse.setPosition(glm::vec2(320.0f, 240.0f));  // Mouse at screen center
        mouse.setButtonPressed(true);  // Player clicks
        
        // Game logic would check these states
        bool shouldMoveForward = context.keyboard.isKeyDown(KeyType::W);
        bool shouldShoot = context.mouse.isButtonDown(MouseButton::Left);
        glm::vec2 aimPosition = context.mouse.getPosition();
        
        REQUIRE(shouldMoveForward);
        REQUIRE(shouldShoot);
        REQUIRE(isVec2Equal(aimPosition, glm::vec2(320.0f, 240.0f)));
    }
    
    SECTION("Context passed to functions") {
        // Function that takes GameContext
        auto processInput = [](const GameContext& ctx) -> std::pair<bool, glm::vec2> {
            bool moving = ctx.keyboard.isKeyDown(KeyType::W);
            glm::vec2 mousePos = ctx.mouse.getPosition();
            return {moving, mousePos};
        };
        
        keyboard.setKeyPressed(true);
        mouse.setPosition(glm::vec2(100.0f, 150.0f));
        
        auto [isMoving, mousePosition] = processInput(context);
        
        REQUIRE(isMoving);
        REQUIRE(isVec2Equal(mousePosition, glm::vec2(100.0f, 150.0f)));
    }
}

TEST_CASE("GameContext const correctness", "[utils][game_context][const]") {
    MockKeyboard keyboard;
    MockMouse mouse;
    const GameContext context{keyboard, mouse};
    
    SECTION("Const context allows read-only access") {
        keyboard.setKeyPressed(true);
        mouse.setPosition(glm::vec2(200.0f, 300.0f));
        
        // Should be able to read from const context
        REQUIRE(context.keyboard.isKeyDown(KeyType::W));
        REQUIRE(isVec2Equal(context.mouse.getPosition(), glm::vec2(200.0f, 300.0f)));
        
        // Const context should provide const references
        const IKeyboard& constKeyboard = context.keyboard;
        const IMouse& constMouse = context.mouse;
        
        REQUIRE(constKeyboard.isKeyDown(KeyType::W));
        REQUIRE(isVec2Equal(constMouse.getPosition(), glm::vec2(200.0f, 300.0f)));
    }
}

TEST_CASE("GameContext edge cases", "[utils][game_context][edge_cases]") {
    MockKeyboard keyboard;
    MockMouse mouse;
    
    SECTION("Multiple contexts with same input devices") {
        GameContext context1{keyboard, mouse};
        GameContext context2{keyboard, mouse};
        
        // Both contexts should reference the same devices
        REQUIRE(&context1.keyboard == &context2.keyboard);
        REQUIRE(&context1.mouse == &context2.mouse);
        
        // Changes through one context affect the other
        keyboard.setKeyPressed(true);
        
        REQUIRE(context1.keyboard.isKeyDown(KeyType::W));
        REQUIRE(context2.keyboard.isKeyDown(KeyType::W));
    }
    
    SECTION("Context with different input device instances") {
        MockKeyboard keyboard2;
        MockMouse mouse2;
        
        GameContext context1{keyboard, mouse};
        GameContext context2{keyboard2, mouse2};
        
        // Contexts should reference different devices
        REQUIRE(&context1.keyboard != &context2.keyboard);
        REQUIRE(&context1.mouse != &context2.mouse);
        
        // Changes to one set shouldn't affect the other
        keyboard.setKeyPressed(true);
        keyboard2.setKeyPressed(false);
        
        REQUIRE(context1.keyboard.isKeyDown(KeyType::W));
        REQUIRE_FALSE(context2.keyboard.isKeyDown(KeyType::W));
    }
}
