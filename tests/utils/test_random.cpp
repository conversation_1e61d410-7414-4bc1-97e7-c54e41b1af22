#include <catch2/catch_test_macros.hpp>
#include <catch2/catch_approx.hpp>

#include "utils/random.hpp"

#include <algorithm>
#include <cmath>
#include <set>
#include <unordered_map>
#include <vector>

using namespace IronFrost;
using Catch::Approx;

TEST_CASE("Random integer generation", "[utils][random][int]") {
    Random rng;
    
    SECTION("Single value range") {
        // When min == max, should always return that value
        int value = rng.getInt(42, 42);
        REQUIRE(value == 42);
        
        // Multiple calls should all return the same value
        for (int i = 0; i < 10; ++i) {
            REQUIRE(rng.getInt(100, 100) == 100);
        }
    }
    
    SECTION("Small range bounds checking") {
        const int min = 5;
        const int max = 10;
        
        // Generate many values and check they're all within bounds
        for (int i = 0; i < 1000; ++i) {
            int value = rng.getInt(min, max);
            REQUIRE(value >= min);
            REQUIRE(value <= max);
        }
    }
    
    SECTION("Large range bounds checking") {
        const int min = -1000000;
        const int max = 1000000;
        
        for (int i = 0; i < 1000; ++i) {
            int value = rng.getInt(min, max);
            REQUIRE(value >= min);
            REQUIRE(value <= max);
        }
    }
    
    SECTION("Negative range") {
        const int min = -50;
        const int max = -10;
        
        for (int i = 0; i < 500; ++i) {
            int value = rng.getInt(min, max);
            REQUIRE(value >= min);
            REQUIRE(value <= max);
        }
    }
    
    SECTION("Distribution coverage") {
        const int min = 1;
        const int max = 6; // Like a dice roll
        std::unordered_map<int, int> counts;
        const int numSamples = 6000;
        
        // Generate many samples
        for (int i = 0; i < numSamples; ++i) {
            int value = rng.getInt(min, max);
            counts[value]++;
        }
        
        // Check that all possible values appeared
        for (int i = min; i <= max; ++i) {
            REQUIRE(counts[i] > 0);
        }
        
        // Check that distribution is reasonably uniform
        // Each value should appear roughly numSamples/(max-min+1) times
        const int expectedCount = numSamples / (max - min + 1);
        const int tolerance = expectedCount / 2; // Allow 50% deviation
        
        for (int i = min; i <= max; ++i) {
            REQUIRE(counts[i] > expectedCount - tolerance);
            REQUIRE(counts[i] < expectedCount + tolerance);
        }
    }
    
    SECTION("Randomness check") {
        // Generate a sequence and check it's not all the same
        std::vector<int> values;
        const int numValues = 100;
        
        for (int i = 0; i < numValues; ++i) {
            values.push_back(rng.getInt(1, 1000));
        }
        
        // Check that we got different values (very unlikely to get all same)
        std::set<int> uniqueValues(values.begin(), values.end());
        REQUIRE(uniqueValues.size() > 1);
        
        // Check that values are not in a simple pattern
        bool allIncreasing = true;
        bool allDecreasing = true;
        for (size_t i = 1; i < values.size(); ++i) {
            if (values[i] <= values[i-1]) allIncreasing = false;
            if (values[i] >= values[i-1]) allDecreasing = false;
        }
        REQUIRE_FALSE(allIncreasing);
        REQUIRE_FALSE(allDecreasing);
    }
}

TEST_CASE("Random double generation", "[utils][random][double]") {
    Random rng;
    
    SECTION("Range bounds checking") {
        const double min = 0.0;
        const double max = 1.0;
        
        for (int i = 0; i < 1000; ++i) {
            double value = rng.getDouble(min, max);
            REQUIRE(value >= min);
            REQUIRE(value < max); // Note: uniform_real_distribution is [min, max)
        }
    }
    
    SECTION("Negative range") {
        const double min = -10.5;
        const double max = -2.3;
        
        for (int i = 0; i < 500; ++i) {
            double value = rng.getDouble(min, max);
            REQUIRE(value >= min);
            REQUIRE(value < max);
        }
    }
    
    SECTION("Large range") {
        const double min = -1000000.0;
        const double max = 1000000.0;
        
        for (int i = 0; i < 500; ++i) {
            double value = rng.getDouble(min, max);
            REQUIRE(value >= min);
            REQUIRE(value < max);
        }
    }
    
    SECTION("Small range precision") {
        const double min = 0.0;
        const double max = 0.001;
        
        for (int i = 0; i < 100; ++i) {
            double value = rng.getDouble(min, max);
            REQUIRE(value >= min);
            REQUIRE(value < max);
        }
    }
    
    SECTION("Distribution properties") {
        const double min = 0.0;
        const double max = 100.0;
        const int numSamples = 10000;
        
        std::vector<double> values;
        double sum = 0.0;
        
        for (int i = 0; i < numSamples; ++i) {
            double value = rng.getDouble(min, max);
            values.push_back(value);
            sum += value;
        }
        
        // Check mean is approximately in the middle of the range
        double mean = sum / numSamples;
        double expectedMean = (min + max) / 2.0;
        REQUIRE(mean == Approx(expectedMean).epsilon(0.05)); // 5% tolerance
        
        // Check that we have good spread (standard deviation)
        double variance = 0.0;
        for (double value : values) {
            variance += (value - mean) * (value - mean);
        }
        variance /= numSamples;
        double stddev = std::sqrt(variance);
        
        // For uniform distribution, stddev = (max-min)/sqrt(12)
        double expectedStddev = (max - min) / std::sqrt(12.0);
        REQUIRE(stddev == Approx(expectedStddev).epsilon(0.1)); // 10% tolerance
    }
    
    SECTION("Uniqueness check") {
        // Generate many doubles and check they're all different
        // (extremely unlikely to get duplicates with floating point)
        std::set<double> uniqueValues;
        const int numValues = 1000;
        
        for (int i = 0; i < numValues; ++i) {
            double value = rng.getDouble(0.0, 1000000.0);
            uniqueValues.insert(value);
        }
        
        // Should have very high uniqueness
        REQUIRE(uniqueValues.size() > numValues * 0.99); // At least 99% unique
    }
}

TEST_CASE("Random class multiple instances", "[utils][random][instances]") {
    
    SECTION("Different instances produce different sequences") {
        Random rng1;
        Random rng2;
        
        // Generate sequences from both instances
        std::vector<int> sequence1, sequence2;
        for (int i = 0; i < 100; ++i) {
            sequence1.push_back(rng1.getInt(1, 1000000));
            sequence2.push_back(rng2.getInt(1, 1000000));
        }
        
        // Sequences should be different (seeded with different times)
        REQUIRE(sequence1 != sequence2);
        
        // Both should have good randomness properties
        std::set<int> unique1(sequence1.begin(), sequence1.end());
        std::set<int> unique2(sequence2.begin(), sequence2.end());
        
        REQUIRE(unique1.size() > 50); // Good uniqueness
        REQUIRE(unique2.size() > 50); // Good uniqueness
    }
    
    SECTION("Same instance produces consistent sequence") {
        Random rng;
        
        // Generate some values to advance the state
        for (int i = 0; i < 10; ++i) {
            rng.getInt(1, 100);
        }
        
        // Now generate a test sequence
        std::vector<int> sequence1;
        for (int i = 0; i < 50; ++i) {
            sequence1.push_back(rng.getInt(1, 1000));
        }
        
        // The sequence should be deterministic from this point
        // (We can't easily test this without exposing the internal state,
        // but we can test that the generator continues to work consistently)
        
        // Generate more values and ensure they're still in valid ranges
        for (int i = 0; i < 100; ++i) {
            int value = rng.getInt(1, 1000);
            REQUIRE(value >= 1);
            REQUIRE(value <= 1000);
        }
    }
}

TEST_CASE("Random edge cases", "[utils][random][edge_cases]") {
    Random rng;
    
    SECTION("Zero range integer") {
        // min == max should work
        REQUIRE(rng.getInt(0, 0) == 0);
        REQUIRE(rng.getInt(-5, -5) == -5);
        REQUIRE(rng.getInt(1000, 1000) == 1000);
    }
    
    SECTION("Minimum range integer") {
        // Range of 1 (two possible values)
        for (int i = 0; i < 100; ++i) {
            int value = rng.getInt(10, 11);
            REQUIRE((value == 10 || value == 11));
        }
        
        // Should get both values eventually
        bool got10 = false, got11 = false;
        for (int i = 0; i < 1000 && (!got10 || !got11); ++i) {
            int value = rng.getInt(10, 11);
            if (value == 10) got10 = true;
            if (value == 11) got11 = true;
        }
        REQUIRE(got10);
        REQUIRE(got11);
    }
    
    SECTION("Very small double range") {
        const double min = 1.0;
        const double max = 1.0 + 1e-10; // Very small range
        
        for (int i = 0; i < 100; ++i) {
            double value = rng.getDouble(min, max);
            REQUIRE(value >= min);
            REQUIRE(value < max);
        }
    }
    
    SECTION("Integer overflow boundaries") {
        // Test with values near integer limits (but not causing overflow)
        const int min = -1000000;
        const int max = 1000000;
        
        for (int i = 0; i < 100; ++i) {
            int value = rng.getInt(min, max);
            REQUIRE(value >= min);
            REQUIRE(value <= max);
        }
    }
}
