#include <catch2/catch_test_macros.hpp>

#include "utils/string_id.hpp"

#include <sstream>
#include <string>
#include <thread>
#include <unordered_map>
#include <unordered_set>
#include <vector>

using namespace IronFrost;

TEST_CASE("StringID basic functionality", "[utils][string_id][basic]") {
    
    SECTION("Default constructor creates zero ID") {
        StringID defaultID;
        StringID anotherDefault;
        
        REQUIRE(defaultID == anotherDefault);
        REQUIRE(defaultID.toString() == "0x00000000");
    }
    
    SECTION("String constructor creates unique IDs") {
        StringID id1("test_string");
        StringID id2("another_string");
        
        REQUIRE(id1 != id2);
        REQUIRE(id1.toString() != id2.toString());
    }
    
    SECTION("Same string creates same ID") {
        StringID id1("identical_string");
        StringID id2("identical_string");
        
        REQUIRE(id1 == id2);
        REQUIRE(id1.toString() == id2.toString());
    }
    
    SECTION("Empty string handling") {
        StringID emptyID("");
        StringID anotherEmpty("");
        
        REQUIRE(emptyID == anotherEmpty);
        REQUIRE(emptyID != StringID()); // Empty string should be different from default
    }
    
    SECTION("Different strings create different IDs") {
        StringID id1("string1");
        StringID id2("string2");
        StringID id3("string3");
        
        REQUIRE(id1 != id2);
        REQUIRE(id2 != id3);
        REQUIRE(id1 != id3);
        
        // All should have different string representations
        REQUIRE(id1.toString() != id2.toString());
        REQUIRE(id2.toString() != id3.toString());
        REQUIRE(id1.toString() != id3.toString());
    }
}

TEST_CASE("StringID copy and move semantics", "[utils][string_id][copy_move]") {
    
    SECTION("Copy constructor") {
        StringID original("original_string");
        StringID copy(original);
        
        REQUIRE(original == copy);
        REQUIRE(original.toString() == copy.toString());
    }
    
    SECTION("Copy assignment") {
        StringID original("original_string");
        StringID copy;
        
        copy = original;
        
        REQUIRE(original == copy);
        REQUIRE(original.toString() == copy.toString());
    }
    
    SECTION("Move constructor") {
        StringID original("move_test_string");
        std::string originalStr = original.toString();
        
        StringID moved(std::move(original));
        
        REQUIRE(moved.toString() == originalStr);
        REQUIRE(original.toString() == "0x00000000"); // Original should be reset to 0
    }
    
    SECTION("Self-assignment safety") {
        StringID id("self_assign_test");
        StringID& ref = id;
        std::string originalStr = id.toString();
        
        id = ref; // Self-assignment
        
        REQUIRE(id.toString() == originalStr);
    }
}

TEST_CASE("StringID string retrieval", "[utils][string_id][retrieval]") {
    
    SECTION("getString returns correct string") {
        std::string testStr = "test_retrieval_string";
        StringID id(testStr);
        
        REQUIRE(StringID::getString(id) == testStr);
    }
    
    SECTION("getString with default ID") {
        StringID defaultID;

        // Default ID (0) might return empty string or the first string that was assigned ID 0
        // The behavior depends on whether any string was assigned ID 0 in previous tests
        std::string result = StringID::getString(defaultID);

        // The result should be consistent - calling it again should return the same thing
        REQUIRE(StringID::getString(defaultID) == result);

        // Two default IDs should return the same string
        StringID anotherDefault;
        REQUIRE(StringID::getString(anotherDefault) == result);
    }
    
    SECTION("getString with multiple IDs") {
        StringID id1("first_string");
        StringID id2("second_string");
        StringID id3("third_string");
        
        REQUIRE(StringID::getString(id1) == "first_string");
        REQUIRE(StringID::getString(id2) == "second_string");
        REQUIRE(StringID::getString(id3) == "third_string");
    }
    
    SECTION("getString after copy") {
        StringID original("copy_test_string");
        StringID copy = original;
        
        REQUIRE(StringID::getString(copy) == "copy_test_string");
        REQUIRE(StringID::getString(original) == StringID::getString(copy));
    }
}

TEST_CASE("StringID toString formatting", "[utils][string_id][formatting]") {
    
    SECTION("toString returns hex format") {
        StringID id("format_test");
        std::string str = id.toString();
        
        // Should start with "0x" and be 10 characters total
        REQUIRE(str.length() == 10);
        REQUIRE(str.substr(0, 2) == "0x");
        
        // Should contain only hex characters after "0x"
        std::string hexPart = str.substr(2);
        for (char c : hexPart) {
            REQUIRE(((c >= '0' && c <= '9') || (c >= 'a' && c <= 'f')));
        }
    }
    
    SECTION("Default ID toString") {
        StringID defaultID;
        REQUIRE(defaultID.toString() == "0x00000000");
    }
    
    SECTION("Different IDs have different toString") {
        StringID id1("string_a");
        StringID id2("string_b");
        
        REQUIRE(id1.toString() != id2.toString());
    }
}

TEST_CASE("StringID stream output", "[utils][string_id][stream]") {
    
    SECTION("Stream output uses toString") {
        StringID id("stream_test");
        std::stringstream ss;
        
        ss << id;
        
        REQUIRE(ss.str() == id.toString());
    }
    
    SECTION("Multiple IDs to stream") {
        StringID id1("first");
        StringID id2("second");
        std::stringstream ss;
        
        ss << id1 << " " << id2;
        
        REQUIRE(ss.str() == id1.toString() + " " + id2.toString());
    }
}

TEST_CASE("StringID hash functionality", "[utils][string_id][hash]") {
    
    SECTION("Hash function works") {
        StringID id("hash_test");
        std::hash<StringID> hasher;
        
        std::size_t hashValue = hasher(id);
        
        // Hash should be consistent
        REQUIRE(hasher(id) == hashValue);
    }
    
    SECTION("Same IDs have same hash") {
        StringID id1("identical");
        StringID id2("identical");
        std::hash<StringID> hasher;
        
        REQUIRE(hasher(id1) == hasher(id2));
    }
    
    SECTION("Different IDs likely have different hashes") {
        StringID id1("different1");
        StringID id2("different2");
        std::hash<StringID> hasher;
        
        // While hash collisions are possible, they should be rare
        REQUIRE(hasher(id1) != hasher(id2));
    }
    
    SECTION("Use in unordered containers") {
        std::unordered_set<StringID> idSet;
        std::unordered_map<StringID, std::string> idMap;
        
        StringID id1("container_test1");
        StringID id2("container_test2");
        
        // Test unordered_set
        idSet.insert(id1);
        idSet.insert(id2);
        idSet.insert(id1); // Duplicate should not increase size
        
        REQUIRE(idSet.size() == 2);
        REQUIRE(idSet.count(id1) == 1);
        REQUIRE(idSet.count(id2) == 1);
        
        // Test unordered_map
        idMap[id1] = "value1";
        idMap[id2] = "value2";
        
        REQUIRE(idMap.size() == 2);
        REQUIRE(idMap[id1] == "value1");
        REQUIRE(idMap[id2] == "value2");
    }
}

TEST_CASE("StringID edge cases", "[utils][string_id][edge_cases]") {
    
    SECTION("Very long strings") {
        std::string longString(1000, 'a');
        StringID longID(longString);
        
        REQUIRE(StringID::getString(longID) == longString);
    }
    
    SECTION("Special characters") {
        std::string specialStr = "!@#$%^&*()_+-=[]{}|;':\",./<>?";
        StringID specialID(specialStr);
        
        REQUIRE(StringID::getString(specialID) == specialStr);
    }
    
    SECTION("Unicode characters") {
        std::string unicodeStr = "Hello 世界 🌍";
        StringID unicodeID(unicodeStr);
        
        REQUIRE(StringID::getString(unicodeID) == unicodeStr);
    }
    
    SECTION("Whitespace strings") {
        StringID spaceID(" ");
        StringID tabID("\t");
        StringID newlineID("\n");
        
        REQUIRE(StringID::getString(spaceID) == " ");
        REQUIRE(StringID::getString(tabID) == "\t");
        REQUIRE(StringID::getString(newlineID) == "\n");
        
        // All should be different
        REQUIRE(spaceID != tabID);
        REQUIRE(tabID != newlineID);
        REQUIRE(spaceID != newlineID);
    }
}

TEST_CASE("StringID thread safety", "[utils][string_id][threading]") {

    SECTION("Concurrent creation of same string") {
        const std::string testString = "concurrent_test";
        const int numThreads = 10;
        std::vector<std::thread> threads;
        std::vector<StringID> results(numThreads);

        // Create multiple threads that all create StringID with same string
        for (int i = 0; i < numThreads; ++i) {
            threads.emplace_back([&results, i, &testString]() {
                results[i] = StringID(testString);
            });
        }

        // Wait for all threads to complete
        for (auto& thread : threads) {
            thread.join();
        }

        // All results should be identical
        for (int i = 1; i < numThreads; ++i) {
            REQUIRE(results[0] == results[i]);
            REQUIRE(results[0].toString() == results[i].toString());
        }

        // All should retrieve the same string
        for (const auto& result : results) {
            REQUIRE(StringID::getString(result) == testString);
        }
    }

    SECTION("Concurrent creation of different strings") {
        const int numThreads = 10;
        std::vector<std::thread> threads;
        std::vector<StringID> results(numThreads);

        // Create multiple threads that create StringIDs with different strings
        for (int i = 0; i < numThreads; ++i) {
            threads.emplace_back([&results, i]() {
                results[i] = StringID("thread_string_" + std::to_string(i));
            });
        }

        // Wait for all threads to complete
        for (auto& thread : threads) {
            thread.join();
        }

        // All results should be different
        for (int i = 0; i < numThreads; ++i) {
            for (int j = i + 1; j < numThreads; ++j) {
                REQUIRE(results[i] != results[j]);
            }
        }

        // Each should retrieve its correct string
        for (int i = 0; i < numThreads; ++i) {
            REQUIRE(StringID::getString(results[i]) == "thread_string_" + std::to_string(i));
        }
    }
}
