#include <catch2/catch_test_macros.hpp>

#include "utils/storage.hpp"

#include <atomic>
#include <chrono>
#include <set>
#include <string>
#include <thread>
#include <vector>

using namespace IronFrost;

// Test objects for storage
struct TestObject {
    int value;
    std::string name;
    
    TestObject() : value(0), name("") {}
    TestObject(int v, const std::string& n) : value(v), name(n) {}
    
    bool operator==(const TestObject& other) const {
        return value == other.value && name == other.name;
    }
};

TEST_CASE("Storage basic functionality", "[utils][storage][basic]") {
    Storage<int, TestObject> storage;
    
    SECTION("Insert single object") {
        TestObject obj(42, "test");
        int id = storage.insert(obj);
        
        REQUIRE(id >= 0);
        
        TestObject* retrieved = storage.get(id);
        REQUIRE(retrieved != nullptr);
        REQUIRE(retrieved->value == 42);
        REQUIRE(retrieved->name == "test");
    }
    
    SECTION("Insert multiple objects") {
        TestObject obj1(1, "first");
        TestObject obj2(2, "second");
        TestObject obj3(3, "third");
        
        int id1 = storage.insert(obj1);
        int id2 = storage.insert(obj2);
        int id3 = storage.insert(obj3);
        
        // IDs should be unique
        REQUIRE(id1 != id2);
        REQUIRE(id2 != id3);
        REQUIRE(id1 != id3);
        
        // All objects should be retrievable
        TestObject* retrieved1 = storage.get(id1);
        TestObject* retrieved2 = storage.get(id2);
        TestObject* retrieved3 = storage.get(id3);
        
        REQUIRE(retrieved1 != nullptr);
        REQUIRE(retrieved2 != nullptr);
        REQUIRE(retrieved3 != nullptr);
        
        REQUIRE(*retrieved1 == obj1);
        REQUIRE(*retrieved2 == obj2);
        REQUIRE(*retrieved3 == obj3);
    }
    
    SECTION("Get non-existent object") {
        TestObject* result = storage.get(999);
        REQUIRE(result == nullptr);
    }
    
    SECTION("Sequential ID generation") {
        TestObject obj(1, "test");
        
        int id1 = storage.insert(obj);
        int id2 = storage.insert(obj);
        int id3 = storage.insert(obj);
        
        // IDs should be sequential (assuming no other Storage instances)
        REQUIRE(id2 == id1 + 1);
        REQUIRE(id3 == id2 + 1);
    }
}

TEST_CASE("Storage const correctness", "[utils][storage][const]") {
    Storage<int, TestObject> storage;
    TestObject obj(100, "const_test");
    int id = storage.insert(obj);
    
    SECTION("Const get method") {
        const Storage<int, TestObject>& constStorage = storage;
        
        const TestObject* retrieved = constStorage.get(id);
        REQUIRE(retrieved != nullptr);
        REQUIRE(retrieved->value == 100);
        REQUIRE(retrieved->name == "const_test");
    }
    
    SECTION("Non-const get method") {
        TestObject* retrieved = storage.get(id);
        REQUIRE(retrieved != nullptr);
        
        // Should be able to modify through non-const pointer
        retrieved->value = 200;
        retrieved->name = "modified";
        
        // Verify modification
        TestObject* retrieved2 = storage.get(id);
        REQUIRE(retrieved2->value == 200);
        REQUIRE(retrieved2->name == "modified");
    }
}

TEST_CASE("Storage destroy functionality", "[utils][storage][destroy]") {
    Storage<int, TestObject> storage;
    
    SECTION("Destroy single object") {
        TestObject obj(42, "to_destroy");
        int id = storage.insert(obj);
        
        // Verify object exists
        REQUIRE(storage.get(id) != nullptr);
        
        // Destroy object
        storage.destroy(id);
        
        // Verify object no longer exists
        REQUIRE(storage.get(id) == nullptr);
    }
    
    SECTION("Destroy non-existent object") {
        // Should not crash or cause issues
        storage.destroy(999);
        
        // Storage should still work normally
        TestObject obj(1, "test");
        int id = storage.insert(obj);
        REQUIRE(storage.get(id) != nullptr);
    }
    
    SECTION("Destroy with swap optimization") {
        TestObject obj1(1, "first");
        TestObject obj2(2, "second");
        TestObject obj3(3, "third");
        
        int id1 = storage.insert(obj1);
        int id2 = storage.insert(obj2);
        int id3 = storage.insert(obj3);
        
        // Destroy middle object (should trigger swap with last)
        storage.destroy(id2);
        
        // First and third should still exist
        TestObject* retrieved1 = storage.get(id1);
        TestObject* retrieved3 = storage.get(id3);
        
        REQUIRE(retrieved1 != nullptr);
        REQUIRE(retrieved3 != nullptr);
        REQUIRE(*retrieved1 == obj1);
        REQUIRE(*retrieved3 == obj3);
        
        // Second should be gone
        REQUIRE(storage.get(id2) == nullptr);
    }
    
    SECTION("Destroy last object") {
        TestObject obj1(1, "first");
        TestObject obj2(2, "second");
        
        int id1 = storage.insert(obj1);
        int id2 = storage.insert(obj2);
        
        // Destroy last object (no swap needed)
        storage.destroy(id2);
        
        // First should still exist
        REQUIRE(storage.get(id1) != nullptr);
        REQUIRE(*storage.get(id1) == obj1);
        
        // Second should be gone
        REQUIRE(storage.get(id2) == nullptr);
    }
}

TEST_CASE("Storage clear functionality", "[utils][storage][clear]") {
    Storage<int, TestObject> storage;
    
    SECTION("Clear empty storage") {
        storage.clear();
        
        // Should still work after clearing empty storage
        TestObject obj(1, "test");
        int id = storage.insert(obj);
        REQUIRE(storage.get(id) != nullptr);
    }
    
    SECTION("Clear storage with objects") {
        TestObject obj1(1, "first");
        TestObject obj2(2, "second");
        TestObject obj3(3, "third");
        
        int id1 = storage.insert(obj1);
        int id2 = storage.insert(obj2);
        int id3 = storage.insert(obj3);
        
        // Verify objects exist
        REQUIRE(storage.get(id1) != nullptr);
        REQUIRE(storage.get(id2) != nullptr);
        REQUIRE(storage.get(id3) != nullptr);
        
        // Clear storage
        storage.clear();
        
        // All objects should be gone
        REQUIRE(storage.get(id1) == nullptr);
        REQUIRE(storage.get(id2) == nullptr);
        REQUIRE(storage.get(id3) == nullptr);
        
        // Should be able to insert new objects
        TestObject newObj(100, "new");
        int newId = storage.insert(newObj);
        REQUIRE(storage.get(newId) != nullptr);
        REQUIRE(*storage.get(newId) == newObj);
    }
}

TEST_CASE("Storage with different types", "[utils][storage][types]") {
    
    SECTION("Storage with strings") {
        Storage<size_t, std::string> stringStorage;
        
        size_t id1 = stringStorage.insert("Hello");
        size_t id2 = stringStorage.insert("World");
        
        std::string* str1 = stringStorage.get(id1);
        std::string* str2 = stringStorage.get(id2);
        
        REQUIRE(str1 != nullptr);
        REQUIRE(str2 != nullptr);
        REQUIRE(*str1 == "Hello");
        REQUIRE(*str2 == "World");
    }
    
    SECTION("Storage with integers") {
        Storage<long, int> intStorage;
        
        long id1 = intStorage.insert(42);
        long id2 = intStorage.insert(100);
        
        int* val1 = intStorage.get(id1);
        int* val2 = intStorage.get(id2);
        
        REQUIRE(val1 != nullptr);
        REQUIRE(val2 != nullptr);
        REQUIRE(*val1 == 42);
        REQUIRE(*val2 == 100);
    }
}

TEST_CASE("Storage complex operations", "[utils][storage][complex]") {
    Storage<int, TestObject> storage;
    
    SECTION("Insert, destroy, insert pattern") {
        TestObject obj1(1, "first");
        TestObject obj2(2, "second");
        
        int id1 = storage.insert(obj1);
        storage.destroy(id1);
        
        int id2 = storage.insert(obj2);
        
        // First should be gone, second should exist
        REQUIRE(storage.get(id1) == nullptr);
        REQUIRE(storage.get(id2) != nullptr);
        REQUIRE(*storage.get(id2) == obj2);
    }
    
    SECTION("Multiple destroy operations") {
        std::vector<int> ids;
        for (int i = 0; i < 5; ++i) {
            TestObject obj(i, "obj_" + std::to_string(i));
            ids.push_back(storage.insert(obj));
        }
        
        // Destroy objects 1, 3 (non-sequential)
        storage.destroy(ids[1]);
        storage.destroy(ids[3]);
        
        // Objects 0, 2, 4 should still exist
        REQUIRE(storage.get(ids[0]) != nullptr);
        REQUIRE(storage.get(ids[2]) != nullptr);
        REQUIRE(storage.get(ids[4]) != nullptr);
        
        // Objects 1, 3 should be gone
        REQUIRE(storage.get(ids[1]) == nullptr);
        REQUIRE(storage.get(ids[3]) == nullptr);
        
        // Verify remaining objects have correct values
        REQUIRE(storage.get(ids[0])->value == 0);
        REQUIRE(storage.get(ids[2])->value == 2);
        REQUIRE(storage.get(ids[4])->value == 4);
    }
}

TEST_CASE("Storage thread safety", "[utils][storage][threading]") {
    Storage<int, TestObject> storage;

    SECTION("Concurrent insertions") {
        const int numThreads = 10;
        const int objectsPerThread = 100;
        std::vector<std::thread> threads;
        std::vector<std::vector<int>> threadIds(numThreads);

        // Create threads that insert objects concurrently
        for (int t = 0; t < numThreads; ++t) {
            threads.emplace_back([&storage, &threadIds, t, objectsPerThread]() {
                for (int i = 0; i < objectsPerThread; ++i) {
                    TestObject obj(t * 1000 + i, "thread_" + std::to_string(t) + "_obj_" + std::to_string(i));
                    int id = storage.insert(obj);
                    threadIds[t].push_back(id);
                }
            });
        }

        // Wait for all threads to complete
        for (auto& thread : threads) {
            thread.join();
        }

        // Verify all objects were inserted correctly
        for (int t = 0; t < numThreads; ++t) {
            for (int i = 0; i < objectsPerThread; ++i) {
                int id = threadIds[t][i];
                TestObject* obj = storage.get(id);

                REQUIRE(obj != nullptr);
                REQUIRE(obj->value == t * 1000 + i);
                REQUIRE(obj->name == "thread_" + std::to_string(t) + "_obj_" + std::to_string(i));
            }
        }

        // Verify all IDs are unique
        std::set<int> allIds;
        for (const auto& ids : threadIds) {
            for (int id : ids) {
                REQUIRE(allIds.find(id) == allIds.end()); // ID should be unique
                allIds.insert(id);
            }
        }

        REQUIRE(allIds.size() == numThreads * objectsPerThread);
    }

    SECTION("Concurrent reads and writes") {
        // Pre-populate storage
        std::vector<int> ids;
        for (int i = 0; i < 50; ++i) {
            TestObject obj(i, "initial_" + std::to_string(i));
            ids.push_back(storage.insert(obj));
        }

        const int numReaderThreads = 5;
        const int numWriterThreads = 3;
        std::vector<std::thread> threads;
        std::atomic<bool> stopFlag{false};
        std::atomic<int> readCount{0};
        std::atomic<int> writeCount{0};

        // Reader threads
        for (int i = 0; i < numReaderThreads; ++i) {
            threads.emplace_back([&storage, &ids, &stopFlag, &readCount]() {
                while (!stopFlag.load()) {
                    for (int id : ids) {
                        TestObject* obj = storage.get(id);
                        if (obj != nullptr) {
                            readCount.fetch_add(1);
                        }
                    }
                }
            });
        }

        // Writer threads
        for (int i = 0; i < numWriterThreads; ++i) {
            threads.emplace_back([&storage, &stopFlag, &writeCount, i]() {
                int counter = 0;
                while (!stopFlag.load()) {
                    TestObject obj(i * 10000 + counter, "writer_" + std::to_string(i) + "_" + std::to_string(counter));
                    storage.insert(obj);
                    writeCount.fetch_add(1);
                    counter++;

                    if (counter > 10) break; // Limit writes to prevent infinite loop
                }
            });
        }

        // Let threads run for a short time
        std::this_thread::sleep_for(std::chrono::milliseconds(100));
        stopFlag.store(true);

        // Wait for all threads to complete
        for (auto& thread : threads) {
            thread.join();
        }

        // Verify that reads and writes occurred
        REQUIRE(readCount.load() > 0);
        REQUIRE(writeCount.load() > 0);
    }

    SECTION("Concurrent destroy operations") {
        // Pre-populate storage
        std::vector<int> ids;
        for (int i = 0; i < 100; ++i) {
            TestObject obj(i, "destroy_test_" + std::to_string(i));
            ids.push_back(storage.insert(obj));
        }

        const int numThreads = 5;
        std::vector<std::thread> threads;

        // Each thread destroys a subset of objects
        for (int t = 0; t < numThreads; ++t) {
            threads.emplace_back([&storage, &ids, t, numThreads]() {
                for (size_t i = t; i < ids.size(); i += numThreads) {
                    storage.destroy(ids[i]);
                }
            });
        }

        // Wait for all threads to complete
        for (auto& thread : threads) {
            thread.join();
        }

        // All objects should be destroyed
        for (int id : ids) {
            REQUIRE(storage.get(id) == nullptr);
        }
    }
}
