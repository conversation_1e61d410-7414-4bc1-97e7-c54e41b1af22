#include <catch2/catch_test_macros.hpp>
#include <catch2/matchers/catch_matchers_string.hpp>

// C++ standard library
#include <memory>
#include <string>

// Third-party libraries
#include <glm/glm.hpp>

// Local includes
#include "gui/widgets/panel_widget.hpp"
#include "gui/widgets/label_widget.hpp"
#include "gui/widgets/image_widget.hpp"
#include "utils/string_id.hpp"
#include "../../test_utils.hpp"
#include "../../mocks/test_mocks.hpp"
#include "../../test_environment.hpp"

using namespace IronFrost;

TEST_CASE("PanelWidget basic functionality", "[gui][widgets][panel_widget][basic]") {
    SECTION("Default constructor") {
        PanelWidget panel;

        REQUIRE(panel.getType() == "panel");

        // Use shared utility for common widget property verification (CLEANUP)
        VERIFY_DEFAULT_WIDGET_STATE(panel);

        // Panel inherits from Widget which doesn't have Colorable functionality
    }
    
    SECTION("Constructor with position and size") {
        glm::vec2 position(100.0f, 200.0f);
        glm::vec2 size(400.0f, 300.0f);

        PanelWidget panel(position, size);

        // Use shared utility for basic property verification (CLEANUP)
        VERIFY_BASIC_WIDGET_PROPERTIES(panel, position, size, "panel", true);
        REQUIRE(panel.isDirty() == true);
    }
}

TEST_CASE("PanelWidget widget management", "[gui][widgets][panel_widget][widgets]") {
    SECTION("Add widget with unique_ptr") {
        PanelWidget panel;
        panel.markClean();
        REQUIRE(panel.isDirty() == false);
        
        StringID widgetId("test_widget");
        auto widget = std::make_unique<Widget>(glm::vec2(10.0f, 20.0f), glm::vec2(50.0f, 30.0f));
        
        panel.addWidget(widgetId, std::move(widget));
        
        REQUIRE(panel.isDirty() == true); // Should mark dirty when widget added
        
        // Retrieve and verify the widget
        Widget& retrievedWidget = panel.getWidget(widgetId);
        REQUIRE(retrievedWidget.getPosition() == glm::vec2(10.0f, 20.0f));
        REQUIRE(retrievedWidget.getSize() == glm::vec2(50.0f, 30.0f));
    }
    
    SECTION("Add widget with raw pointer") {
        PanelWidget panel;
        panel.markClean();
        
        StringID widgetId("raw_widget");
        Widget* rawWidget = new Widget(glm::vec2(15.0f, 25.0f), glm::vec2(60.0f, 40.0f));
        
        panel.addWidgetRaw(widgetId, rawWidget);
        
        REQUIRE(panel.isDirty() == true); // Should mark dirty when widget added
        
        // Retrieve and verify the widget
        Widget& retrievedWidget = panel.getWidget(widgetId);
        REQUIRE(retrievedWidget.getPosition() == glm::vec2(15.0f, 25.0f));
        REQUIRE(retrievedWidget.getSize() == glm::vec2(60.0f, 40.0f));
    }
    
    SECTION("Add multiple widgets") {
        PanelWidget panel;
        
        // Add different types of widgets
        StringID labelId("label");
        StringID imageId("image");
        StringID widgetId("widget");
        
        auto label = std::make_unique<LabelWidget>(StringID("font::test"), "Test Label");
        auto image = std::make_unique<ImageWidget>(StringID("texture::test"));
        auto widget = std::make_unique<Widget>();
        
        panel.addWidget(labelId, std::move(label));
        panel.addWidget(imageId, std::move(image));
        panel.addWidget(widgetId, std::move(widget));
        
        // Verify all widgets can be retrieved
        Widget& retrievedLabel = panel.getWidget(labelId);
        Widget& retrievedImage = panel.getWidget(imageId);
        Widget& retrievedWidget = panel.getWidget(widgetId);
        
        REQUIRE(retrievedLabel.getType() == "label");
        REQUIRE(retrievedImage.getType() == "image");
        REQUIRE(retrievedWidget.getType() == "widget");
    }
    
    SECTION("Remove widget") {
        PanelWidget panel;
        
        StringID widgetId("removable");
        auto widget = std::make_unique<Widget>();
        
        panel.addWidget(widgetId, std::move(widget));
        panel.markClean();
        
        // Verify widget exists
        REQUIRE_NOTHROW(panel.getWidget(widgetId));
        
        // Remove widget
        panel.removeWidget(widgetId);
        
        REQUIRE(panel.isDirty() == true); // Should mark dirty when widget removed
        
        // Verify widget no longer exists
        REQUIRE_THROWS_AS(panel.getWidget(widgetId), std::runtime_error);
    }
    
    SECTION("Get non-existent widget throws exception") {
        PanelWidget panel;
        
        StringID nonExistentId("does_not_exist");
        
        REQUIRE_THROWS_AS(panel.getWidget(nonExistentId), std::runtime_error);
        REQUIRE_THROWS_WITH(panel.getWidget(nonExistentId), Catch::Matchers::ContainsSubstring("Widget not found"));
    }
    
    SECTION("Widget ID collision handling") {
        PanelWidget panel;
        
        StringID duplicateId("duplicate");
        
        auto widget1 = std::make_unique<Widget>(glm::vec2(10.0f, 10.0f), glm::vec2(50.0f, 50.0f));
        auto widget2 = std::make_unique<Widget>(glm::vec2(20.0f, 20.0f), glm::vec2(60.0f, 60.0f));
        
        panel.addWidget(duplicateId, std::move(widget1));
        
        // Verify first widget
        Widget& firstWidget = panel.getWidget(duplicateId);
        REQUIRE(firstWidget.getPosition() == glm::vec2(10.0f, 10.0f));
        
        // Add second widget with same ID (should not replace due to try_emplace)
        panel.addWidget(duplicateId, std::move(widget2));
        
        // Should still have the first widget
        Widget& stillFirstWidget = panel.getWidget(duplicateId);
        REQUIRE(stillFirstWidget.getPosition() == glm::vec2(10.0f, 10.0f));
    }
}

TEST_CASE("PanelWidget dirty state management", "[gui][widgets][panel_widget][dirty]") {
    SECTION("Panel dirty when child widget is dirty") {
        PanelWidget panel;
        
        StringID widgetId("child");
        auto widget = std::make_unique<Widget>();
        widget->markClean(); // Start clean
        
        panel.addWidget(widgetId, std::move(widget));
        panel.markClean(); // Mark panel and all children clean
        
        REQUIRE(panel.isDirty() == false);
        
        // Make child widget dirty
        Widget& childWidget = panel.getWidget(widgetId);
        childWidget.markDirty();
        
        // Panel should now be dirty because child is dirty
        REQUIRE(panel.isDirty() == true);
    }
    
    SECTION("markDirty propagates to all children") {
        PanelWidget panel;
        
        StringID widget1Id("widget1");
        StringID widget2Id("widget2");
        
        auto widget1 = std::make_unique<Widget>();
        auto widget2 = std::make_unique<Widget>();
        
        panel.addWidget(widget1Id, std::move(widget1));
        panel.addWidget(widget2Id, std::move(widget2));
        
        panel.markClean(); // Mark all clean
        
        REQUIRE(panel.isDirty() == false);
        REQUIRE(panel.getWidget(widget1Id).isDirty() == false);
        REQUIRE(panel.getWidget(widget2Id).isDirty() == false);
        
        // Mark panel dirty
        panel.markDirty();
        
        // All should be dirty
        REQUIRE(panel.isDirty() == true);
        REQUIRE(panel.getWidget(widget1Id).isDirty() == true);
        REQUIRE(panel.getWidget(widget2Id).isDirty() == true);
    }
    
    SECTION("markClean propagates to all children") {
        PanelWidget panel;
        
        StringID widget1Id("widget1");
        StringID widget2Id("widget2");
        
        auto widget1 = std::make_unique<Widget>();
        auto widget2 = std::make_unique<Widget>();
        
        // Start with dirty widgets
        widget1->markDirty();
        widget2->markDirty();
        
        panel.addWidget(widget1Id, std::move(widget1));
        panel.addWidget(widget2Id, std::move(widget2));
        
        REQUIRE(panel.isDirty() == true);
        REQUIRE(panel.getWidget(widget1Id).isDirty() == true);
        REQUIRE(panel.getWidget(widget2Id).isDirty() == true);
        
        // Mark panel clean
        panel.markClean();
        
        // All should be clean
        REQUIRE(panel.isDirty() == false);
        REQUIRE(panel.getWidget(widget1Id).isDirty() == false);
        REQUIRE(panel.getWidget(widget2Id).isDirty() == false);
    }
}

TEST_CASE("PanelWidget update functionality", "[gui][widgets][panel_widget][update]") {
    SKIP_IF_HEADLESS();

    SECTION("Update propagates to all child widgets when visible") {
        PanelWidget panel(glm::vec2(0.0f, 0.0f), glm::vec2(200.0f, 200.0f));
        
        MockKeyboard mockKeyboard;
        MockMouse mockMouse;
        GameContext context = createTestGameContext(mockKeyboard, mockMouse);
        
        // Add child widgets
        StringID widget1Id("widget1");
        StringID widget2Id("widget2");
        
        auto widget1 = std::make_unique<Widget>(glm::vec2(10.0f, 10.0f), glm::vec2(50.0f, 50.0f));
        auto widget2 = std::make_unique<Widget>(glm::vec2(70.0f, 70.0f), glm::vec2(50.0f, 50.0f));
        
        panel.addWidget(widget1Id, std::move(widget1));
        panel.addWidget(widget2Id, std::move(widget2));
        
        // Set up mouse to hover over first widget
        mockMouse.setPosition(35.0f, 35.0f); // Center of first widget
        
        panel.update(0.016f, context);
        
        // First widget should be hovered
        REQUIRE(panel.getWidget(widget1Id).isHovered() == true);
        REQUIRE(panel.getWidget(widget2Id).isHovered() == false);
        
        // Move mouse to second widget
        mockMouse.setPosition(95.0f, 95.0f); // Center of second widget
        
        panel.update(0.016f, context);
        
        // Second widget should be hovered
        REQUIRE(panel.getWidget(widget1Id).isHovered() == false);
        REQUIRE(panel.getWidget(widget2Id).isHovered() == true);
    }
    
    SECTION("Update does not propagate when panel is invisible") {
        PanelWidget panel(glm::vec2(0.0f, 0.0f), glm::vec2(200.0f, 200.0f));
        panel.setVisible(false);
        
        MockKeyboard mockKeyboard;
        MockMouse mockMouse;
        GameContext context = createTestGameContext(mockKeyboard, mockMouse);
        
        // Add child widget
        StringID widgetId("child");
        auto widget = std::make_unique<Widget>(glm::vec2(10.0f, 10.0f), glm::vec2(50.0f, 50.0f));
        
        panel.addWidget(widgetId, std::move(widget));
        
        // Set up mouse to hover over widget
        mockMouse.setPosition(35.0f, 35.0f);
        mockMouse.setButtonPressed(MOUSE_BUTTON_LEFT, true);
        
        panel.update(0.016f, context);
        
        // Child widget should not be hovered or pressed because panel is invisible
        REQUIRE(panel.getWidget(widgetId).isHovered() == false);
        REQUIRE(panel.getWidget(widgetId).isPressed() == false);
        REQUIRE(panel.isHovered() == false);
        REQUIRE(panel.isPressed() == false);
    }
}

TEST_CASE("PanelWidget traversal functionality", "[gui][widgets][panel_widget][traversal]") {
    SECTION("Traverse all widgets") {
        PanelWidget panel;
        
        // Add multiple widgets
        StringID label1Id("label1");
        StringID label2Id("label2");
        StringID imageId("image");
        
        auto label1 = std::make_unique<LabelWidget>(StringID("font::test"), "Label 1");
        auto label2 = std::make_unique<LabelWidget>(StringID("font::test"), "Label 2");
        auto image = std::make_unique<ImageWidget>(StringID("texture::test"));
        
        panel.addWidget(label1Id, std::move(label1));
        panel.addWidget(label2Id, std::move(label2));
        panel.addWidget(imageId, std::move(image));
        
        // Count widgets and collect types
        int widgetCount = 0;
        std::vector<std::string> types;
        
        panel.traverseWidgets([&widgetCount, &types](Widget& widget) {
            widgetCount++;
            types.push_back(widget.getType());
        });
        
        REQUIRE(widgetCount == 3);
        REQUIRE(types.size() == 3);
        
        // Check that we have the expected types (order may vary due to unordered_map)
        std::sort(types.begin(), types.end());
        std::vector<std::string> expectedTypes = {"image", "label", "label"};
        std::sort(expectedTypes.begin(), expectedTypes.end());
        REQUIRE(types == expectedTypes);
    }
    
    SECTION("Traverse empty panel") {
        PanelWidget panel;
        
        int widgetCount = 0;
        panel.traverseWidgets([&widgetCount](Widget& widget) {
            widgetCount++;
        });
        
        REQUIRE(widgetCount == 0);
    }
    
    SECTION("Modify widgets during traversal") {
        PanelWidget panel;
        
        // Add widgets
        StringID widget1Id("widget1");
        StringID widget2Id("widget2");
        
        auto widget1 = std::make_unique<Widget>();
        auto widget2 = std::make_unique<Widget>();
        
        // Base Widget doesn't have color functionality
        
        panel.addWidget(widget1Id, std::move(widget1));
        panel.addWidget(widget2Id, std::move(widget2));
        
        // Change all widgets' visibility during traversal
        panel.traverseWidgets([](Widget& widget) {
            widget.setVisible(false);
        });

        // Verify all widgets are now invisible
        REQUIRE_FALSE(panel.getWidget(widget1Id).isVisible());
        REQUIRE_FALSE(panel.getWidget(widget2Id).isVisible());
    }
}

TEST_CASE("PanelWidget inheritance functionality", "[gui][widgets][panel_widget][inheritance]") {
    SKIP_IF_HEADLESS();

    SECTION("Widget functionality - visibility and interaction") {
        PanelWidget panel(glm::vec2(50.0f, 50.0f), glm::vec2(200.0f, 150.0f));

        MockKeyboard mockKeyboard;
        MockMouse mockMouse;
        GameContext context = createTestGameContext(mockKeyboard, mockMouse);

        // Test visibility
        REQUIRE(panel.isVisible() == true);
        panel.setVisible(false);
        REQUIRE(panel.isVisible() == false);

        // Test interaction when invisible
        mockMouse.setPosition(150.0f, 125.0f); // Inside bounds
        mockMouse.setButtonPressed(MOUSE_BUTTON_LEFT, true);
        panel.update(0.016f, context);

        REQUIRE_FALSE(panel.isHovered()); // Should not hover when invisible
        REQUIRE_FALSE(panel.isPressed()); // Should not press when invisible

        // Test interaction when visible
        panel.setVisible(true);
        panel.update(0.016f, context);

        REQUIRE(panel.isHovered()); // Should hover when visible and mouse inside
        REQUIRE(panel.isPressed()); // Should press when visible and mouse pressed
    }

    SECTION("Transformable functionality - position and size") {
        PanelWidget panel;

        // Test position changes
        glm::vec2 newPosition(300.0f, 400.0f);
        panel.setPosition(newPosition);
        REQUIRE(panel.getPosition() == newPosition);
        REQUIRE(panel.isDirty() == true);

        panel.markClean();

        // Test size changes
        glm::vec2 newSize(500.0f, 350.0f);
        panel.setSize(newSize);
        REQUIRE(panel.getSize() == newSize);
        REQUIRE(panel.isDirty() == true);

        panel.markClean();

        // Test rotation changes
        float newRotation = 30.0f;
        panel.setRotation(newRotation);
        REQUIRE(panel.getRotation() == newRotation);
        REQUIRE(panel.isDirty() == true);
    }

    // Note: PanelWidget inherits from Widget which doesn't have Colorable functionality
    // Individual child widgets (like ImageWidget, LabelWidget) have color functionality

    SECTION("Callbackable functionality - callbacks") {
        PanelWidget panel;
        bool hoverTriggered = false;
        bool clickTriggered = false;

        panel.setCallback("onHover", [&hoverTriggered]() {
            hoverTriggered = true;
        });

        panel.setCallback("onClick", [&clickTriggered]() {
            clickTriggered = true;
        });

        // Manually trigger callbacks to test integration
        panel.triggerCallback("onHover");
        panel.triggerCallback("onClick");

        REQUIRE(hoverTriggered);
        REQUIRE(clickTriggered);
    }
}

TEST_CASE("PanelWidget edge cases", "[gui][widgets][panel_widget][edge_cases]") {
    SKIP_IF_HEADLESS();

    SECTION("Empty StringID for widget") {
        PanelWidget panel;

        StringID emptyId("");
        auto widget = std::make_unique<Widget>();

        panel.addWidget(emptyId, std::move(widget));

        // Should be able to retrieve with empty ID
        REQUIRE_NOTHROW(panel.getWidget(emptyId));

        Widget& retrievedWidget = panel.getWidget(emptyId);
        REQUIRE(retrievedWidget.getType() == "widget");
    }

    SECTION("Very long StringID") {
        PanelWidget panel;

        std::string longIdString(1000, 'a');
        StringID longId(longIdString);
        auto widget = std::make_unique<Widget>();

        panel.addWidget(longId, std::move(widget));

        REQUIRE_NOTHROW(panel.getWidget(longId));
    }

    SECTION("Special characters in StringID") {
        PanelWidget panel;

        std::vector<std::string> specialIds = {
            "widget@#$%",
            "widget with spaces",
            "widget-with-dashes",
            "widget_with_underscores",
            "widget.with.dots",
            "тест", // Unicode
            "🎮"   // Emoji
        };

        for (const auto& idString : specialIds) {
            StringID id(idString);
            auto widget = std::make_unique<Widget>();

            panel.addWidget(id, std::move(widget));
            REQUIRE_NOTHROW(panel.getWidget(id));
        }
    }

    SECTION("Zero size panel") {
        glm::vec2 zeroSize(0.0f, 0.0f);
        PanelWidget panel(glm::vec2(100.0f, 100.0f), zeroSize);

        REQUIRE(panel.getSize() == zeroSize);

        MockKeyboard mockKeyboard;
        MockMouse mockMouse;
        GameContext context = createTestGameContext(mockKeyboard, mockMouse);

        // Mouse at the exact position should not trigger hover (size is 0)
        mockMouse.setPosition(100.0f, 100.0f);
        panel.update(0.016f, context);

        REQUIRE_FALSE(panel.isHovered());
    }

    SECTION("Negative position") {
        glm::vec2 negativePosition(-100.0f, -50.0f);
        PanelWidget panel(negativePosition, glm::vec2(200.0f, 150.0f));

        REQUIRE(panel.getPosition() == negativePosition);
    }

    SECTION("Large position and size values") {
        glm::vec2 largePosition(1e6f, 1e6f);
        glm::vec2 largeSize(1e5f, 1e4f);
        PanelWidget panel(largePosition, largeSize);

        REQUIRE(panel.getPosition() == largePosition);
        REQUIRE(panel.getSize() == largeSize);
    }

    SECTION("Remove non-existent widget") {
        PanelWidget panel;

        StringID nonExistentId("does_not_exist");

        // Should not throw when removing non-existent widget
        REQUIRE_NOTHROW(panel.removeWidget(nonExistentId));
    }
}

TEST_CASE("PanelWidget real-world scenarios", "[gui][widgets][panel_widget][real_world]") {
    SKIP_IF_HEADLESS();

    SECTION("Main menu panel with buttons") {
        PanelWidget mainMenu(glm::vec2(100.0f, 100.0f), glm::vec2(400.0f, 300.0f));

        // Add menu buttons
        StringID startButtonId("start_button");
        StringID optionsButtonId("options_button");
        StringID exitButtonId("exit_button");

        auto startButton = std::make_unique<LabelWidget>(StringID("font::menu"), "Start Game");
        auto optionsButton = std::make_unique<LabelWidget>(StringID("font::menu"), "Options");
        auto exitButton = std::make_unique<LabelWidget>(StringID("font::menu"), "Exit");

        startButton->setPosition(glm::vec2(150.0f, 150.0f));
        startButton->setSize(glm::vec2(120.0f, 40.0f));

        optionsButton->setPosition(glm::vec2(150.0f, 200.0f));
        optionsButton->setSize(glm::vec2(120.0f, 40.0f));

        exitButton->setPosition(glm::vec2(150.0f, 250.0f));
        exitButton->setSize(glm::vec2(120.0f, 40.0f));

        mainMenu.addWidget(startButtonId, std::move(startButton));
        mainMenu.addWidget(optionsButtonId, std::move(optionsButton));
        mainMenu.addWidget(exitButtonId, std::move(exitButton));

        // Verify all buttons are accessible
        REQUIRE(mainMenu.getWidget(startButtonId).getType() == "label");
        REQUIRE(mainMenu.getWidget(optionsButtonId).getType() == "label");
        REQUIRE(mainMenu.getWidget(exitButtonId).getType() == "label");

        // Test interaction with buttons
        MockKeyboard mockKeyboard;
        MockMouse mockMouse;
        GameContext context = createTestGameContext(mockKeyboard, mockMouse);

        // Click on start button
        mockMouse.setPosition(210.0f, 170.0f); // Center of start button
        mockMouse.setButtonPressed(MOUSE_BUTTON_LEFT, true);

        mainMenu.update(0.016f, context);

        REQUIRE(mainMenu.getWidget(startButtonId).isHovered());
        REQUIRE(mainMenu.getWidget(startButtonId).isPressed());
        REQUIRE_FALSE(mainMenu.getWidget(optionsButtonId).isHovered());
        REQUIRE_FALSE(mainMenu.getWidget(exitButtonId).isHovered());
    }

    SECTION("Game HUD panel with multiple elements") {
        PanelWidget hudPanel(glm::vec2(0.0f, 0.0f), glm::vec2(800.0f, 100.0f));

        // Add HUD elements
        StringID healthBarId("health_bar");
        StringID scoreId("score");
        StringID miniMapId("mini_map");

        auto healthBar = std::make_unique<ImageWidget>(StringID("texture::ui::health_bar"));
        auto scoreLabel = std::make_unique<LabelWidget>(StringID("font::hud"), "Score: 0");
        auto miniMap = std::make_unique<ImageWidget>(StringID("texture::ui::minimap"));

        healthBar->setPosition(glm::vec2(10.0f, 10.0f));
        healthBar->setSize(glm::vec2(200.0f, 20.0f));
        healthBar->setColor(glm::vec3(1.0f, 0.0f, 0.0f)); // Red

        scoreLabel->setPosition(glm::vec2(250.0f, 10.0f));
        scoreLabel->setSize(glm::vec2(150.0f, 30.0f));
        scoreLabel->setColor(glm::vec3(1.0f, 1.0f, 1.0f)); // White

        miniMap->setPosition(glm::vec2(650.0f, 10.0f));
        miniMap->setSize(glm::vec2(80.0f, 80.0f));

        hudPanel.addWidget(healthBarId, std::move(healthBar));
        hudPanel.addWidget(scoreId, std::move(scoreLabel));
        hudPanel.addWidget(miniMapId, std::move(miniMap));

        // Verify HUD elements
        REQUIRE(hudPanel.getWidget(healthBarId).getType() == "image");
        REQUIRE(hudPanel.getWidget(scoreId).getType() == "label");
        REQUIRE(hudPanel.getWidget(miniMapId).getType() == "image");

        // Test updating score
        LabelWidget& scoreWidget = static_cast<LabelWidget&>(hudPanel.getWidget(scoreId));
        scoreWidget.setText("Score: 1500");

        REQUIRE(scoreWidget.getText() == "Score: 1500");
        REQUIRE(hudPanel.isDirty() == true); // Panel should be dirty when child changes
    }

    SECTION("Dialog panel with dynamic content") {
        PanelWidget dialogPanel(glm::vec2(200.0f, 150.0f), glm::vec2(400.0f, 200.0f));

        // Add dialog elements
        StringID backgroundId("background");
        StringID titleId("title");
        StringID textId("text");
        StringID okButtonId("ok_button");
        StringID cancelButtonId("cancel_button");

        auto background = std::make_unique<ImageWidget>(StringID("texture::ui::dialog_bg"));
        auto title = std::make_unique<LabelWidget>(StringID("font::dialog_title"), "Confirmation");
        auto text = std::make_unique<LabelWidget>(StringID("font::dialog_text"), "Are you sure you want to quit?");
        auto okButton = std::make_unique<LabelWidget>(StringID("font::button"), "OK");
        auto cancelButton = std::make_unique<LabelWidget>(StringID("font::button"), "Cancel");

        background->setPosition(glm::vec2(200.0f, 150.0f));
        background->setSize(glm::vec2(400.0f, 200.0f));

        title->setPosition(glm::vec2(220.0f, 170.0f));
        title->setSize(glm::vec2(360.0f, 30.0f));

        text->setPosition(glm::vec2(220.0f, 210.0f));
        text->setSize(glm::vec2(360.0f, 60.0f));

        okButton->setPosition(glm::vec2(450.0f, 300.0f));
        okButton->setSize(glm::vec2(60.0f, 30.0f));

        cancelButton->setPosition(glm::vec2(520.0f, 300.0f));
        cancelButton->setSize(glm::vec2(60.0f, 30.0f));

        dialogPanel.addWidget(backgroundId, std::move(background));
        dialogPanel.addWidget(titleId, std::move(title));
        dialogPanel.addWidget(textId, std::move(text));
        dialogPanel.addWidget(okButtonId, std::move(okButton));
        dialogPanel.addWidget(cancelButtonId, std::move(cancelButton));

        // Test dialog content changes
        LabelWidget& titleWidget = static_cast<LabelWidget&>(dialogPanel.getWidget(titleId));
        LabelWidget& textWidget = static_cast<LabelWidget&>(dialogPanel.getWidget(textId));

        titleWidget.setText("Warning");
        textWidget.setText("This action cannot be undone!");

        REQUIRE(titleWidget.getText() == "Warning");
        REQUIRE(textWidget.getText() == "This action cannot be undone!");
    }

    SECTION("Inventory panel with grid layout simulation") {
        PanelWidget inventoryPanel(glm::vec2(50.0f, 50.0f), glm::vec2(300.0f, 400.0f));

        // Simulate a 3x4 grid of inventory slots
        const int gridWidth = 3;
        const int gridHeight = 4;
        const float slotSize = 80.0f;
        const float padding = 10.0f;

        for (int y = 0; y < gridHeight; ++y) {
            for (int x = 0; x < gridWidth; ++x) {
                StringID slotId("slot_" + std::to_string(y * gridWidth + x));

                auto slot = std::make_unique<ImageWidget>(StringID("texture::ui::inventory_slot"));

                float posX = 60.0f + x * (slotSize + padding);
                float posY = 60.0f + y * (slotSize + padding);

                slot->setPosition(glm::vec2(posX, posY));
                slot->setSize(glm::vec2(slotSize, slotSize));

                inventoryPanel.addWidget(slotId, std::move(slot));
            }
        }

        // Verify all slots were added
        int slotCount = 0;
        inventoryPanel.traverseWidgets([&slotCount](Widget& widget) {
            if (widget.getType() == "image") {
                slotCount++;
            }
        });

        REQUIRE(slotCount == gridWidth * gridHeight);

        // Test accessing specific slots
        REQUIRE_NOTHROW(inventoryPanel.getWidget(StringID("slot_0")));  // Top-left
        REQUIRE_NOTHROW(inventoryPanel.getWidget(StringID("slot_11"))); // Bottom-right

        // Test removing an item (slot)
        inventoryPanel.removeWidget(StringID("slot_5"));
        REQUIRE_THROWS_AS(inventoryPanel.getWidget(StringID("slot_5")), std::runtime_error);
    }

    SECTION("Settings panel with form elements") {
        PanelWidget settingsPanel(glm::vec2(100.0f, 100.0f), glm::vec2(500.0f, 400.0f));

        // Add form elements
        StringID titleId("title");
        StringID volumeLabelId("volume_label");
        StringID volumeSliderBgId("volume_slider_bg");
        StringID volumeSliderHandleId("volume_slider_handle");
        StringID fullscreenLabelId("fullscreen_label");
        StringID fullscreenCheckboxId("fullscreen_checkbox");
        StringID applyButtonId("apply_button");
        StringID cancelButtonId("cancel_button");

        auto title = std::make_unique<LabelWidget>(StringID("font::title"), "Settings");
        auto volumeLabel = std::make_unique<LabelWidget>(StringID("font::label"), "Volume:");
        auto volumeSliderBg = std::make_unique<ImageWidget>(StringID("texture::ui::slider_bg"));
        auto volumeSliderHandle = std::make_unique<ImageWidget>(StringID("texture::ui::slider_handle"));
        auto fullscreenLabel = std::make_unique<LabelWidget>(StringID("font::label"), "Fullscreen:");
        auto fullscreenCheckbox = std::make_unique<ImageWidget>(StringID("texture::ui::checkbox_unchecked"));
        auto applyButton = std::make_unique<LabelWidget>(StringID("font::button"), "Apply");
        auto cancelButton = std::make_unique<LabelWidget>(StringID("font::button"), "Cancel");

        // Position elements
        title->setPosition(glm::vec2(250.0f, 120.0f));
        volumeLabel->setPosition(glm::vec2(120.0f, 180.0f));
        volumeSliderBg->setPosition(glm::vec2(200.0f, 180.0f));
        volumeSliderHandle->setPosition(glm::vec2(300.0f, 175.0f)); // 50% position
        fullscreenLabel->setPosition(glm::vec2(120.0f, 220.0f));
        fullscreenCheckbox->setPosition(glm::vec2(220.0f, 220.0f));
        applyButton->setPosition(glm::vec2(400.0f, 450.0f));
        cancelButton->setPosition(glm::vec2(500.0f, 450.0f));

        settingsPanel.addWidget(titleId, std::move(title));
        settingsPanel.addWidget(volumeLabelId, std::move(volumeLabel));
        settingsPanel.addWidget(volumeSliderBgId, std::move(volumeSliderBg));
        settingsPanel.addWidget(volumeSliderHandleId, std::move(volumeSliderHandle));
        settingsPanel.addWidget(fullscreenLabelId, std::move(fullscreenLabel));
        settingsPanel.addWidget(fullscreenCheckboxId, std::move(fullscreenCheckbox));
        settingsPanel.addWidget(applyButtonId, std::move(applyButton));
        settingsPanel.addWidget(cancelButtonId, std::move(cancelButton));

        // Test changing settings
        ImageWidget& checkbox = static_cast<ImageWidget&>(settingsPanel.getWidget(fullscreenCheckboxId));
        checkbox.setTextureName(StringID("texture::ui::checkbox_checked"));

        REQUIRE(checkbox.getTextureName() == StringID("texture::ui::checkbox_checked"));

        // Test slider interaction
        ImageWidget& sliderHandle = static_cast<ImageWidget&>(settingsPanel.getWidget(volumeSliderHandleId));
        sliderHandle.setPosition(glm::vec2(350.0f, 175.0f)); // Move to 75% position

        REQUIRE(sliderHandle.getPosition().x == 350.0f);
    }
}
