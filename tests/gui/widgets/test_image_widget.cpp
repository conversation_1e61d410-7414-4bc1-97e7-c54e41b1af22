#include <catch2/catch_test_macros.hpp>

// C++ standard library
#include <memory>

// Third-party libraries
#include <glm/glm.hpp>

// Local includes
#include "gui/widgets/image_widget.hpp"
#include "renderer/fallback_resources.hpp"
#include "utils/string_id.hpp"
#include "../../test_environment.hpp"
#include "../../test_utils.hpp"
#include "../../mocks/test_mocks.hpp"

using namespace IronFrost;

TEST_CASE("ImageWidget basic functionality", "[gui][widgets][image_widget][basic]") {
    SECTION("Default constructor uses fallback texture") {
        ImageWidget imageWidget;

        REQUIRE(imageWidget.getTextureName() == FallbackResources::FALLBACK_TEXTURE_NAME);
        REQUIRE(imageWidget.getType() == "image");

        // Use shared utilities to verify common properties (CLEANUP EXAMPLE)
        VERIFY_DEFAULT_WIDGET_STATE(imageWidget);
        VERIFY_DEFAULT_COLORABLE_PROPERTIES(imageWidget);
    }
    
    SECTION("Constructor with texture name") {
        StringID customTexture("texture::custom::test");
        ImageWidget imageWidget(customTexture);
        
        REQUIRE(imageWidget.getTextureName() == customTexture);
        REQUIRE(imageWidget.getType() == "image");
        
        // Should still have default position and size
        REQUIRE(imageWidget.getPosition() == glm::vec2(0.0f, 0.0f));
        REQUIRE(imageWidget.getSize() == glm::vec2(10.0f, 10.0f));
    }
    
    SECTION("Constructor with position, size, and texture") {
        glm::vec2 position(100.0f, 200.0f);
        glm::vec2 size(150.0f, 100.0f);
        StringID texture("texture::ui::button");
        
        ImageWidget imageWidget(position, size, texture);
        
        REQUIRE(imageWidget.getPosition() == position);
        REQUIRE(imageWidget.getSize() == size);
        REQUIRE(imageWidget.getTextureName() == texture);
        REQUIRE(imageWidget.getType() == "image");
    }
}

TEST_CASE("ImageWidget texture management", "[gui][widgets][image_widget][texture]") {
    SECTION("Set and get texture name") {
        ImageWidget imageWidget;
        
        StringID newTexture("texture::ui::icon");
        imageWidget.setTextureName(newTexture);
        
        REQUIRE(imageWidget.getTextureName() == newTexture);
        REQUIRE(imageWidget.isDirty() == true); // Should mark dirty when texture changes
    }
    
    SECTION("Setting texture marks widget as dirty") {
        ImageWidget imageWidget;
        imageWidget.markClean();
        REQUIRE(imageWidget.isDirty() == false);
        
        StringID newTexture("texture::ui::background");
        imageWidget.setTextureName(newTexture);
        
        REQUIRE(imageWidget.isDirty() == true);
    }
    
    SECTION("Multiple texture changes") {
        ImageWidget imageWidget;
        
        std::vector<StringID> textures = {
            StringID("texture::ui::button"),
            StringID("texture::ui::icon"),
            StringID("texture::ui::background"),
            StringID("texture::game::sprite"),
            FallbackResources::FALLBACK_TEXTURE_NAME
        };
        
        for (const auto& texture : textures) {
            imageWidget.setTextureName(texture);
            REQUIRE(imageWidget.getTextureName() == texture);
            REQUIRE(imageWidget.isDirty() == true);
            imageWidget.markClean();
        }
    }
    
    SECTION("Texture name with special characters") {
        ImageWidget imageWidget;
        
        std::vector<std::string> specialTextures = {
            "texture::ui::button-normal",
            "texture::ui::button_hover",
            "texture::ui::button.pressed",
            "texture::ui::button@2x",
            "texture::ui::button#variant",
            "texture::ui::button with spaces",
            "texture::ui::тест", // Unicode
            "texture::ui::🎮" // Emoji
        };
        
        for (const auto& textureName : specialTextures) {
            StringID texture(textureName);
            imageWidget.setTextureName(texture);
            REQUIRE(imageWidget.getTextureName() == texture);
        }
    }
}

TEST_CASE("ImageWidget inheritance functionality", "[gui][widgets][image_widget][inheritance]") {
    SKIP_IF_HEADLESS();

    SECTION("Widget functionality - visibility and interaction") {
        ImageWidget imageWidget(glm::vec2(50.0f, 50.0f), glm::vec2(100.0f, 100.0f), StringID("test_texture"));
        
        // Use shared mock classes (CLEANUP EXAMPLE)
        MockKeyboard mockKeyboard;
        MockMouse mockMouse;
        GameContext context = createTestGameContext(mockKeyboard, mockMouse);

        // Test visibility
        REQUIRE(imageWidget.isVisible() == true);
        imageWidget.setVisible(false);
        REQUIRE(imageWidget.isVisible() == false);

        // Test interaction when invisible using shared utility (CLEANUP EXAMPLE)
        setupMouseClickWidget(mockMouse, imageWidget.getPosition(), imageWidget.getSize());
        imageWidget.update(0.016f, context);
        
        REQUIRE_FALSE(imageWidget.isHovered()); // Should not hover when invisible
        REQUIRE_FALSE(imageWidget.isPressed()); // Should not press when invisible
        
        // Test interaction when visible
        imageWidget.setVisible(true);
        imageWidget.update(0.016f, context);
        
        REQUIRE(imageWidget.isHovered()); // Should hover when visible and mouse inside
        REQUIRE(imageWidget.isPressed()); // Should press when visible and mouse pressed
    }
    
    SECTION("Transformable functionality - position and size") {
        ImageWidget imageWidget;
        
        // Test position changes
        glm::vec2 newPosition(200.0f, 300.0f);
        imageWidget.setPosition(newPosition);
        REQUIRE(imageWidget.getPosition() == newPosition);
        REQUIRE(imageWidget.isDirty() == true);
        
        imageWidget.markClean();
        
        // Test size changes
        glm::vec2 newSize(250.0f, 180.0f);
        imageWidget.setSize(newSize);
        REQUIRE(imageWidget.getSize() == newSize);
        REQUIRE(imageWidget.isDirty() == true);
        
        imageWidget.markClean();
        
        // Test rotation changes
        float newRotation = 45.0f;
        imageWidget.setRotation(newRotation);
        REQUIRE(imageWidget.getRotation() == newRotation);
        REQUIRE(imageWidget.isDirty() == true);
    }
    
    SECTION("Colorable functionality - color and alpha") {
        ImageWidget imageWidget;
        
        // Test color changes
        glm::vec3 newColor(0.8f, 0.2f, 0.4f);
        imageWidget.setColor(newColor);
        
        const auto& color = imageWidget.getColor();
        REQUIRE(color.r == 0.8f);
        REQUIRE(color.g == 0.2f);
        REQUIRE(color.b == 0.4f);
        
        // Test alpha changes
        float newAlpha = 0.7f;
        imageWidget.setAlpha(newAlpha);
        REQUIRE(imageWidget.getAlpha() == newAlpha);
        
        // Color should remain unchanged when alpha changes
        const auto& colorAfterAlpha = imageWidget.getColor();
        REQUIRE(colorAfterAlpha.r == 0.8f);
        REQUIRE(colorAfterAlpha.g == 0.2f);
        REQUIRE(colorAfterAlpha.b == 0.4f);
    }
    
    SECTION("Callbackable functionality - callbacks") {
        ImageWidget imageWidget;
        bool hoverTriggered = false;
        bool clickTriggered = false;
        
        imageWidget.setCallback("onHover", [&hoverTriggered]() {
            hoverTriggered = true;
        });
        
        imageWidget.setCallback("onClick", [&clickTriggered]() {
            clickTriggered = true;
        });
        
        // Manually trigger callbacks to test integration
        imageWidget.triggerCallback("onHover");
        imageWidget.triggerCallback("onClick");
        
        REQUIRE(hoverTriggered);
        REQUIRE(clickTriggered);
    }
}

TEST_CASE("ImageWidget edge cases", "[gui][widgets][image_widget][edge_cases]") {
    SKIP_IF_HEADLESS();

    SECTION("Empty texture name") {
        StringID emptyTexture("");
        ImageWidget imageWidget(emptyTexture);
        
        REQUIRE(imageWidget.getTextureName() == emptyTexture);
    }
    
    SECTION("Very long texture name") {
        std::string longTextureName(1000, 'a');
        StringID longTexture(longTextureName);
        ImageWidget imageWidget(longTexture);
        
        REQUIRE(imageWidget.getTextureName() == longTexture);
    }
    
    SECTION("Zero size image widget") {
        glm::vec2 zeroSize(0.0f, 0.0f);
        ImageWidget imageWidget(glm::vec2(100.0f, 100.0f), zeroSize, StringID("test"));
        
        REQUIRE(imageWidget.getSize() == zeroSize);
        
        MockKeyboard mockKeyboard;
        MockMouse mockMouse;
        GameContext context = createTestGameContext(mockKeyboard, mockMouse);
        
        // Mouse at the exact position should not trigger hover (size is 0)
        mockMouse.setPosition(100.0f, 100.0f);
        imageWidget.update(0.016f, context);
        
        REQUIRE_FALSE(imageWidget.isHovered());
    }
    
    SECTION("Negative position") {
        glm::vec2 negativePosition(-50.0f, -100.0f);
        ImageWidget imageWidget(negativePosition, glm::vec2(50.0f, 50.0f), StringID("test"));
        
        REQUIRE(imageWidget.getPosition() == negativePosition);
    }
    
    SECTION("Large position and size values") {
        glm::vec2 largePosition(1e6f, 1e6f);
        glm::vec2 largeSize(1e5f, 1e5f);
        ImageWidget imageWidget(largePosition, largeSize, StringID("test"));
        
        REQUIRE(imageWidget.getPosition() == largePosition);
        REQUIRE(imageWidget.getSize() == largeSize);
    }
}

TEST_CASE("ImageWidget real-world scenarios", "[gui][widgets][image_widget][real_world]") {
    SKIP_IF_HEADLESS();

    SECTION("UI button with different states") {
        ImageWidget button(glm::vec2(100.0f, 50.0f), glm::vec2(120.0f, 40.0f), StringID("texture::ui::button_normal"));

        // Normal state
        REQUIRE(button.getTextureName() == StringID("texture::ui::button_normal"));
        REQUIRE(button.getColor() == glm::vec3(1.0f, 1.0f, 1.0f));
        REQUIRE(button.getAlpha() == 1.0f);

        // Hover state - change texture and color
        button.setTextureName(StringID("texture::ui::button_hover"));
        button.setColor(glm::vec3(1.1f, 1.1f, 1.1f)); // Slightly brighter

        REQUIRE(button.getTextureName() == StringID("texture::ui::button_hover"));
        REQUIRE(button.getColor() == glm::vec3(1.1f, 1.1f, 1.1f));

        // Pressed state - change texture and color
        button.setTextureName(StringID("texture::ui::button_pressed"));
        button.setColor(glm::vec3(0.9f, 0.9f, 0.9f)); // Slightly darker

        REQUIRE(button.getTextureName() == StringID("texture::ui::button_pressed"));
        REQUIRE(button.getColor() == glm::vec3(0.9f, 0.9f, 0.9f));

        // Disabled state - change texture and alpha
        button.setTextureName(StringID("texture::ui::button_disabled"));
        button.setColor(glm::vec3(0.5f, 0.5f, 0.5f)); // Grayed out
        button.setAlpha(0.5f); // Semi-transparent

        REQUIRE(button.getTextureName() == StringID("texture::ui::button_disabled"));
        REQUIRE(button.getColor() == glm::vec3(0.5f, 0.5f, 0.5f));
        REQUIRE(button.getAlpha() == 0.5f);
    }

    SECTION("Game sprite with animation frames") {
        ImageWidget sprite(glm::vec2(200.0f, 300.0f), glm::vec2(64.0f, 64.0f), StringID("sprite::character::idle_01"));

        // Animation frame sequence
        std::vector<StringID> animationFrames = {
            StringID("sprite::character::idle_01"),
            StringID("sprite::character::idle_02"),
            StringID("sprite::character::idle_03"),
            StringID("sprite::character::idle_04"),
            StringID("sprite::character::idle_05"),
            StringID("sprite::character::idle_06")
        };

        // Simulate animation by changing frames
        for (size_t i = 0; i < animationFrames.size(); ++i) {
            sprite.setTextureName(animationFrames[i]);
            REQUIRE(sprite.getTextureName() == animationFrames[i]);
            REQUIRE(sprite.isDirty() == true); // Each frame change should mark dirty
            sprite.markClean();
        }

        // Change to different animation
        sprite.setTextureName(StringID("sprite::character::walk_01"));
        REQUIRE(sprite.getTextureName() == StringID("sprite::character::walk_01"));
    }

    SECTION("HUD element with fade effect") {
        ImageWidget hudElement(glm::vec2(10.0f, 10.0f), glm::vec2(200.0f, 50.0f), StringID("texture::ui::health_bar"));

        // Simulate fade in effect
        std::vector<float> fadeInSteps = {0.0f, 0.2f, 0.4f, 0.6f, 0.8f, 1.0f};

        for (float alpha : fadeInSteps) {
            hudElement.setAlpha(alpha);
            REQUIRE(hudElement.getAlpha() == alpha);

            // Texture and color should remain unchanged
            REQUIRE(hudElement.getTextureName() == StringID("texture::ui::health_bar"));
            REQUIRE(hudElement.getColor() == glm::vec3(1.0f, 1.0f, 1.0f));
        }

        // Simulate fade out effect
        std::vector<float> fadeOutSteps = {1.0f, 0.8f, 0.6f, 0.4f, 0.2f, 0.0f};

        for (float alpha : fadeOutSteps) {
            hudElement.setAlpha(alpha);
            REQUIRE(hudElement.getAlpha() == alpha);
        }
    }

    SECTION("Interactive image with click handling") {
        ImageWidget interactiveImage(glm::vec2(150.0f, 200.0f), glm::vec2(100.0f, 100.0f), StringID("texture::ui::interactive"));

        bool wasClicked = false;
        bool wasHovered = false;
        int clickCount = 0;

        interactiveImage.setCallback("onClick", [&wasClicked, &clickCount]() {
            wasClicked = true;
            clickCount++;
        });

        interactiveImage.setCallback("onHover", [&wasHovered]() {
            wasHovered = true;
        });

        MockKeyboard mockKeyboard;
        MockMouse mockMouse;
        GameContext context = createTestGameContext(mockKeyboard, mockMouse);

        // Mouse enters image area
        mockMouse.setPosition(200.0f, 250.0f); // Center of image
        interactiveImage.update(0.016f, context);

        REQUIRE(interactiveImage.isHovered());
        REQUIRE(wasHovered);

        // Mouse clicks
        mockMouse.setButtonPressed(MOUSE_BUTTON_LEFT, true);
        interactiveImage.update(0.016f, context);

        REQUIRE(interactiveImage.isPressed());
        REQUIRE(wasClicked);
        REQUIRE(clickCount == 1);

        // Mouse releases and clicks again
        mockMouse.setButtonPressed(MOUSE_BUTTON_LEFT, false);
        interactiveImage.update(0.016f, context);

        mockMouse.setButtonPressed(MOUSE_BUTTON_LEFT, true);
        interactiveImage.update(0.016f, context);

        REQUIRE(clickCount == 2);
    }

    SECTION("Responsive image scaling") {
        ImageWidget responsiveImage(glm::vec2(0.0f, 0.0f), glm::vec2(100.0f, 100.0f), StringID("texture::ui::logo"));

        // Simulate different screen resolutions
        struct Resolution {
            glm::vec2 screenSize;
            glm::vec2 expectedImageSize;
            glm::vec2 expectedPosition;
        };

        std::vector<Resolution> resolutions = {
            {{1920.0f, 1080.0f}, {100.0f, 100.0f}, {960.0f, 540.0f}},  // 1080p - center
            {{1280.0f, 720.0f}, {67.0f, 67.0f}, {640.0f, 360.0f}},     // 720p - scaled down
            {{2560.0f, 1440.0f}, {133.0f, 133.0f}, {1280.0f, 720.0f}}, // 1440p - scaled up
            {{3840.0f, 2160.0f}, {200.0f, 200.0f}, {1920.0f, 1080.0f}} // 4K - scaled up
        };

        for (const auto& res : resolutions) {
            responsiveImage.setSize(res.expectedImageSize);
            responsiveImage.setPosition(res.expectedPosition);

            REQUIRE(responsiveImage.getSize() == res.expectedImageSize);
            REQUIRE(responsiveImage.getPosition() == res.expectedPosition);
            REQUIRE(responsiveImage.getTextureName() == StringID("texture::ui::logo"));
        }
    }

    SECTION("Image widget with color tinting") {
        ImageWidget tintedImage(glm::vec2(50.0f, 50.0f), glm::vec2(80.0f, 80.0f), StringID("texture::ui::icon"));

        // Test different color tints
        struct ColorTint {
            glm::vec3 color;
            std::string description;
        };

        std::vector<ColorTint> tints = {
            {{1.0f, 0.0f, 0.0f}, "red tint"},
            {{0.0f, 1.0f, 0.0f}, "green tint"},
            {{0.0f, 0.0f, 1.0f}, "blue tint"},
            {{1.0f, 1.0f, 0.0f}, "yellow tint"},
            {{1.0f, 0.0f, 1.0f}, "magenta tint"},
            {{0.0f, 1.0f, 1.0f}, "cyan tint"},
            {{0.5f, 0.5f, 0.5f}, "gray tint"},
            {{1.5f, 1.2f, 0.8f}, "warm tint (HDR)"}
        };

        for (const auto& tint : tints) {
            tintedImage.setColor(tint.color);

            const auto& color = tintedImage.getColor();
            REQUIRE(color.r == tint.color.r);
            REQUIRE(color.g == tint.color.g);
            REQUIRE(color.b == tint.color.b);

            // Texture should remain unchanged
            REQUIRE(tintedImage.getTextureName() == StringID("texture::ui::icon"));
        }
    }
}
