#include <catch2/catch_test_macros.hpp>

// Third-party libraries
#include <glm/glm.hpp>

// Local includes
#include "gui/widgets/mixins/colorable.hpp"
#include "../../../test_utils.hpp"

using namespace IronFrost;

TEST_CASE("Colorable mixin basic functionality", "[gui][widgets][mixins][colorable][basic]") {
    SECTION("Default constructor sets white color and full alpha") {
        Colorable colorable;

        // Use shared utility for default color verification (CLEANUP)
        VERIFY_DEFAULT_COLORABLE_PROPERTIES(colorable);
    }
    
    SECTION("Set and get RGB color") {
        Colorable colorable;
        
        glm::vec3 testColor(0.5f, 0.7f, 0.9f);
        colorable.setColor(testColor);
        
        const auto& retrievedColor = colorable.getColor();
        REQUIRE(retrievedColor.r == 0.5f);
        REQUIRE(retrievedColor.g == 0.7f);
        REQUIRE(retrievedColor.b == 0.9f);
    }
    
    SECTION("Set and get alpha value") {
        Colorable colorable;
        
        colorable.setAlpha(0.75f);
        REQUIRE(colorable.getAlpha() == 0.75f);
        
        colorable.setAlpha(0.0f);
        REQUIRE(colorable.getAlpha() == 0.0f);
        
        colorable.setAlpha(1.0f);
        REQUIRE(colorable.getAlpha() == 1.0f);
    }
    
    SECTION("Color and alpha are independent") {
        Colorable colorable;
        
        glm::vec3 redColor(1.0f, 0.0f, 0.0f);
        colorable.setColor(redColor);
        colorable.setAlpha(0.5f);
        
        const auto& color = colorable.getColor();
        REQUIRE(color.r == 1.0f);
        REQUIRE(color.g == 0.0f);
        REQUIRE(color.b == 0.0f);
        REQUIRE(colorable.getAlpha() == 0.5f);
        
        // Change color, alpha should remain
        glm::vec3 blueColor(0.0f, 0.0f, 1.0f);
        colorable.setColor(blueColor);
        
        const auto& newColor = colorable.getColor();
        REQUIRE(newColor.r == 0.0f);
        REQUIRE(newColor.g == 0.0f);
        REQUIRE(newColor.b == 1.0f);
        REQUIRE(colorable.getAlpha() == 0.5f); // Alpha unchanged
    }
}

TEST_CASE("Colorable mixin color values", "[gui][widgets][mixins][colorable][values]") {
    SECTION("Primary colors") {
        Colorable colorable;
        
        // Red
        colorable.setColor(glm::vec3(1.0f, 0.0f, 0.0f));
        auto color = colorable.getColor();
        REQUIRE(color.r == 1.0f);
        REQUIRE(color.g == 0.0f);
        REQUIRE(color.b == 0.0f);
        
        // Green
        colorable.setColor(glm::vec3(0.0f, 1.0f, 0.0f));
        color = colorable.getColor();
        REQUIRE(color.r == 0.0f);
        REQUIRE(color.g == 1.0f);
        REQUIRE(color.b == 0.0f);
        
        // Blue
        colorable.setColor(glm::vec3(0.0f, 0.0f, 1.0f));
        color = colorable.getColor();
        REQUIRE(color.r == 0.0f);
        REQUIRE(color.g == 0.0f);
        REQUIRE(color.b == 1.0f);
    }
    
    SECTION("Secondary colors") {
        Colorable colorable;
        
        // Cyan
        colorable.setColor(glm::vec3(0.0f, 1.0f, 1.0f));
        auto color = colorable.getColor();
        REQUIRE(color.r == 0.0f);
        REQUIRE(color.g == 1.0f);
        REQUIRE(color.b == 1.0f);
        
        // Magenta
        colorable.setColor(glm::vec3(1.0f, 0.0f, 1.0f));
        color = colorable.getColor();
        REQUIRE(color.r == 1.0f);
        REQUIRE(color.g == 0.0f);
        REQUIRE(color.b == 1.0f);
        
        // Yellow
        colorable.setColor(glm::vec3(1.0f, 1.0f, 0.0f));
        color = colorable.getColor();
        REQUIRE(color.r == 1.0f);
        REQUIRE(color.g == 1.0f);
        REQUIRE(color.b == 0.0f);
    }
    
    SECTION("Grayscale colors") {
        Colorable colorable;
        
        // Black
        colorable.setColor(glm::vec3(0.0f, 0.0f, 0.0f));
        auto color = colorable.getColor();
        REQUIRE(color.r == 0.0f);
        REQUIRE(color.g == 0.0f);
        REQUIRE(color.b == 0.0f);
        
        // Gray
        colorable.setColor(glm::vec3(0.5f, 0.5f, 0.5f));
        color = colorable.getColor();
        REQUIRE(color.r == 0.5f);
        REQUIRE(color.g == 0.5f);
        REQUIRE(color.b == 0.5f);
        
        // White (default)
        colorable.setColor(glm::vec3(1.0f, 1.0f, 1.0f));
        color = colorable.getColor();
        REQUIRE(color.r == 1.0f);
        REQUIRE(color.g == 1.0f);
        REQUIRE(color.b == 1.0f);
    }
    
    SECTION("Intermediate color values") {
        Colorable colorable;
        
        // Test various intermediate values
        std::vector<glm::vec3> testColors = {
            {0.25f, 0.75f, 0.125f},
            {0.333f, 0.666f, 0.999f},
            {0.1f, 0.2f, 0.3f},
            {0.9f, 0.8f, 0.7f},
            {0.0001f, 0.9999f, 0.5f}
        };
        
        for (const auto& testColor : testColors) {
            colorable.setColor(testColor);
            const auto& retrievedColor = colorable.getColor();
            
            REQUIRE(retrievedColor.r == testColor.r);
            REQUIRE(retrievedColor.g == testColor.g);
            REQUIRE(retrievedColor.b == testColor.b);
        }
    }
}

TEST_CASE("Colorable mixin alpha values", "[gui][widgets][mixins][colorable][alpha]") {
    SECTION("Alpha boundary values") {
        Colorable colorable;
        
        // Fully transparent
        colorable.setAlpha(0.0f);
        REQUIRE(colorable.getAlpha() == 0.0f);
        
        // Fully opaque
        colorable.setAlpha(1.0f);
        REQUIRE(colorable.getAlpha() == 1.0f);
        
        // Half transparent
        colorable.setAlpha(0.5f);
        REQUIRE(colorable.getAlpha() == 0.5f);
    }
    
    SECTION("Various alpha values") {
        Colorable colorable;
        
        std::vector<float> alphaValues = {
            0.0f, 0.1f, 0.25f, 0.33f, 0.5f, 0.66f, 0.75f, 0.9f, 1.0f,
            0.001f, 0.999f, 0.123f, 0.456f, 0.789f
        };
        
        for (float alpha : alphaValues) {
            colorable.setAlpha(alpha);
            REQUIRE(colorable.getAlpha() == alpha);
        }
    }
    
    SECTION("Alpha doesn't affect color") {
        Colorable colorable;
        
        glm::vec3 originalColor(0.3f, 0.6f, 0.9f);
        colorable.setColor(originalColor);
        
        // Change alpha multiple times
        colorable.setAlpha(0.0f);
        auto color1 = colorable.getColor();
        REQUIRE(color1.r == originalColor.r);
        REQUIRE(color1.g == originalColor.g);
        REQUIRE(color1.b == originalColor.b);
        
        colorable.setAlpha(0.5f);
        auto color2 = colorable.getColor();
        REQUIRE(color2.r == originalColor.r);
        REQUIRE(color2.g == originalColor.g);
        REQUIRE(color2.b == originalColor.b);
        
        colorable.setAlpha(1.0f);
        auto color3 = colorable.getColor();
        REQUIRE(color3.r == originalColor.r);
        REQUIRE(color3.g == originalColor.g);
        REQUIRE(color3.b == originalColor.b);
    }
}

TEST_CASE("Colorable mixin edge cases", "[gui][widgets][mixins][colorable][edge_cases]") {
    SECTION("Out of range color values") {
        Colorable colorable;
        
        // Values above 1.0 (should be allowed, might be used for HDR)
        glm::vec3 brightColor(2.0f, 1.5f, 3.0f);
        colorable.setColor(brightColor);
        
        const auto& color = colorable.getColor();
        REQUIRE(color.r == 2.0f);
        REQUIRE(color.g == 1.5f);
        REQUIRE(color.b == 3.0f);
        
        // Negative values (should be allowed for special effects)
        glm::vec3 negativeColor(-0.5f, -1.0f, -0.25f);
        colorable.setColor(negativeColor);
        
        const auto& negColor = colorable.getColor();
        REQUIRE(negColor.r == -0.5f);
        REQUIRE(negColor.g == -1.0f);
        REQUIRE(negColor.b == -0.25f);
    }
    
    SECTION("Out of range alpha values") {
        Colorable colorable;
        
        // Alpha above 1.0
        colorable.setAlpha(2.0f);
        REQUIRE(colorable.getAlpha() == 2.0f);
        
        // Negative alpha
        colorable.setAlpha(-0.5f);
        REQUIRE(colorable.getAlpha() == -0.5f);
    }
    
    SECTION("Very small and very large values") {
        Colorable colorable;
        
        // Very small values
        glm::vec3 tinyColor(1e-10f, 1e-15f, 1e-20f);
        colorable.setColor(tinyColor);
        colorable.setAlpha(1e-8f);
        
        const auto& color = colorable.getColor();
        REQUIRE(color.r == 1e-10f);
        REQUIRE(color.g == 1e-15f);
        REQUIRE(color.b == 1e-20f);
        REQUIRE(colorable.getAlpha() == 1e-8f);
        
        // Very large values
        glm::vec3 hugeColor(1e10f, 1e15f, 1e20f);
        colorable.setColor(hugeColor);
        colorable.setAlpha(1e8f);
        
        const auto& bigColor = colorable.getColor();
        REQUIRE(bigColor.r == 1e10f);
        REQUIRE(bigColor.g == 1e15f);
        REQUIRE(bigColor.b == 1e20f);
        REQUIRE(colorable.getAlpha() == 1e8f);
    }
}

TEST_CASE("Colorable mixin real-world scenarios", "[gui][widgets][mixins][colorable][real_world]") {
    SECTION("UI element colors") {
        Colorable button;
        
        // Button normal state (light blue)
        button.setColor(glm::vec3(0.7f, 0.8f, 1.0f));
        button.setAlpha(1.0f);
        
        auto normalColor = button.getColor();
        REQUIRE(normalColor.r == 0.7f);
        REQUIRE(normalColor.g == 0.8f);
        REQUIRE(normalColor.b == 1.0f);
        REQUIRE(button.getAlpha() == 1.0f);
        
        // Button hover state (brighter)
        button.setColor(glm::vec3(0.8f, 0.9f, 1.0f));
        
        auto hoverColor = button.getColor();
        REQUIRE(hoverColor.r == 0.8f);
        REQUIRE(hoverColor.g == 0.9f);
        REQUIRE(hoverColor.b == 1.0f);
        
        // Button disabled state (grayed out)
        button.setColor(glm::vec3(0.5f, 0.5f, 0.5f));
        button.setAlpha(0.5f);
        
        auto disabledColor = button.getColor();
        REQUIRE(disabledColor.r == 0.5f);
        REQUIRE(disabledColor.g == 0.5f);
        REQUIRE(disabledColor.b == 0.5f);
        REQUIRE(button.getAlpha() == 0.5f);
    }
    
    SECTION("Fade animation simulation") {
        Colorable fadeElement;
        
        glm::vec3 baseColor(1.0f, 0.5f, 0.2f);
        fadeElement.setColor(baseColor);
        
        // Simulate fade out animation
        std::vector<float> fadeSteps = {1.0f, 0.8f, 0.6f, 0.4f, 0.2f, 0.0f};
        
        for (float alpha : fadeSteps) {
            fadeElement.setAlpha(alpha);
            
            // Color should remain constant
            const auto& color = fadeElement.getColor();
            REQUIRE(color.r == baseColor.r);
            REQUIRE(color.g == baseColor.g);
            REQUIRE(color.b == baseColor.b);
            
            // Alpha should change
            REQUIRE(fadeElement.getAlpha() == alpha);
        }
    }
    
    SECTION("Color theme switching") {
        Colorable themeElement;
        
        // Light theme
        glm::vec3 lightTheme(0.9f, 0.9f, 0.9f);
        themeElement.setColor(lightTheme);
        themeElement.setAlpha(1.0f);
        
        auto lightColor = themeElement.getColor();
        REQUIRE(lightColor.r == 0.9f);
        REQUIRE(lightColor.g == 0.9f);
        REQUIRE(lightColor.b == 0.9f);
        
        // Dark theme
        glm::vec3 darkTheme(0.2f, 0.2f, 0.2f);
        themeElement.setColor(darkTheme);
        
        auto darkColor = themeElement.getColor();
        REQUIRE(darkColor.r == 0.2f);
        REQUIRE(darkColor.g == 0.2f);
        REQUIRE(darkColor.b == 0.2f);
        REQUIRE(themeElement.getAlpha() == 1.0f); // Alpha preserved
        
        // High contrast theme
        glm::vec3 contrastTheme(1.0f, 1.0f, 0.0f);
        themeElement.setColor(contrastTheme);
        
        auto contrastColor = themeElement.getColor();
        REQUIRE(contrastColor.r == 1.0f);
        REQUIRE(contrastColor.g == 1.0f);
        REQUIRE(contrastColor.b == 0.0f);
    }
}
