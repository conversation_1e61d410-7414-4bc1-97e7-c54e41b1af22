#include <catch2/catch_test_macros.hpp>

// C++ standard library
#include <cmath>

// Third-party libraries
#include <glm/glm.hpp>

// Local includes
#include "gui/widgets/mixins/transformable.hpp"
#include "../../../test_utils.hpp"

using namespace IronFrost;

TEST_CASE("Transformable mixin basic functionality", "[gui][widgets][mixins][transformable][basic]") {
    SECTION("Default constructor sets default values") {
        Transformable transformable;

        REQUIRE(transformable.getPosition() == glm::vec2(0.0f, 0.0f));
        REQUIRE(transformable.getSize() == glm::vec2(10.0f, 10.0f)); // Default size from implementation
        REQUIRE(transformable.getRotation() == 0.0f);
        REQUIRE(transformable.isDirty() == true); // Should be dirty initially
    }

    SECTION("Constructor with position, size, and rotation") {
        glm::vec2 position(100.0f, 200.0f);
        glm::vec2 size(50.0f, 75.0f);
        float rotation = 45.0f;

        Transformable transformable(position, size, rotation);

        REQUIRE(transformable.getPosition() == position);
        REQUIRE(transformable.getSize() == size);
        REQUIRE(transformable.getRotation() == rotation);
    }
    
    SECTION("Set and get position") {
        Transformable transformable;
        
        glm::vec2 newPosition(150.0f, 250.0f);
        transformable.setPosition(newPosition);
        
        REQUIRE(transformable.getPosition() == newPosition);
    }
    
    SECTION("Set and get size") {
        Transformable transformable;
        
        glm::vec2 newSize(80.0f, 120.0f);
        transformable.setSize(newSize);
        
        REQUIRE(transformable.getSize() == newSize);
    }
    
    SECTION("Set and get rotation") {
        Transformable transformable;
        
        float rotation = 45.0f;
        transformable.setRotation(rotation);
        
        REQUIRE(transformable.getRotation() == rotation);
    }
    
    SECTION("Dirty flag management") {
        Transformable transformable;

        // Initially dirty
        REQUIRE(transformable.isDirty() == true);

        // Mark clean
        transformable.markClean();
        REQUIRE(transformable.isDirty() == false);

        // Setting position marks dirty
        transformable.setPosition(glm::vec2(10.0f, 20.0f));
        REQUIRE(transformable.isDirty() == true);

        // Mark clean again
        transformable.markClean();
        REQUIRE(transformable.isDirty() == false);

        // Setting size marks dirty
        transformable.setSize(glm::vec2(30.0f, 40.0f));
        REQUIRE(transformable.isDirty() == true);

        // Mark clean again
        transformable.markClean();
        REQUIRE(transformable.isDirty() == false);

        // Setting rotation marks dirty
        transformable.setRotation(90.0f);
        REQUIRE(transformable.isDirty() == true);
    }
}

TEST_CASE("Transformable mixin position operations", "[gui][widgets][mixins][transformable][position]") {
    SECTION("Set position updates correctly") {
        Transformable transformable;

        glm::vec2 newPosition(150.0f, 250.0f);
        transformable.setPosition(newPosition);

        REQUIRE(transformable.getPosition() == newPosition);
        REQUIRE(transformable.isDirty() == true);
    }

    SECTION("Multiple position changes") {
        Transformable transformable;

        std::vector<glm::vec2> positions = {
            {10.0f, 20.0f},
            {50.0f, 100.0f},
            {-25.0f, 75.0f},
            {0.0f, 0.0f}
        };

        for (const auto& pos : positions) {
            transformable.setPosition(pos);
            REQUIRE(transformable.getPosition() == pos);
            REQUIRE(transformable.isDirty() == true);
            transformable.markClean();
        }
    }
    
    SECTION("Negative positions") {
        Transformable transformable;
        
        glm::vec2 negativePosition(-50.0f, -100.0f);
        transformable.setPosition(negativePosition);
        
        REQUIRE(transformable.getPosition() == negativePosition);
    }
    
    SECTION("Large position values") {
        Transformable transformable;
        
        glm::vec2 largePosition(1e6f, 1e6f);
        transformable.setPosition(largePosition);
        
        REQUIRE(transformable.getPosition() == largePosition);
    }
}

TEST_CASE("Transformable mixin size operations", "[gui][widgets][mixins][transformable][size]") {
    SECTION("Set size updates correctly") {
        Transformable transformable;

        glm::vec2 newSize(200.0f, 150.0f);
        transformable.setSize(newSize);

        REQUIRE(transformable.getSize() == newSize);
        REQUIRE(transformable.isDirty() == true);
    }
    
    SECTION("Zero size") {
        Transformable transformable;
        
        glm::vec2 zeroSize(0.0f, 0.0f);
        transformable.setSize(zeroSize);
        
        REQUIRE(transformable.getSize() == zeroSize);
    }
    
    SECTION("Negative size values") {
        Transformable transformable;
        
        glm::vec2 negativeSize(-10.0f, -20.0f);
        transformable.setSize(negativeSize);
        
        REQUIRE(transformable.getSize() == negativeSize);
    }
    
    SECTION("Very small size values") {
        Transformable transformable;
        
        glm::vec2 tinySize(0.001f, 0.0001f);
        transformable.setSize(tinySize);
        
        REQUIRE(isVec2Equal(transformable.getSize(), tinySize));
    }
}

TEST_CASE("Transformable mixin rotation operations", "[gui][widgets][mixins][transformable][rotation]") {
    SECTION("Set rotation updates correctly") {
        Transformable transformable;

        float newRotation = 75.0f;
        transformable.setRotation(newRotation);

        REQUIRE(transformable.getRotation() == newRotation);
        REQUIRE(transformable.isDirty() == true);
    }

    SECTION("Multiple rotation changes") {
        Transformable transformable;

        std::vector<float> rotations = {0.0f, 90.0f, 45.0f, -30.0f, 180.0f};

        for (float rotation : rotations) {
            transformable.setRotation(rotation);
            REQUIRE(transformable.getRotation() == rotation);
            REQUIRE(transformable.isDirty() == true);
            transformable.markClean();
        }
    }
    
    SECTION("Negative rotation") {
        Transformable transformable;

        transformable.setRotation(-45.0f);
        REQUIRE(transformable.getRotation() == -45.0f);

        transformable.setRotation(-75.0f);
        REQUIRE(transformable.getRotation() == -75.0f);
    }
    
    SECTION("Large rotation values") {
        Transformable transformable;
        
        transformable.setRotation(720.0f); // Two full rotations
        REQUIRE(transformable.getRotation() == 720.0f);
        
        transformable.setRotation(-360.0f); // One full rotation backwards
        REQUIRE(transformable.getRotation() == -360.0f);
    }
    
    SECTION("Fractional rotation") {
        Transformable transformable;
        
        float preciseAngle = 33.333333f;
        transformable.setRotation(preciseAngle);
        
        REQUIRE(isFloatEqual(transformable.getRotation(), preciseAngle));
    }
}



TEST_CASE("Transformable mixin combined operations", "[gui][widgets][mixins][transformable][combined]") {
    SECTION("All transforms together") {
        Transformable transformable;

        glm::vec2 position(100.0f, 200.0f);
        glm::vec2 size(50.0f, 75.0f);
        float rotation = 45.0f;

        transformable.setPosition(position);
        transformable.setSize(size);
        transformable.setRotation(rotation);

        REQUIRE(transformable.getPosition() == position);
        REQUIRE(transformable.getSize() == size);
        REQUIRE(transformable.getRotation() == rotation);
        REQUIRE(transformable.isDirty() == true);
    }
    
    SECTION("Transforms are independent") {
        Transformable transformable;

        // Set initial values
        transformable.setPosition(glm::vec2(10.0f, 20.0f));
        transformable.setSize(glm::vec2(30.0f, 40.0f));
        transformable.setRotation(90.0f);

        // Change position, others should remain
        transformable.setPosition(glm::vec2(100.0f, 200.0f));
        REQUIRE(transformable.getPosition() == glm::vec2(100.0f, 200.0f));
        REQUIRE(transformable.getSize() == glm::vec2(30.0f, 40.0f));
        REQUIRE(transformable.getRotation() == 90.0f);

        // Change rotation, others should remain
        transformable.setRotation(180.0f);
        REQUIRE(transformable.getPosition() == glm::vec2(100.0f, 200.0f));
        REQUIRE(transformable.getSize() == glm::vec2(30.0f, 40.0f));
        REQUIRE(transformable.getRotation() == 180.0f);
    }
    
    SECTION("Sequential operations") {
        Transformable transformable;

        // Start with default values
        transformable.setPosition(glm::vec2(0.0f, 0.0f));
        transformable.setSize(glm::vec2(10.0f, 10.0f));
        transformable.setRotation(0.0f);
        transformable.markClean();

        // Apply a series of transformations
        transformable.setPosition(glm::vec2(50.0f, 100.0f));
        REQUIRE(transformable.isDirty() == true);
        transformable.markClean();

        transformable.setSize(glm::vec2(20.0f, 15.0f));
        REQUIRE(transformable.isDirty() == true);
        transformable.markClean();

        transformable.setRotation(45.0f);
        REQUIRE(transformable.isDirty() == true);

        REQUIRE(transformable.getPosition() == glm::vec2(50.0f, 100.0f));
        REQUIRE(transformable.getSize() == glm::vec2(20.0f, 15.0f));
        REQUIRE(transformable.getRotation() == 45.0f);
    }
}

TEST_CASE("Transformable mixin real-world scenarios", "[gui][widgets][mixins][transformable][real_world]") {
    SECTION("UI button positioning and sizing") {
        Transformable button;

        // Initial button setup
        button.setPosition(glm::vec2(100.0f, 50.0f));
        button.setSize(glm::vec2(120.0f, 40.0f));

        REQUIRE(button.getPosition() == glm::vec2(100.0f, 50.0f));
        REQUIRE(button.getSize() == glm::vec2(120.0f, 40.0f));
        REQUIRE(button.isDirty() == true);

        // Resize for different screen resolution
        button.setSize(glm::vec2(180.0f, 60.0f));
        REQUIRE(button.getSize() == glm::vec2(180.0f, 60.0f));

        // Move to new position
        button.setPosition(glm::vec2(150.0f, 75.0f));
        REQUIRE(button.getPosition() == glm::vec2(150.0f, 75.0f));
    }
    
    SECTION("Animated widget transformation") {
        Transformable animatedWidget;

        // Animation keyframes
        std::vector<glm::vec2> positions = {
            {0.0f, 0.0f},
            {50.0f, 25.0f},
            {100.0f, 50.0f},
            {150.0f, 75.0f},
            {200.0f, 100.0f}
        };

        std::vector<float> rotations = {0.0f, 45.0f, 90.0f, 135.0f, 180.0f};
        std::vector<glm::vec2> sizes = {
            {10.0f, 10.0f},
            {12.0f, 12.0f},
            {15.0f, 15.0f},
            {12.0f, 12.0f},
            {10.0f, 10.0f}
        };

        // Simulate animation frames
        for (size_t i = 0; i < positions.size(); ++i) {
            animatedWidget.setPosition(positions[i]);
            animatedWidget.setRotation(rotations[i]);
            animatedWidget.setSize(sizes[i]);

            REQUIRE(animatedWidget.getPosition() == positions[i]);
            REQUIRE(animatedWidget.getRotation() == rotations[i]);
            REQUIRE(animatedWidget.getSize() == sizes[i]);
            REQUIRE(animatedWidget.isDirty() == true);
            animatedWidget.markClean();
        }
    }
    
    SECTION("Responsive layout sizing") {
        Transformable responsiveElement;

        // Base design at 1920x1080
        glm::vec2 basePosition(960.0f, 540.0f);
        glm::vec2 baseSize(200.0f, 100.0f);

        responsiveElement.setPosition(basePosition);
        responsiveElement.setSize(baseSize);

        // Resize for different screen sizes
        struct ScreenSize {
            glm::vec2 resolution;
            glm::vec2 newSize;
        };

        std::vector<ScreenSize> screenSizes = {
            {{1280.0f, 720.0f}, {133.0f, 67.0f}},    // 720p (scaled down)
            {{2560.0f, 1440.0f}, {267.0f, 133.0f}},  // 1440p (scaled up)
            {{3840.0f, 2160.0f}, {400.0f, 200.0f}}   // 4K (scaled up)
        };

        for (const auto& screen : screenSizes) {
            // Reset to base
            responsiveElement.setPosition(basePosition);
            responsiveElement.setSize(screen.newSize);

            // Position should remain the same
            REQUIRE(responsiveElement.getPosition() == basePosition);
            // Size should be updated
            REQUIRE(responsiveElement.getSize() == screen.newSize);
            REQUIRE(responsiveElement.isDirty() == true);
            responsiveElement.markClean();
        }
    }
    
    SECTION("Widget rotation for orientation changes") {
        Transformable orientationWidget;
        
        orientationWidget.setPosition(glm::vec2(100.0f, 100.0f));
        orientationWidget.setSize(glm::vec2(200.0f, 50.0f));
        
        // Portrait orientation
        orientationWidget.setRotation(0.0f);
        REQUIRE(orientationWidget.getRotation() == 0.0f);
        
        // Landscape left
        orientationWidget.setRotation(90.0f);
        REQUIRE(orientationWidget.getRotation() == 90.0f);
        
        // Upside down
        orientationWidget.setRotation(180.0f);
        REQUIRE(orientationWidget.getRotation() == 180.0f);
        
        // Landscape right
        orientationWidget.setRotation(270.0f);
        REQUIRE(orientationWidget.getRotation() == 270.0f);
        
        // Back to portrait
        orientationWidget.setRotation(360.0f);
        REQUIRE(orientationWidget.getRotation() == 360.0f);
    }
}
