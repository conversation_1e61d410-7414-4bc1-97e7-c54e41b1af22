#include <catch2/catch_test_macros.hpp>

// C++ standard library
#include <string>
#include <functional>

// Local includes
#include "gui/widgets/mixins/callbackable.hpp"

using namespace IronFrost;

TEST_CASE("Callbackable mixin basic functionality", "[gui][widgets][mixins][callbackable][basic]") {
    SECTION("Default constructor creates empty callback map") {
        Callbackable callbackable;
        
        // Should not crash when triggering non-existent callback
        callbackable.triggerCallback("nonexistent");
    }
    
    SECTION("Set and trigger single callback") {
        Callbackable callbackable;
        bool callbackTriggered = false;
        
        callbackable.setCallback("test", [&callbackTriggered]() {
            callbackTriggered = true;
        });
        
        REQUIRE_FALSE(callbackTriggered);
        
        callbackable.triggerCallback("test");
        REQUIRE(callbackTriggered);
    }
    
    SECTION("Set and trigger multiple callbacks") {
        Callbackable callbackable;
        int counter1 = 0;
        int counter2 = 0;
        std::string result;
        
        callbackable.setCallback("increment1", [&counter1]() {
            counter1++;
        });
        
        callbackable.setCallback("increment2", [&counter2]() {
            counter2++;
        });
        
        callbackable.setCallback("setText", [&result]() {
            result = "callback_triggered";
        });
        
        // Trigger callbacks multiple times
        callbackable.triggerCallback("increment1");
        callbackable.triggerCallback("increment1");
        callbackable.triggerCallback("increment2");
        callbackable.triggerCallback("setText");
        
        REQUIRE(counter1 == 2);
        REQUIRE(counter2 == 1);
        REQUIRE(result == "callback_triggered");
    }
    
    SECTION("Overwrite existing callback") {
        Callbackable callbackable;
        int counter = 0;
        
        // Set initial callback
        callbackable.setCallback("test", [&counter]() {
            counter += 1;
        });
        
        callbackable.triggerCallback("test");
        REQUIRE(counter == 1);
        
        // Overwrite with new callback
        callbackable.setCallback("test", [&counter]() {
            counter += 10;
        });
        
        callbackable.triggerCallback("test");
        REQUIRE(counter == 11); // 1 + 10
    }
    
    SECTION("Trigger non-existent callback does nothing") {
        Callbackable callbackable;
        bool callbackTriggered = false;
        
        callbackable.setCallback("existing", [&callbackTriggered]() {
            callbackTriggered = true;
        });
        
        // Trigger non-existent callback
        callbackable.triggerCallback("nonexistent");
        REQUIRE_FALSE(callbackTriggered);
        
        // Trigger existing callback to verify it still works
        callbackable.triggerCallback("existing");
        REQUIRE(callbackTriggered);
    }
}

TEST_CASE("Callbackable mixin callback types", "[gui][widgets][mixins][callbackable][types]") {
    SECTION("Lambda callbacks") {
        Callbackable callbackable;
        int value = 0;
        
        callbackable.setCallback("lambda", [&value]() {
            value = 42;
        });
        
        callbackable.triggerCallback("lambda");
        REQUIRE(value == 42);
    }
    
    SECTION("Function pointer callbacks") {
        Callbackable callbackable;
        static bool functionCalled = false;
        functionCalled = false;
        
        auto testFunction = []() {
            functionCalled = true;
        };
        
        callbackable.setCallback("function", testFunction);
        callbackable.triggerCallback("function");
        REQUIRE(functionCalled);
    }
    
    SECTION("std::function callbacks") {
        Callbackable callbackable;
        std::string result;
        
        std::function<void()> func = [&result]() {
            result = "std_function_called";
        };
        
        callbackable.setCallback("std_function", func);
        callbackable.triggerCallback("std_function");
        REQUIRE(result == "std_function_called");
    }
    
    SECTION("Capturing complex state") {
        Callbackable callbackable;
        
        struct TestState {
            int count = 0;
            std::string message;
            bool flag = false;
        };
        
        TestState state;
        
        callbackable.setCallback("complex", [&state]() {
            state.count++;
            state.message = "updated";
            state.flag = true;
        });
        
        callbackable.triggerCallback("complex");
        
        REQUIRE(state.count == 1);
        REQUIRE(state.message == "updated");
        REQUIRE(state.flag == true);
    }
}

TEST_CASE("Callbackable mixin edge cases", "[gui][widgets][mixins][callbackable][edge_cases]") {
    SECTION("Empty callback name") {
        Callbackable callbackable;
        bool triggered = false;
        
        callbackable.setCallback("", [&triggered]() {
            triggered = true;
        });
        
        callbackable.triggerCallback("");
        REQUIRE(triggered);
    }
    
    SECTION("Very long callback name") {
        Callbackable callbackable;
        bool triggered = false;
        
        std::string longName(1000, 'a');
        
        callbackable.setCallback(longName, [&triggered]() {
            triggered = true;
        });
        
        callbackable.triggerCallback(longName);
        REQUIRE(triggered);
    }
    
    SECTION("Special characters in callback name") {
        Callbackable callbackable;
        int counter = 0;
        
        callbackable.setCallback("test@#$%^&*()", [&counter]() { counter++; });
        callbackable.setCallback("test with spaces", [&counter]() { counter++; });
        callbackable.setCallback("test-with-dashes", [&counter]() { counter++; });
        callbackable.setCallback("test_with_underscores", [&counter]() { counter++; });
        callbackable.setCallback("test.with.dots", [&counter]() { counter++; });
        
        callbackable.triggerCallback("test@#$%^&*()");
        callbackable.triggerCallback("test with spaces");
        callbackable.triggerCallback("test-with-dashes");
        callbackable.triggerCallback("test_with_underscores");
        callbackable.triggerCallback("test.with.dots");
        
        REQUIRE(counter == 5);
    }
    
    SECTION("Callback that throws exception") {
        Callbackable callbackable;
        
        callbackable.setCallback("throwing", []() {
            throw std::runtime_error("Test exception");
        });
        
        // Should throw when callback is triggered
        REQUIRE_THROWS_AS(callbackable.triggerCallback("throwing"), std::runtime_error);
    }
    
    SECTION("Callback that modifies the callbackable object") {
        Callbackable callbackable;
        bool secondCallbackTriggered = false;
        
        // Callback that sets another callback
        callbackable.setCallback("modifier", [&callbackable, &secondCallbackTriggered]() {
            callbackable.setCallback("new_callback", [&secondCallbackTriggered]() {
                secondCallbackTriggered = true;
            });
        });
        
        callbackable.triggerCallback("modifier");
        callbackable.triggerCallback("new_callback");
        
        REQUIRE(secondCallbackTriggered);
    }
}

TEST_CASE("Callbackable mixin performance", "[gui][widgets][mixins][callbackable][performance]") {
    SECTION("Many callbacks") {
        Callbackable callbackable;
        const int numCallbacks = 1000;
        int totalTriggered = 0;
        
        // Set many callbacks
        for (int i = 0; i < numCallbacks; ++i) {
            std::string name = "callback_" + std::to_string(i);
            callbackable.setCallback(name, [&totalTriggered]() {
                totalTriggered++;
            });
        }
        
        // Trigger all callbacks
        for (int i = 0; i < numCallbacks; ++i) {
            std::string name = "callback_" + std::to_string(i);
            callbackable.triggerCallback(name);
        }
        
        REQUIRE(totalTriggered == numCallbacks);
    }
    
    SECTION("Repeated callback triggers") {
        Callbackable callbackable;
        int counter = 0;
        
        callbackable.setCallback("repeated", [&counter]() {
            counter++;
        });
        
        const int numTriggers = 10000;
        for (int i = 0; i < numTriggers; ++i) {
            callbackable.triggerCallback("repeated");
        }
        
        REQUIRE(counter == numTriggers);
    }
}
