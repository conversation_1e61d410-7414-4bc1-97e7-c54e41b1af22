#include <catch2/catch_test_macros.hpp>

// C++ standard library
#include <algorithm>
#include <vector>

// Third-party libraries
#define GLFW_INCLUDE_NONE
#include <GLFW/glfw3.h>

// Local includes
#include "window/glfw/glfw_mouse.hpp"
#include "window/glfw/glfw_window.hpp"
#include "../../test_environment.hpp"

using namespace IronFrost;

// Test fixture for GLFW_Mouse testing
class GLFWMouseTestFixture {
private:
    std::unique_ptr<GLFW_Window> m_window;

public:
    GLFWMouseTestFixture() {
        // Initialize GLFW if not already initialized
        if (!glfwInit()) {
            throw std::runtime_error("Failed to initialize GLFW for testing");
        }

        // Create window (this will create the mouse)
        m_window = GLFW_Window::create(800, 600, "Test Window");

        if (!m_window || !m_window->isInitialized()) {
            throw std::runtime_error("Failed to create GLFW window for testing");
        }
    }

    ~GLFWMouseTestFixture() {
        m_window.reset();
    }

    GLFW_Mouse& getMouse() {
        return dynamic_cast<GLFW_Mouse&>(m_window->getMouse());
    }

    GLFW_Window& getWindow() {
        return *m_window;
    }
};

TEST_CASE("GLFW_Mouse construction and initial state", "[window][glfw][mouse][construction]") {
    SKIP_IF_HEADLESS();

    GLFWMouseTestFixture fixture;
    GLFW_Mouse& mouse = fixture.getMouse();

    SECTION("Initial position is zero") {
        REQUIRE(mouse.getXPos() == 0.0);
        REQUIRE(mouse.getYPos() == 0.0);
    }

    SECTION("All buttons start in released state") {
        REQUIRE(mouse.isButtonReleased(MOUSE_BUTTON_LEFT) == true);
        REQUIRE(mouse.isButtonReleased(MOUSE_BUTTON_RIGHT) == true);
        REQUIRE(mouse.isButtonReleased(MOUSE_BUTTON_MIDDLE) == true);

        REQUIRE(mouse.isButtonPressed(MOUSE_BUTTON_LEFT) == false);
        REQUIRE(mouse.isButtonPressed(MOUSE_BUTTON_RIGHT) == false);
        REQUIRE(mouse.isButtonPressed(MOUSE_BUTTON_MIDDLE) == false);
    }

    SECTION("All button types are within valid range") {
        for (int i = 0; i < MOUSE_BUTTON_NUM_OF_TYPES; ++i) {
            MouseButtonType button = static_cast<MouseButtonType>(i);
            
            // Initially all buttons should be released
            REQUIRE(mouse.isButtonReleased(button) == true);
            REQUIRE(mouse.isButtonPressed(button) == false);
        }
    }
}

TEST_CASE("GLFW_Mouse button state management", "[window][glfw][mouse][buttons]") {
    SKIP_IF_HEADLESS();

    GLFWMouseTestFixture fixture;
    GLFW_Mouse& mouse = fixture.getMouse();

    SECTION("updateButtonState changes button state") {
        // Initially released
        REQUIRE(mouse.isButtonReleased(MOUSE_BUTTON_LEFT) == true);
        REQUIRE(mouse.isButtonPressed(MOUSE_BUTTON_LEFT) == false);

        // Update to pressed
        mouse.updateButtonState(MOUSE_BUTTON_LEFT, MOUSE_BUTTON_DOWN);
        
        REQUIRE(mouse.isButtonReleased(MOUSE_BUTTON_LEFT) == false);
        REQUIRE(mouse.isButtonPressed(MOUSE_BUTTON_LEFT) == true);
    }

    SECTION("updateButtonState can release pressed button") {
        // First press the button
        mouse.updateButtonState(MOUSE_BUTTON_RIGHT, MOUSE_BUTTON_DOWN);
        REQUIRE(mouse.isButtonPressed(MOUSE_BUTTON_RIGHT) == true);

        // Then release it
        mouse.updateButtonState(MOUSE_BUTTON_RIGHT, MOUSE_BUTTON_UP);
        
        REQUIRE(mouse.isButtonReleased(MOUSE_BUTTON_RIGHT) == true);
        REQUIRE(mouse.isButtonPressed(MOUSE_BUTTON_RIGHT) == false);
    }

    SECTION("Multiple buttons can be pressed simultaneously") {
        mouse.updateButtonState(MOUSE_BUTTON_LEFT, MOUSE_BUTTON_DOWN);
        mouse.updateButtonState(MOUSE_BUTTON_RIGHT, MOUSE_BUTTON_DOWN);
        mouse.updateButtonState(MOUSE_BUTTON_MIDDLE, MOUSE_BUTTON_DOWN);

        REQUIRE(mouse.isButtonPressed(MOUSE_BUTTON_LEFT) == true);
        REQUIRE(mouse.isButtonPressed(MOUSE_BUTTON_RIGHT) == true);
        REQUIRE(mouse.isButtonPressed(MOUSE_BUTTON_MIDDLE) == true);
    }

    SECTION("Button state transitions work correctly") {
        MouseButtonType button = MOUSE_BUTTON_MIDDLE;
        
        // Initially released
        REQUIRE(mouse.isButtonReleased(button) == true);

        // Press button
        mouse.updateButtonState(button, MOUSE_BUTTON_DOWN);
        REQUIRE(mouse.isButtonPressed(button) == true);
        REQUIRE(mouse.isButtonReleased(button) == false);

        // Release button
        mouse.updateButtonState(button, MOUSE_BUTTON_UP);
        REQUIRE(mouse.isButtonReleased(button) == true);
        REQUIRE(mouse.isButtonPressed(button) == false);

        // Press again
        mouse.updateButtonState(button, MOUSE_BUTTON_DOWN);
        REQUIRE(mouse.isButtonPressed(button) == true);
    }
}

TEST_CASE("GLFW_Mouse edge cases and bounds checking", "[window][glfw][mouse][edge_cases]") {
    SKIP_IF_HEADLESS();

    GLFWMouseTestFixture fixture;
    GLFW_Mouse& mouse = fixture.getMouse();

    SECTION("Invalid button indices handled gracefully") {
        // Test negative button index
        REQUIRE(mouse.isButtonPressed(static_cast<MouseButtonType>(-1)) == false);
        REQUIRE(mouse.isButtonReleased(static_cast<MouseButtonType>(-1)) == false);

        // Test out-of-bounds button index
        REQUIRE(mouse.isButtonPressed(static_cast<MouseButtonType>(MOUSE_BUTTON_NUM_OF_TYPES)) == false);
        REQUIRE(mouse.isButtonReleased(static_cast<MouseButtonType>(MOUSE_BUTTON_NUM_OF_TYPES)) == false);

        // Test way out-of-bounds button index
        REQUIRE(mouse.isButtonPressed(static_cast<MouseButtonType>(MOUSE_BUTTON_NUM_OF_TYPES + 100)) == false);
        REQUIRE(mouse.isButtonReleased(static_cast<MouseButtonType>(MOUSE_BUTTON_NUM_OF_TYPES + 100)) == false);
    }

    SECTION("Boundary button values work correctly") {
        // Test first valid button
        MouseButtonType firstButton = static_cast<MouseButtonType>(0);
        mouse.updateButtonState(firstButton, MOUSE_BUTTON_DOWN);
        REQUIRE(mouse.isButtonPressed(firstButton) == true);

        // Test last valid button
        MouseButtonType lastButton = static_cast<MouseButtonType>(MOUSE_BUTTON_NUM_OF_TYPES - 1);
        mouse.updateButtonState(lastButton, MOUSE_BUTTON_DOWN);
        REQUIRE(mouse.isButtonPressed(lastButton) == true);
    }

    SECTION("Setting invalid buttons doesn't crash") {
        // These should not crash or affect valid buttons
        mouse.updateButtonState(static_cast<MouseButtonType>(-1), MOUSE_BUTTON_DOWN);
        mouse.updateButtonState(static_cast<MouseButtonType>(MOUSE_BUTTON_NUM_OF_TYPES), MOUSE_BUTTON_DOWN);
        mouse.updateButtonState(static_cast<MouseButtonType>(MOUSE_BUTTON_NUM_OF_TYPES + 100), MOUSE_BUTTON_DOWN);

        // Valid button should still work
        mouse.updateButtonState(MOUSE_BUTTON_LEFT, MOUSE_BUTTON_DOWN);
        REQUIRE(mouse.isButtonPressed(MOUSE_BUTTON_LEFT) == true);
    }
}

TEST_CASE("GLFW_Mouse position management", "[window][glfw][mouse][position]") {
    SKIP_IF_HEADLESS();

    GLFWMouseTestFixture fixture;
    GLFW_Mouse& mouse = fixture.getMouse();

    SECTION("Position starts at origin") {
        REQUIRE(mouse.getXPos() == 0.0);
        REQUIRE(mouse.getYPos() == 0.0);
    }

    SECTION("Position can be read consistently") {
        double x = mouse.getXPos();
        double y = mouse.getYPos();

        // Multiple reads should return same values
        REQUIRE(mouse.getXPos() == x);
        REQUIRE(mouse.getYPos() == y);
    }

    SECTION("Position values are valid doubles") {
        double x = mouse.getXPos();
        double y = mouse.getYPos();

        // Should not be NaN or infinite
        REQUIRE(std::isfinite(x) == true);
        REQUIRE(std::isfinite(y) == true);
    }
}

TEST_CASE("GLFW_Mouse button mapping", "[window][glfw][mouse][mapping]") {
    SKIP_IF_HEADLESS();

    GLFWMouseTestFixture fixture;
    GLFW_Mouse& mouse = fixture.getMouse();

    SECTION("All mapped buttons are within valid range") {
        // Test that common mouse buttons work correctly
        mouse.updateButtonState(MOUSE_BUTTON_LEFT, MOUSE_BUTTON_DOWN);
        REQUIRE(mouse.isButtonPressed(MOUSE_BUTTON_LEFT) == true);

        mouse.updateButtonState(MOUSE_BUTTON_RIGHT, MOUSE_BUTTON_DOWN);
        REQUIRE(mouse.isButtonPressed(MOUSE_BUTTON_RIGHT) == true);

        mouse.updateButtonState(MOUSE_BUTTON_MIDDLE, MOUSE_BUTTON_DOWN);
        REQUIRE(mouse.isButtonPressed(MOUSE_BUTTON_MIDDLE) == true);
    }

    SECTION("Button state consistency") {
        // Test that isButtonPressed and isButtonReleased are always opposite for valid buttons
        for (int i = 0; i < MOUSE_BUTTON_NUM_OF_TYPES; ++i) {
            MouseButtonType button = static_cast<MouseButtonType>(i);

            // Initially all buttons should be released
            REQUIRE(mouse.isButtonReleased(button) == true);
            REQUIRE(mouse.isButtonPressed(button) == false);

            // After setting pressed, should be pressed
            mouse.updateButtonState(button, MOUSE_BUTTON_DOWN);
            REQUIRE(mouse.isButtonReleased(button) == false);
            REQUIRE(mouse.isButtonPressed(button) == true);

            // After setting released, should be released again
            mouse.updateButtonState(button, MOUSE_BUTTON_UP);
            REQUIRE(mouse.isButtonReleased(button) == true);
            REQUIRE(mouse.isButtonPressed(button) == false);
        }
    }
}

TEST_CASE("GLFW_Mouse stress testing", "[window][glfw][mouse][stress]") {
    SKIP_IF_HEADLESS();

    GLFWMouseTestFixture fixture;
    GLFW_Mouse& mouse = fixture.getMouse();

    SECTION("Rapid button state changes") {
        // Rapidly toggle button states
        for (int i = 0; i < 1000; ++i) {
            mouse.updateButtonState(MOUSE_BUTTON_LEFT, MOUSE_BUTTON_DOWN);
            REQUIRE(mouse.isButtonPressed(MOUSE_BUTTON_LEFT) == true);

            mouse.updateButtonState(MOUSE_BUTTON_LEFT, MOUSE_BUTTON_UP);
            REQUIRE(mouse.isButtonReleased(MOUSE_BUTTON_LEFT) == true);
        }
    }

    SECTION("Many buttons pressed simultaneously") {
        // Press all available buttons
        std::vector<MouseButtonType> buttonsToTest;
        for (int i = 0; i < MOUSE_BUTTON_NUM_OF_TYPES; ++i) {
            buttonsToTest.push_back(static_cast<MouseButtonType>(i));
        }

        // Press all buttons
        for (MouseButtonType button : buttonsToTest) {
            mouse.updateButtonState(button, MOUSE_BUTTON_DOWN);
        }

        // Verify all are pressed
        for (MouseButtonType button : buttonsToTest) {
            REQUIRE(mouse.isButtonPressed(button) == true);
        }

        // Release all buttons
        for (MouseButtonType button : buttonsToTest) {
            mouse.updateButtonState(button, MOUSE_BUTTON_UP);
        }

        // Verify all are released
        for (MouseButtonType button : buttonsToTest) {
            REQUIRE(mouse.isButtonReleased(button) == true);
        }
    }

    SECTION("Repeated state setting") {
        // Setting same state multiple times should be safe
        for (int i = 0; i < 100; ++i) {
            mouse.updateButtonState(MOUSE_BUTTON_RIGHT, MOUSE_BUTTON_DOWN);
            REQUIRE(mouse.isButtonPressed(MOUSE_BUTTON_RIGHT) == true);
        }

        for (int i = 0; i < 100; ++i) {
            mouse.updateButtonState(MOUSE_BUTTON_RIGHT, MOUSE_BUTTON_UP);
            REQUIRE(mouse.isButtonReleased(MOUSE_BUTTON_RIGHT) == true);
        }
    }
}

TEST_CASE("GLFW_Mouse internal state management", "[window][glfw][mouse][state_management]") {
    SKIP_IF_HEADLESS();

    GLFWMouseTestFixture fixture;
    GLFW_Mouse& mouse = fixture.getMouse();

    SECTION("updateButtonState updates internal state only") {
        REQUIRE(mouse.isButtonReleased(MOUSE_BUTTON_LEFT) == true);
        REQUIRE(mouse.isButtonPressed(MOUSE_BUTTON_LEFT) == false);

        mouse.updateButtonState(MOUSE_BUTTON_LEFT, MOUSE_BUTTON_DOWN);

        // State should be updated
        REQUIRE(mouse.isButtonReleased(MOUSE_BUTTON_LEFT) == false);
        REQUIRE(mouse.isButtonPressed(MOUSE_BUTTON_LEFT) == true);

        // Note: updateButtonState does NOT dispatch events - that's done by GLFW callbacks
    }

    SECTION("Multiple state changes work correctly") {
        // Test sequence of state changes
        mouse.updateButtonState(MOUSE_BUTTON_LEFT, MOUSE_BUTTON_DOWN);
        mouse.updateButtonState(MOUSE_BUTTON_RIGHT, MOUSE_BUTTON_DOWN);
        mouse.updateButtonState(MOUSE_BUTTON_LEFT, MOUSE_BUTTON_UP);
        mouse.updateButtonState(MOUSE_BUTTON_MIDDLE, MOUSE_BUTTON_DOWN);
        mouse.updateButtonState(MOUSE_BUTTON_RIGHT, MOUSE_BUTTON_UP);
        mouse.updateButtonState(MOUSE_BUTTON_MIDDLE, MOUSE_BUTTON_UP);

        // Verify final states
        REQUIRE(mouse.isButtonReleased(MOUSE_BUTTON_LEFT) == true);
        REQUIRE(mouse.isButtonReleased(MOUSE_BUTTON_RIGHT) == true);
        REQUIRE(mouse.isButtonReleased(MOUSE_BUTTON_MIDDLE) == true);
    }

    SECTION("State consistency maintained") {
        // Test that state changes are consistent
        for (int i = 0; i < MOUSE_BUTTON_NUM_OF_TYPES; ++i) {
            MouseButtonType button = static_cast<MouseButtonType>(i);

            // Initially released
            REQUIRE(mouse.isButtonReleased(button) == true);
            REQUIRE(mouse.isButtonPressed(button) == false);

            // Set pressed
            mouse.updateButtonState(button, MOUSE_BUTTON_DOWN);
            REQUIRE(mouse.isButtonReleased(button) == false);
            REQUIRE(mouse.isButtonPressed(button) == true);

            // Set released
            mouse.updateButtonState(button, MOUSE_BUTTON_UP);
            REQUIRE(mouse.isButtonReleased(button) == true);
            REQUIRE(mouse.isButtonPressed(button) == false);
        }
    }
}

TEST_CASE("GLFW_Mouse real-world usage scenarios", "[window][glfw][mouse][real_world]") {
    SKIP_IF_HEADLESS();

    GLFWMouseTestFixture fixture;
    GLFW_Mouse& mouse = fixture.getMouse();

    SECTION("Game input simulation") {
        // Simulate typical game mouse usage

        // Left click for primary action
        mouse.updateButtonState(MOUSE_BUTTON_LEFT, MOUSE_BUTTON_DOWN);
        REQUIRE(mouse.isButtonPressed(MOUSE_BUTTON_LEFT) == true);

        // Right click for secondary action while left is held
        mouse.updateButtonState(MOUSE_BUTTON_RIGHT, MOUSE_BUTTON_DOWN);
        REQUIRE(mouse.isButtonPressed(MOUSE_BUTTON_LEFT) == true);
        REQUIRE(mouse.isButtonPressed(MOUSE_BUTTON_RIGHT) == true);

        // Release left click
        mouse.updateButtonState(MOUSE_BUTTON_LEFT, MOUSE_BUTTON_UP);
        REQUIRE(mouse.isButtonReleased(MOUSE_BUTTON_LEFT) == true);
        REQUIRE(mouse.isButtonPressed(MOUSE_BUTTON_RIGHT) == true);

        // Release right click
        mouse.updateButtonState(MOUSE_BUTTON_RIGHT, MOUSE_BUTTON_UP);
        REQUIRE(mouse.isButtonReleased(MOUSE_BUTTON_LEFT) == true);
        REQUIRE(mouse.isButtonReleased(MOUSE_BUTTON_RIGHT) == true);
    }

    SECTION("UI interaction simulation") {
        // Simulate UI mouse interactions

        // Single click
        mouse.updateButtonState(MOUSE_BUTTON_LEFT, MOUSE_BUTTON_DOWN);
        REQUIRE(mouse.isButtonPressed(MOUSE_BUTTON_LEFT) == true);
        mouse.updateButtonState(MOUSE_BUTTON_LEFT, MOUSE_BUTTON_UP);
        REQUIRE(mouse.isButtonReleased(MOUSE_BUTTON_LEFT) == true);

        // Double click simulation
        mouse.updateButtonState(MOUSE_BUTTON_LEFT, MOUSE_BUTTON_DOWN);
        mouse.updateButtonState(MOUSE_BUTTON_LEFT, MOUSE_BUTTON_UP);
        mouse.updateButtonState(MOUSE_BUTTON_LEFT, MOUSE_BUTTON_DOWN);
        mouse.updateButtonState(MOUSE_BUTTON_LEFT, MOUSE_BUTTON_UP);
        REQUIRE(mouse.isButtonReleased(MOUSE_BUTTON_LEFT) == true);

        // Context menu (right click)
        mouse.updateButtonState(MOUSE_BUTTON_RIGHT, MOUSE_BUTTON_DOWN);
        REQUIRE(mouse.isButtonPressed(MOUSE_BUTTON_RIGHT) == true);
        mouse.updateButtonState(MOUSE_BUTTON_RIGHT, MOUSE_BUTTON_UP);
        REQUIRE(mouse.isButtonReleased(MOUSE_BUTTON_RIGHT) == true);
    }

    SECTION("Complex interaction patterns") {
        // Simulate complex mouse interaction patterns

        // Drag operation (press, hold, release)
        mouse.updateButtonState(MOUSE_BUTTON_LEFT, MOUSE_BUTTON_DOWN);
        REQUIRE(mouse.isButtonPressed(MOUSE_BUTTON_LEFT) == true);

        // Simulate holding for drag (state should remain pressed)
        for (int i = 0; i < 10; ++i) {
            REQUIRE(mouse.isButtonPressed(MOUSE_BUTTON_LEFT) == true);
            REQUIRE(mouse.isButtonReleased(MOUSE_BUTTON_LEFT) == false);
        }

        // End drag
        mouse.updateButtonState(MOUSE_BUTTON_LEFT, MOUSE_BUTTON_UP);
        REQUIRE(mouse.isButtonReleased(MOUSE_BUTTON_LEFT) == true);

        // Multi-button selection (Ctrl+Click simulation)
        mouse.updateButtonState(MOUSE_BUTTON_LEFT, MOUSE_BUTTON_DOWN);
        mouse.updateButtonState(MOUSE_BUTTON_MIDDLE, MOUSE_BUTTON_DOWN); // Simulate modifier
        REQUIRE(mouse.isButtonPressed(MOUSE_BUTTON_LEFT) == true);
        REQUIRE(mouse.isButtonPressed(MOUSE_BUTTON_MIDDLE) == true);

        mouse.updateButtonState(MOUSE_BUTTON_LEFT, MOUSE_BUTTON_UP);
        mouse.updateButtonState(MOUSE_BUTTON_MIDDLE, MOUSE_BUTTON_UP);
        REQUIRE(mouse.isButtonReleased(MOUSE_BUTTON_LEFT) == true);
        REQUIRE(mouse.isButtonReleased(MOUSE_BUTTON_MIDDLE) == true);
    }
}

TEST_CASE("GLFW_Mouse coordinate system", "[window][glfw][mouse][coordinates]") {
    SKIP_IF_HEADLESS();

    GLFWMouseTestFixture fixture;
    GLFW_Mouse& mouse = fixture.getMouse();

    SECTION("Position coordinates are accessible") {
        // Position should be readable
        double x = mouse.getXPos();
        double y = mouse.getYPos();

        // Should be valid numbers
        REQUIRE(std::isfinite(x) == true);
        REQUIRE(std::isfinite(y) == true);
    }

    SECTION("Position values are consistent") {
        // Multiple calls should return same values
        double x1 = mouse.getXPos();
        double y1 = mouse.getYPos();
        double x2 = mouse.getXPos();
        double y2 = mouse.getYPos();

        REQUIRE(x1 == x2);
        REQUIRE(y1 == y2);
    }

    SECTION("Initial position is at origin") {
        // Mouse should start at (0, 0)
        REQUIRE(mouse.getXPos() == 0.0);
        REQUIRE(mouse.getYPos() == 0.0);
    }
}

TEST_CASE("GLFW_Mouse integration with window", "[window][glfw][mouse][integration]") {
    SKIP_IF_HEADLESS();

    GLFWMouseTestFixture fixture;
    GLFW_Mouse& mouse = fixture.getMouse();
    GLFW_Window& window = fixture.getWindow();

    SECTION("Mouse is properly integrated with window") {
        // Mouse should be accessible through window
        IMouse& windowMouse = window.getMouse();

        // Should be the same instance
        REQUIRE(&windowMouse == &mouse);
    }

    SECTION("Mouse state is independent of window operations") {
        // Set some mouse state
        mouse.updateButtonState(MOUSE_BUTTON_LEFT, MOUSE_BUTTON_DOWN);
        REQUIRE(mouse.isButtonPressed(MOUSE_BUTTON_LEFT) == true);

        // Window operations shouldn't affect mouse state
        // (We can't easily test window operations without affecting the test environment,
        // but we can verify the state remains consistent)
        REQUIRE(mouse.isButtonPressed(MOUSE_BUTTON_LEFT) == true);
        REQUIRE(mouse.getXPos() == 0.0);
        REQUIRE(mouse.getYPos() == 0.0);
    }

    SECTION("Mouse interface methods work correctly") {
        // Test through the interface
        IMouse& iMouse = window.getMouse();

        // Position methods
        REQUIRE(iMouse.getXPos() == 0.0);
        REQUIRE(iMouse.getYPos() == 0.0);

        // Button methods
        REQUIRE(iMouse.isButtonReleased(MOUSE_BUTTON_LEFT) == true);
        REQUIRE(iMouse.isButtonPressed(MOUSE_BUTTON_LEFT) == false);

        // Update state through concrete class
        mouse.updateButtonState(MOUSE_BUTTON_LEFT, MOUSE_BUTTON_DOWN);

        // Verify through interface
        REQUIRE(iMouse.isButtonPressed(MOUSE_BUTTON_LEFT) == true);
        REQUIRE(iMouse.isButtonReleased(MOUSE_BUTTON_LEFT) == false);
    }
}

TEST_CASE("GLFW_Mouse performance characteristics", "[window][glfw][mouse][performance]") {
    SKIP_IF_HEADLESS();

    GLFWMouseTestFixture fixture;
    GLFW_Mouse& mouse = fixture.getMouse();

    SECTION("Button state queries are fast") {
        // Set up some button states
        mouse.updateButtonState(MOUSE_BUTTON_LEFT, MOUSE_BUTTON_DOWN);
        mouse.updateButtonState(MOUSE_BUTTON_RIGHT, MOUSE_BUTTON_UP);
        mouse.updateButtonState(MOUSE_BUTTON_MIDDLE, MOUSE_BUTTON_DOWN);

        // Rapid queries should be consistent and fast
        for (int i = 0; i < 10000; ++i) {
            REQUIRE(mouse.isButtonPressed(MOUSE_BUTTON_LEFT) == true);
            REQUIRE(mouse.isButtonReleased(MOUSE_BUTTON_RIGHT) == true);
            REQUIRE(mouse.isButtonPressed(MOUSE_BUTTON_MIDDLE) == true);
        }
    }

    SECTION("Position queries are fast") {
        // Rapid position queries should be consistent
        double expectedX = mouse.getXPos();
        double expectedY = mouse.getYPos();

        for (int i = 0; i < 10000; ++i) {
            REQUIRE(mouse.getXPos() == expectedX);
            REQUIRE(mouse.getYPos() == expectedY);
        }
    }

    SECTION("State updates are efficient") {
        // Rapid state updates should work correctly
        for (int i = 0; i < 1000; ++i) {
            mouse.updateButtonState(MOUSE_BUTTON_LEFT, MOUSE_BUTTON_DOWN);
            mouse.updateButtonState(MOUSE_BUTTON_RIGHT, MOUSE_BUTTON_DOWN);
            mouse.updateButtonState(MOUSE_BUTTON_MIDDLE, MOUSE_BUTTON_DOWN);

            REQUIRE(mouse.isButtonPressed(MOUSE_BUTTON_LEFT) == true);
            REQUIRE(mouse.isButtonPressed(MOUSE_BUTTON_RIGHT) == true);
            REQUIRE(mouse.isButtonPressed(MOUSE_BUTTON_MIDDLE) == true);

            mouse.updateButtonState(MOUSE_BUTTON_LEFT, MOUSE_BUTTON_UP);
            mouse.updateButtonState(MOUSE_BUTTON_RIGHT, MOUSE_BUTTON_UP);
            mouse.updateButtonState(MOUSE_BUTTON_MIDDLE, MOUSE_BUTTON_UP);

            REQUIRE(mouse.isButtonReleased(MOUSE_BUTTON_LEFT) == true);
            REQUIRE(mouse.isButtonReleased(MOUSE_BUTTON_RIGHT) == true);
            REQUIRE(mouse.isButtonReleased(MOUSE_BUTTON_MIDDLE) == true);
        }
    }
}

TEST_CASE("GLFW_Mouse relative position functionality", "[window][glfw][mouse][relative]") {
    SKIP_IF_HEADLESS();

    GLFWMouseTestFixture fixture;
    GLFW_Mouse& mouse = fixture.getMouse();

    SECTION("Initial relative position is zero") {
        REQUIRE(mouse.getRelativeXPos() == 0.0);
        REQUIRE(mouse.getRelativeYPos() == 0.0);
    }

    SECTION("Reset relative position works") {
        // Note: We can't easily test actual mouse movement in unit tests
        // since it requires GLFW callbacks, but we can test the reset functionality
        mouse.resetRelativePosition();

        REQUIRE(mouse.getRelativeXPos() == 0.0);
        REQUIRE(mouse.getRelativeYPos() == 0.0);
    }

    SECTION("Relative position methods exist and return doubles") {
        // Test that the methods exist and return the expected type
        double relX = mouse.getRelativeXPos();
        double relY = mouse.getRelativeYPos();

        // These should be valid double values (not NaN)
        REQUIRE(relX == relX); // NaN check (NaN != NaN)
        REQUIRE(relY == relY); // NaN check (NaN != NaN)
    }
}
