#include <catch2/catch_test_macros.hpp>

#include "window/glfw/glfw_keyboard.hpp"
#include "window/glfw/glfw_window.hpp"
#include "events/event_dispatcher.hpp"
#include "events/events.hpp"
#include "services/service_locator.hpp"
#include "window/keys.hpp"
#include "window/keyboard_events.hpp"
#include "test_utils.hpp"

#include <memory>
#include <functional>
#include <vector>

using namespace IronFrost;

// Event tracking utility for testing
class KeyboardEventTracker {
private:
    std::vector<std::string> m_dispatchedEvents;
    std::vector<KeyType> m_keyDownEvents;
    std::vector<KeyType> m_keyUpEvents;
    std::vector<unsigned int> m_keyInputEvents;
    
public:
    void trackKeyDown(KeyType key) {
        m_keyDownEvents.push_back(key);
        m_dispatchedEvents.push_back("KeyDownEvent");
    }
    
    void trackKeyUp(KeyType key) {
        m_keyUpEvents.push_back(key);
        m_dispatchedEvents.push_back("KeyUpEvent");
    }
    
    void trackKeyInput(unsigned int codepoint) {
        m_keyInputEvents.push_back(codepoint);
        m_dispatchedEvents.push_back("KeyInputEvent");
    }
    
    const std::vector<std::string>& getDispatchedEvents() const {
        return m_dispatchedEvents;
    }
    
    const std::vector<KeyType>& getKeyDownEvents() const {
        return m_keyDownEvents;
    }
    
    const std::vector<KeyType>& getKeyUpEvents() const {
        return m_keyUpEvents;
    }
    
    const std::vector<unsigned int>& getKeyInputEvents() const {
        return m_keyInputEvents;
    }
    
    void clearEvents() {
        m_dispatchedEvents.clear();
        m_keyDownEvents.clear();
        m_keyUpEvents.clear();
        m_keyInputEvents.clear();
    }
    
    bool wasEventDispatched(const std::string& eventType) const {
        return std::find(m_dispatchedEvents.begin(), m_dispatchedEvents.end(), eventType) != m_dispatchedEvents.end();
    }
    
    bool wasKeyPressed(KeyType key) const {
        return std::find(m_keyDownEvents.begin(), m_keyDownEvents.end(), key) != m_keyDownEvents.end();
    }
    
    bool wasKeyReleased(KeyType key) const {
        return std::find(m_keyUpEvents.begin(), m_keyUpEvents.end(), key) != m_keyUpEvents.end();
    }
};

// Test fixture for GLFW_Keyboard tests
class GLFWKeyboardTestFixture {
private:
    std::unique_ptr<EventDispatcher> m_eventDispatcher;
    std::unique_ptr<GLFW_Window> m_window;
    KeyboardEventTracker m_eventTracker;
    
public:
    GLFWKeyboardTestFixture() {
        // Initialize GLFW if not already initialized
        if (!glfwInit()) {
            throw std::runtime_error("Failed to initialize GLFW for testing");
        }
        
        // Create event dispatcher
        m_eventDispatcher = std::make_unique<EventDispatcher>();
        
        // Register event tracking listeners
        m_eventDispatcher->registerListener<KeyDownEvent>(
            [this](const KeyDownEvent& event) {
                m_eventTracker.trackKeyDown(static_cast<KeyType>(event.getKey()));
            });
            
        m_eventDispatcher->registerListener<KeyUpEvent>(
            [this](const KeyUpEvent& event) {
                m_eventTracker.trackKeyUp(static_cast<KeyType>(event.getKey()));
            });
            
        m_eventDispatcher->registerListener<KeyInputEvent>(
            [this](const KeyInputEvent& event) {
                m_eventTracker.trackKeyInput(event.getInput());
            });
        
        // Register it with ServiceLocator
        ServiceLocator::registerService<EventDispatcher>(std::move(m_eventDispatcher));
        
        // Create window (this will create the keyboard)
        m_window = GLFW_Window::create(800, 600, "Test Window");
        
        if (!m_window || !m_window->isInitialized()) {
            throw std::runtime_error("Failed to create GLFW window for testing");
        }
    }
    
    ~GLFWKeyboardTestFixture() {
        // Clean up window first
        m_window.reset();
        // Note: ServiceLocator cleanup is handled by static destruction
        // GLFW cleanup is handled by the window destructor
    }
    
    GLFW_Keyboard& getKeyboard() { 
        return static_cast<GLFW_Keyboard&>(m_window->getKeyboard());
    }
    
    KeyboardEventTracker& getEventTracker() { 
        return m_eventTracker;
    }
    
    EventDispatcher& getEventDispatcher() {
        return ServiceLocator::getService<EventDispatcher>();
    }
    
    GLFW_Window& getWindow() {
        return *m_window;
    }
};

TEST_CASE("GLFW_Keyboard construction and initial state", "[window][glfw][keyboard][construction]") {
    GLFWKeyboardTestFixture fixture;
    GLFW_Keyboard& keyboard = fixture.getKeyboard();
    
    SECTION("All keys start in UP state") {
        // Test a representative sample of keys
        REQUIRE(keyboard.isKeyUp(KEY_A) == true);
        REQUIRE(keyboard.isKeyUp(KEY_SPACE) == true);
        REQUIRE(keyboard.isKeyUp(KEY_ENTER) == true);
        REQUIRE(keyboard.isKeyUp(KEY_ESCAPE) == true);
        REQUIRE(keyboard.isKeyUp(KEY_ARROW_UP) == true);
        
        REQUIRE(keyboard.isKeyDown(KEY_A) == false);
        REQUIRE(keyboard.isKeyDown(KEY_SPACE) == false);
        REQUIRE(keyboard.isKeyDown(KEY_ENTER) == false);
        REQUIRE(keyboard.isKeyDown(KEY_ESCAPE) == false);
        REQUIRE(keyboard.isKeyDown(KEY_ARROW_UP) == false);
    }
    
    SECTION("All numeric keys start in UP state") {
        for (int i = KEY_0; i <= KEY_9; ++i) {
            KeyType key = static_cast<KeyType>(i);
            REQUIRE(keyboard.isKeyUp(key) == true);
            REQUIRE(keyboard.isKeyDown(key) == false);
        }
    }
    
    SECTION("All letter keys start in UP state") {
        for (int i = KEY_A; i <= KEY_Z; ++i) {
            KeyType key = static_cast<KeyType>(i);
            REQUIRE(keyboard.isKeyUp(key) == true);
            REQUIRE(keyboard.isKeyDown(key) == false);
        }
    }
}

TEST_CASE("GLFW_Keyboard key state management", "[window][glfw][keyboard][state]") {
    GLFWKeyboardTestFixture fixture;
    GLFW_Keyboard& keyboard = fixture.getKeyboard();
    KeyboardEventTracker& eventTracker = fixture.getEventTracker();

    SECTION("setKeyDown changes key state") {
        REQUIRE(keyboard.isKeyUp(KEY_A) == true);
        REQUIRE(keyboard.isKeyDown(KEY_A) == false);

        // Use public method for testing
        keyboard.setKeyDown(KEY_A);

        REQUIRE(keyboard.isKeyUp(KEY_A) == false);
        REQUIRE(keyboard.isKeyDown(KEY_A) == true);
    }

    SECTION("setKeyUp changes key state") {
        // First set key down
        keyboard.setKeyDown(KEY_B);
        REQUIRE(keyboard.isKeyDown(KEY_B) == true);

        // Then set key up
        keyboard.setKeyUp(KEY_B);
        REQUIRE(keyboard.isKeyUp(KEY_B) == true);
        REQUIRE(keyboard.isKeyDown(KEY_B) == false);
    }

    SECTION("Multiple keys can be down simultaneously") {
        keyboard.setKeyDown(KEY_A);
        keyboard.setKeyDown(KEY_B);
        keyboard.setKeyDown(KEY_C);

        REQUIRE(keyboard.isKeyDown(KEY_A) == true);
        REQUIRE(keyboard.isKeyDown(KEY_B) == true);
        REQUIRE(keyboard.isKeyDown(KEY_C) == true);

        // Other keys should still be up
        REQUIRE(keyboard.isKeyUp(KEY_D) == true);
        REQUIRE(keyboard.isKeyUp(KEY_E) == true);
    }

    SECTION("Key state transitions work correctly") {
        // Start up
        REQUIRE(keyboard.isKeyUp(KEY_SPACE) == true);

        // Press down
        keyboard.setKeyDown(KEY_SPACE);
        REQUIRE(keyboard.isKeyDown(KEY_SPACE) == true);
        REQUIRE(keyboard.isKeyUp(KEY_SPACE) == false);

        // Release
        keyboard.setKeyUp(KEY_SPACE);
        REQUIRE(keyboard.isKeyUp(KEY_SPACE) == true);
        REQUIRE(keyboard.isKeyDown(KEY_SPACE) == false);

        // Press again
        keyboard.setKeyDown(KEY_SPACE);
        REQUIRE(keyboard.isKeyDown(KEY_SPACE) == true);
    }
}

TEST_CASE("GLFW_Keyboard edge cases and bounds checking", "[window][glfw][keyboard][edge_cases]") {
    GLFWKeyboardTestFixture fixture;
    GLFW_Keyboard& keyboard = fixture.getKeyboard();
    
    SECTION("Invalid key indices handled gracefully") {
        // Test negative key values
        REQUIRE(keyboard.isKeyDown(static_cast<KeyType>(-1)) == false);
        REQUIRE(keyboard.isKeyUp(static_cast<KeyType>(-1)) == true);
        
        // Test key values beyond range
        REQUIRE(keyboard.isKeyDown(static_cast<KeyType>(KEY_NUM_OF_KEYS)) == false);
        REQUIRE(keyboard.isKeyUp(static_cast<KeyType>(KEY_NUM_OF_KEYS)) == true);
        
        REQUIRE(keyboard.isKeyDown(static_cast<KeyType>(KEY_NUM_OF_KEYS + 100)) == false);
        REQUIRE(keyboard.isKeyUp(static_cast<KeyType>(KEY_NUM_OF_KEYS + 100)) == true);
    }
    
    SECTION("Boundary key values work correctly") {
        // Test first valid key
        KeyType firstKey = static_cast<KeyType>(0);
        keyboard.setKeyDown(firstKey);
        REQUIRE(keyboard.isKeyDown(firstKey) == true);

        // Test last valid key
        KeyType lastKey = static_cast<KeyType>(KEY_NUM_OF_KEYS - 1);
        keyboard.setKeyDown(lastKey);
        REQUIRE(keyboard.isKeyDown(lastKey) == true);
    }

    SECTION("Setting invalid keys doesn't crash") {
        // These should not crash or affect valid keys
        keyboard.setKeyDown(static_cast<KeyType>(-1));
        keyboard.setKeyUp(static_cast<KeyType>(-1));
        keyboard.setKeyDown(static_cast<KeyType>(KEY_NUM_OF_KEYS));
        keyboard.setKeyUp(static_cast<KeyType>(KEY_NUM_OF_KEYS));

        // Valid keys should still work
        keyboard.setKeyDown(KEY_A);
        REQUIRE(keyboard.isKeyDown(KEY_A) == true);
    }
}

TEST_CASE("GLFW_Keyboard callback functionality", "[window][glfw][keyboard][callbacks]") {
    GLFWKeyboardTestFixture fixture;
    GLFW_Keyboard& keyboard = fixture.getKeyboard();

    SECTION("setCallback registers callback correctly") {
        bool callbackTriggered = false;

        keyboard.setCallback(KEY_A, [&callbackTriggered]() {
            callbackTriggered = true;
        });

        // Callback should trigger when key is pressed (goes from UP to DOWN)
        REQUIRE(callbackTriggered == false);
        keyboard.setKeyDown(KEY_A);
        REQUIRE(callbackTriggered == true);
    }

    SECTION("Callback only triggers on key press, not release") {
        int callbackCount = 0;

        keyboard.setCallback(KEY_B, [&callbackCount]() {
            callbackCount++;
        });

        // First press should trigger callback
        keyboard.setKeyDown(KEY_B);
        REQUIRE(callbackCount == 1);

        // Release should not trigger callback
        keyboard.setKeyUp(KEY_B);
        REQUIRE(callbackCount == 1);

        // Second press should trigger callback again
        keyboard.setKeyDown(KEY_B);
        REQUIRE(callbackCount == 2);
    }

    SECTION("Callback doesn't trigger if key is already down") {
        int callbackCount = 0;

        keyboard.setCallback(KEY_C, [&callbackCount]() {
            callbackCount++;
        });

        // First press triggers callback
        keyboard.setKeyDown(KEY_C);
        REQUIRE(callbackCount == 1);

        // Setting key down again while already down should not trigger callback
        keyboard.setKeyDown(KEY_C);
        REQUIRE(callbackCount == 1);
    }

    SECTION("Multiple callbacks can be registered") {
        bool callback1Triggered = false;
        bool callback2Triggered = false;

        keyboard.setCallback(KEY_D, [&callback1Triggered]() {
            callback1Triggered = true;
        });

        keyboard.setCallback(KEY_E, [&callback2Triggered]() {
            callback2Triggered = true;
        });

        // Trigger first callback
        keyboard.setKeyDown(KEY_D);
        REQUIRE(callback1Triggered == true);
        REQUIRE(callback2Triggered == false);

        // Trigger second callback
        keyboard.setKeyDown(KEY_E);
        REQUIRE(callback1Triggered == true);
        REQUIRE(callback2Triggered == true);
    }

    SECTION("Callback can be overwritten") {
        int callback1Count = 0;
        int callback2Count = 0;

        // Set first callback
        keyboard.setCallback(KEY_F, [&callback1Count]() {
            callback1Count++;
        });

        keyboard.setKeyDown(KEY_F);
        REQUIRE(callback1Count == 1);
        REQUIRE(callback2Count == 0);

        keyboard.setKeyUp(KEY_F);

        // Overwrite with second callback
        keyboard.setCallback(KEY_F, [&callback2Count]() {
            callback2Count++;
        });

        keyboard.setKeyDown(KEY_F);
        REQUIRE(callback1Count == 1); // Should not increment
        REQUIRE(callback2Count == 1); // Should increment
    }

    SECTION("triggerCallback works directly") {
        bool callbackTriggered = false;

        keyboard.setCallback(KEY_G, [&callbackTriggered]() {
            callbackTriggered = true;
        });

        // Direct trigger should work
        keyboard.triggerCallback(KEY_G);
        REQUIRE(callbackTriggered == true);
    }

    SECTION("triggerCallback with no registered callback doesn't crash") {
        // Should not crash
        keyboard.triggerCallback(KEY_H);
        keyboard.triggerCallback(static_cast<KeyType>(-1));
        keyboard.triggerCallback(static_cast<KeyType>(KEY_NUM_OF_KEYS));
    }
}

TEST_CASE("GLFW_Keyboard key mapping", "[window][glfw][keyboard][mapping]") {
    GLFWKeyboardTestFixture fixture;
    GLFW_Keyboard& keyboard = fixture.getKeyboard();

    SECTION("All mapped keys are within valid range") {
        // This test verifies that the keyMap contains only valid KeyType values
        // We can't directly access the keyMap, but we can test the behavior

        // Test that common keys work correctly
        keyboard.setKeyDown(KEY_A);
        REQUIRE(keyboard.isKeyDown(KEY_A) == true);

        keyboard.setKeyDown(KEY_SPACE);
        REQUIRE(keyboard.isKeyDown(KEY_SPACE) == true);

        keyboard.setKeyDown(KEY_ENTER);
        REQUIRE(keyboard.isKeyDown(KEY_ENTER) == true);
    }

    SECTION("Key state consistency") {
        // Test that isKeyDown and isKeyUp are always opposite for valid keys
        for (int i = 0; i < KEY_NUM_OF_KEYS; ++i) {
            KeyType key = static_cast<KeyType>(i);

            // Initially all keys should be up
            REQUIRE(keyboard.isKeyUp(key) == true);
            REQUIRE(keyboard.isKeyDown(key) == false);

            // After setting down, should be down
            keyboard.setKeyDown(key);
            REQUIRE(keyboard.isKeyUp(key) == false);
            REQUIRE(keyboard.isKeyDown(key) == true);

            // After setting up, should be up again
            keyboard.setKeyUp(key);
            REQUIRE(keyboard.isKeyUp(key) == true);
            REQUIRE(keyboard.isKeyDown(key) == false);
        }
    }
}

TEST_CASE("GLFW_Keyboard stress testing", "[window][glfw][keyboard][stress]") {
    GLFWKeyboardTestFixture fixture;
    GLFW_Keyboard& keyboard = fixture.getKeyboard();

    SECTION("Rapid key state changes") {
        // Rapidly toggle key states
        for (int i = 0; i < 1000; ++i) {
            keyboard.setKeyDown(KEY_A);
            REQUIRE(keyboard.isKeyDown(KEY_A) == true);

            keyboard.setKeyUp(KEY_A);
            REQUIRE(keyboard.isKeyUp(KEY_A) == true);
        }
    }

    SECTION("Many keys pressed simultaneously") {
        // Press many keys at once
        std::vector<KeyType> keysToTest;
        for (int i = 0; i < std::min(100, static_cast<int>(KEY_NUM_OF_KEYS)); ++i) {
            keysToTest.push_back(static_cast<KeyType>(i));
        }

        // Press all keys
        for (KeyType key : keysToTest) {
            keyboard.setKeyDown(key);
        }

        // Verify all are down
        for (KeyType key : keysToTest) {
            REQUIRE(keyboard.isKeyDown(key) == true);
        }

        // Release all keys
        for (KeyType key : keysToTest) {
            keyboard.setKeyUp(key);
        }

        // Verify all are up
        for (KeyType key : keysToTest) {
            REQUIRE(keyboard.isKeyUp(key) == true);
        }
    }

    SECTION("Many callbacks registered") {
        std::vector<bool> callbackFlags(50, false);

        // Register many callbacks
        for (size_t i = 0; i < callbackFlags.size() && i < KEY_NUM_OF_KEYS; ++i) {
            KeyType key = static_cast<KeyType>(i);
            keyboard.setCallback(key, [&callbackFlags, i]() {
                callbackFlags[i] = true;
            });
        }

        // Trigger all callbacks
        for (size_t i = 0; i < callbackFlags.size() && i < KEY_NUM_OF_KEYS; ++i) {
            KeyType key = static_cast<KeyType>(i);
            keyboard.setKeyDown(key);
        }

        // Verify all callbacks were triggered
        for (size_t i = 0; i < callbackFlags.size() && i < KEY_NUM_OF_KEYS; ++i) {
            REQUIRE(callbackFlags[i] == true);
        }
    }
}

TEST_CASE("GLFW_Keyboard internal state management", "[window][glfw][keyboard][state_management]") {
    GLFWKeyboardTestFixture fixture;
    GLFW_Keyboard& keyboard = fixture.getKeyboard();

    SECTION("setKeyDown updates internal state only") {
        REQUIRE(keyboard.isKeyUp(KEY_A) == true);
        REQUIRE(keyboard.isKeyDown(KEY_A) == false);

        keyboard.setKeyDown(KEY_A);

        // State should be updated
        REQUIRE(keyboard.isKeyUp(KEY_A) == false);
        REQUIRE(keyboard.isKeyDown(KEY_A) == true);

        // Note: setKeyDown does NOT dispatch events - that's done by GLFW callbacks
    }

    SECTION("setKeyUp updates internal state only") {
        // First set key down
        keyboard.setKeyDown(KEY_B);
        REQUIRE(keyboard.isKeyDown(KEY_B) == true);

        // Then set key up
        keyboard.setKeyUp(KEY_B);

        // State should be updated
        REQUIRE(keyboard.isKeyUp(KEY_B) == true);
        REQUIRE(keyboard.isKeyDown(KEY_B) == false);

        // Note: setKeyUp does NOT dispatch events - that's done by GLFW callbacks
    }

    SECTION("Multiple state changes work correctly") {
        // Test sequence of state changes
        keyboard.setKeyDown(KEY_A);
        keyboard.setKeyDown(KEY_B);
        keyboard.setKeyUp(KEY_A);
        keyboard.setKeyDown(KEY_C);
        keyboard.setKeyUp(KEY_B);
        keyboard.setKeyUp(KEY_C);

        // Verify final states
        REQUIRE(keyboard.isKeyUp(KEY_A) == true);
        REQUIRE(keyboard.isKeyUp(KEY_B) == true);
        REQUIRE(keyboard.isKeyUp(KEY_C) == true);
    }

    SECTION("State consistency maintained") {
        // Test that state changes are consistent
        for (int i = 0; i < 10; ++i) {
            KeyType key = static_cast<KeyType>(i);

            // Initially up
            REQUIRE(keyboard.isKeyUp(key) == true);
            REQUIRE(keyboard.isKeyDown(key) == false);

            // Set down
            keyboard.setKeyDown(key);
            REQUIRE(keyboard.isKeyUp(key) == false);
            REQUIRE(keyboard.isKeyDown(key) == true);

            // Set up
            keyboard.setKeyUp(key);
            REQUIRE(keyboard.isKeyUp(key) == true);
            REQUIRE(keyboard.isKeyDown(key) == false);
        }
    }
}

TEST_CASE("GLFW_Keyboard callback system", "[window][glfw][keyboard][callbacks_only]") {
    GLFWKeyboardTestFixture fixture;
    GLFW_Keyboard& keyboard = fixture.getKeyboard();

    SECTION("Callback triggered on key press") {
        bool callbackTriggered = false;

        keyboard.setCallback(KEY_SPACE, [&callbackTriggered]() {
            callbackTriggered = true;
        });

        keyboard.setKeyDown(KEY_SPACE);

        // Callback should be triggered
        REQUIRE(callbackTriggered == true);

        // Note: Events are NOT dispatched by setKeyDown - only by GLFW callbacks
    }

    SECTION("Callback execution order with state changes") {
        std::vector<std::string> executionOrder;

        keyboard.setCallback(KEY_ENTER, [&executionOrder]() {
            executionOrder.push_back("callback");
        });

        keyboard.setKeyDown(KEY_ENTER);

        REQUIRE(executionOrder.size() == 1);
        REQUIRE(executionOrder[0] == "callback");
    }

    SECTION("Multiple callbacks work independently") {
        std::vector<KeyType> callbackKeys;

        keyboard.setCallback(KEY_A, [&callbackKeys]() {
            callbackKeys.push_back(KEY_A);
        });

        keyboard.setCallback(KEY_B, [&callbackKeys]() {
            callbackKeys.push_back(KEY_B);
        });

        keyboard.setCallback(KEY_C, [&callbackKeys]() {
            callbackKeys.push_back(KEY_C);
        });

        keyboard.setKeyDown(KEY_A);
        keyboard.setKeyDown(KEY_B);
        keyboard.setKeyDown(KEY_C);

        // All callbacks should be triggered
        REQUIRE(callbackKeys.size() == 3);
        REQUIRE(std::find(callbackKeys.begin(), callbackKeys.end(), KEY_A) != callbackKeys.end());
        REQUIRE(std::find(callbackKeys.begin(), callbackKeys.end(), KEY_B) != callbackKeys.end());
        REQUIRE(std::find(callbackKeys.begin(), callbackKeys.end(), KEY_C) != callbackKeys.end());
    }

    SECTION("Callback and state management integration") {
        int callbackCount = 0;

        keyboard.setCallback(KEY_D, [&callbackCount]() {
            callbackCount++;
        });

        // First press should trigger callback and change state
        keyboard.setKeyDown(KEY_D);
        REQUIRE(callbackCount == 1);
        REQUIRE(keyboard.isKeyDown(KEY_D) == true);

        // Release doesn't trigger callback but changes state
        keyboard.setKeyUp(KEY_D);
        REQUIRE(callbackCount == 1); // Still 1
        REQUIRE(keyboard.isKeyUp(KEY_D) == true);

        // Second press should trigger callback again
        keyboard.setKeyDown(KEY_D);
        REQUIRE(callbackCount == 2);
        REQUIRE(keyboard.isKeyDown(KEY_D) == true);
    }
}

TEST_CASE("GLFW_Keyboard real-world usage scenarios", "[window][glfw][keyboard][scenarios]") {
    GLFWKeyboardTestFixture fixture;
    GLFW_Keyboard& keyboard = fixture.getKeyboard();
    KeyboardEventTracker& eventTracker = fixture.getEventTracker();

    SECTION("Game input simulation") {
        // Simulate typical game input patterns
        bool wPressed = false, aPressed = false, sPressed = false, dPressed = false;
        bool spacePressed = false;

        keyboard.setCallback(KEY_W, [&wPressed]() { wPressed = true; });
        keyboard.setCallback(KEY_A, [&aPressed]() { aPressed = true; });
        keyboard.setCallback(KEY_S, [&sPressed]() { sPressed = true; });
        keyboard.setCallback(KEY_D, [&dPressed]() { dPressed = true; });
        keyboard.setCallback(KEY_SPACE, [&spacePressed]() { spacePressed = true; });

        // Simulate movement input
        keyboard.setKeyDown(KEY_W); // Move forward
        keyboard.setKeyDown(KEY_D); // Move right

        REQUIRE(wPressed == true);
        REQUIRE(dPressed == true);
        REQUIRE(keyboard.isKeyDown(KEY_W) == true);
        REQUIRE(keyboard.isKeyDown(KEY_D) == true);

        // Simulate jump while moving
        keyboard.setKeyDown(KEY_SPACE);
        REQUIRE(spacePressed == true);
        REQUIRE(keyboard.isKeyDown(KEY_SPACE) == true);

        // Release movement keys
        keyboard.setKeyUp(KEY_W);
        keyboard.setKeyUp(KEY_D);
        keyboard.setKeyUp(KEY_SPACE);

        REQUIRE(keyboard.isKeyUp(KEY_W) == true);
        REQUIRE(keyboard.isKeyUp(KEY_D) == true);
        REQUIRE(keyboard.isKeyUp(KEY_SPACE) == true);
    }

    SECTION("Text input key state simulation") {
        // Simulate typing "hello" - focus on state management
        keyboard.setKeyDown(KEY_H);
        REQUIRE(keyboard.isKeyDown(KEY_H) == true);
        keyboard.setKeyUp(KEY_H);
        REQUIRE(keyboard.isKeyUp(KEY_H) == true);

        keyboard.setKeyDown(KEY_E);
        REQUIRE(keyboard.isKeyDown(KEY_E) == true);
        keyboard.setKeyUp(KEY_E);
        REQUIRE(keyboard.isKeyUp(KEY_E) == true);

        keyboard.setKeyDown(KEY_L);
        REQUIRE(keyboard.isKeyDown(KEY_L) == true);
        keyboard.setKeyUp(KEY_L);
        REQUIRE(keyboard.isKeyUp(KEY_L) == true);

        keyboard.setKeyDown(KEY_L);
        REQUIRE(keyboard.isKeyDown(KEY_L) == true);
        keyboard.setKeyUp(KEY_L);
        REQUIRE(keyboard.isKeyUp(KEY_L) == true);

        keyboard.setKeyDown(KEY_O);
        REQUIRE(keyboard.isKeyDown(KEY_O) == true);
        keyboard.setKeyUp(KEY_O);
        REQUIRE(keyboard.isKeyUp(KEY_O) == true);

        // All keys should be up after typing sequence
        REQUIRE(keyboard.isKeyUp(KEY_H) == true);
        REQUIRE(keyboard.isKeyUp(KEY_E) == true);
        REQUIRE(keyboard.isKeyUp(KEY_L) == true);
        REQUIRE(keyboard.isKeyUp(KEY_O) == true);
    }

    SECTION("Modifier key combinations") {
        // Simulate Shift+C (using available keys) - focus on state management
        keyboard.setKeyDown(KEY_ESCAPE); // Use ESCAPE as modifier key
        keyboard.setKeyDown(KEY_C);

        REQUIRE(keyboard.isKeyDown(KEY_ESCAPE) == true);
        REQUIRE(keyboard.isKeyDown(KEY_C) == true);

        keyboard.setKeyUp(KEY_C);
        keyboard.setKeyUp(KEY_ESCAPE);

        REQUIRE(keyboard.isKeyUp(KEY_ESCAPE) == true);
        REQUIRE(keyboard.isKeyUp(KEY_C) == true);

        // Verify both keys are properly released
        REQUIRE(keyboard.isKeyUp(KEY_ESCAPE) == true);
        REQUIRE(keyboard.isKeyUp(KEY_C) == true);
    }
}
