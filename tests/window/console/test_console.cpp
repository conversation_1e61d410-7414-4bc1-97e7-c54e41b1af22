#include <catch2/catch_test_macros.hpp>

#include "window/console/console.hpp"
#include "events/event_dispatcher.hpp"
#include "events/events.hpp"
#include "services/service_locator.hpp"
#include "window/keys.hpp"
#include "window/keyboard_events.hpp"
#include "test_utils.hpp"

#include <memory>
#include <string>
#include <vector>

using namespace IronFrost;

// Event tracking utility for testing
class EventTracker {
private:
    std::vector<std::string> m_dispatchedEvents;

public:
    void trackEvent(const std::string& eventType) {
        m_dispatchedEvents.push_back(eventType);
    }

    const std::vector<std::string>& getDispatchedEvents() const {
        return m_dispatchedEvents;
    }

    void clearDispatchedEvents() {
        m_dispatchedEvents.clear();
    }

    bool wasEventDispatched(const std::string& eventType) const {
        return std::find(m_dispatchedEvents.begin(), m_dispatchedEvents.end(), eventType) != m_dispatchedEvents.end();
    }
};

// Test fixture for Console tests
class ConsoleTestFixture {
private:
    std::unique_ptr<Console> m_console;
    std::unique_ptr<EventDispatcher> m_eventDispatcher;
    EventTracker m_eventTracker;

public:
    ConsoleTestFixture() {
        // Create event dispatcher
        m_eventDispatcher = std::make_unique<EventDispatcher>();

        // Register event tracking listeners
        m_eventDispatcher->registerListener<ConsoleOpenEvent>(
            [this](const ConsoleOpenEvent& event) {
                m_eventTracker.trackEvent(event.toString());
            });

        m_eventDispatcher->registerListener<ConsoleCloseEvent>(
            [this](const ConsoleCloseEvent& event) {
                m_eventTracker.trackEvent(event.toString());
            });

        m_eventDispatcher->registerListener<ConsoleInputBufferEvent>(
            [this](const ConsoleInputBufferEvent& event) {
                m_eventTracker.trackEvent(event.toString());
            });

        m_eventDispatcher->registerListener<ConsoleCommandEvent>(
            [this](const ConsoleCommandEvent& event) {
                m_eventTracker.trackEvent(event.toString());
            });

        // Register it with ServiceLocator
        ServiceLocator::registerService<EventDispatcher>(std::move(m_eventDispatcher));

        // Create console (this will register listeners)
        m_console = std::make_unique<Console>();
    }

    ~ConsoleTestFixture() {
        // Clean up console first
        m_console.reset();
        // Note: ServiceLocator cleanup is handled by static destruction
    }

    Console& getConsole() { return *m_console; }
    EventTracker& getEventTracker() {
        return m_eventTracker;
    }

    EventDispatcher& getEventDispatcher() {
        return ServiceLocator::getService<EventDispatcher>();
    }
};

TEST_CASE("Console construction and initial state", "[window][console][construction]") {
    ConsoleTestFixture fixture;
    Console& console = fixture.getConsole();
    
    SECTION("Console starts closed") {
        REQUIRE(console.isOpen() == false);
    }
    
    SECTION("Input buffer starts empty") {
        REQUIRE(console.getInputBuffer().empty());
    }
    
    SECTION("Output log starts empty") {
        REQUIRE(console.getOutputLog().empty());
    }
}

TEST_CASE("Console toggle functionality", "[window][console][toggle]") {
    ConsoleTestFixture fixture;
    Console& console = fixture.getConsole();
    EventTracker& eventTracker = fixture.getEventTracker();

    SECTION("Toggle opens console") {
        console.toggle();

        REQUIRE(console.isOpen() == true);
        REQUIRE(eventTracker.wasEventDispatched("ConsoleOpenEvent"));
    }

    SECTION("Toggle twice returns to closed state") {
        console.toggle(); // Open
        eventTracker.clearDispatchedEvents();

        console.toggle(); // Close

        REQUIRE(console.isOpen() == false);
        REQUIRE(eventTracker.wasEventDispatched("ConsoleCloseEvent"));
    }
    
    SECTION("Multiple toggles work correctly") {
        // Start closed
        REQUIRE(console.isOpen() == false);
        
        // Open
        console.toggle();
        REQUIRE(console.isOpen() == true);
        
        // Close
        console.toggle();
        REQUIRE(console.isOpen() == false);
        
        // Open again
        console.toggle();
        REQUIRE(console.isOpen() == true);
    }
}

TEST_CASE("Console input handling when closed", "[window][console][input][closed]") {
    ConsoleTestFixture fixture;
    Console& console = fixture.getConsole();
    EventDispatcher& eventDispatcher = fixture.getEventDispatcher();
    EventTracker& eventTracker = fixture.getEventTracker();

    // Ensure console is closed
    REQUIRE(console.isOpen() == false);

    SECTION("KeyInputEvent ignored when console closed") {
        eventDispatcher.dispatch<KeyInputEvent>('a');

        REQUIRE(console.getInputBuffer().empty());
        REQUIRE_FALSE(eventTracker.wasEventDispatched("ConsoleInputBufferEvent"));
    }

    SECTION("KeyDownEvent ignored when console closed") {
        eventDispatcher.dispatch<KeyDownEvent>(KEY_ENTER);

        REQUIRE(console.getInputBuffer().empty());
        REQUIRE_FALSE(eventTracker.wasEventDispatched("ConsoleInputBufferEvent"));
        REQUIRE_FALSE(eventTracker.wasEventDispatched("ConsoleCommandEvent"));
    }
}

TEST_CASE("Console input handling when open", "[window][console][input][open]") {
    ConsoleTestFixture fixture;
    Console& console = fixture.getConsole();
    EventDispatcher& eventDispatcher = fixture.getEventDispatcher();
    EventTracker& eventTracker = fixture.getEventTracker();

    // Open console
    console.toggle();
    eventTracker.clearDispatchedEvents();

    SECTION("KeyInputEvent adds character to buffer") {
        eventDispatcher.dispatch<KeyInputEvent>('h');

        REQUIRE(console.getInputBuffer() == "h");
        REQUIRE(eventTracker.wasEventDispatched("ConsoleInputBufferEvent"));
    }

    SECTION("Multiple KeyInputEvents build up buffer") {
        eventDispatcher.dispatch<KeyInputEvent>('h');
        eventDispatcher.dispatch<KeyInputEvent>('e');
        eventDispatcher.dispatch<KeyInputEvent>('l');
        eventDispatcher.dispatch<KeyInputEvent>('l');
        eventDispatcher.dispatch<KeyInputEvent>('o');

        REQUIRE(console.getInputBuffer() == "hello");
    }

    SECTION("Special characters handled correctly") {
        eventDispatcher.dispatch<KeyInputEvent>(' ');
        eventDispatcher.dispatch<KeyInputEvent>('!');
        eventDispatcher.dispatch<KeyInputEvent>('@');
        eventDispatcher.dispatch<KeyInputEvent>('#');

        REQUIRE(console.getInputBuffer() == " !@#");
    }
}

TEST_CASE("Console backspace handling", "[window][console][backspace]") {
    ConsoleTestFixture fixture;
    Console& console = fixture.getConsole();
    EventDispatcher& eventDispatcher = fixture.getEventDispatcher();
    EventTracker& eventTracker = fixture.getEventTracker();

    // Open console
    console.toggle();
    eventTracker.clearDispatchedEvents();
    
    SECTION("Backspace removes last character") {
        // Add some text
        eventDispatcher.dispatch<KeyInputEvent>('h');
        eventDispatcher.dispatch<KeyInputEvent>('i');
        REQUIRE(console.getInputBuffer() == "hi");
        
        // Backspace
        eventDispatcher.dispatch<KeyDownEvent>(KEY_BACKSPACE);
        REQUIRE(console.getInputBuffer() == "h");
    }
    
    SECTION("Multiple backspaces work correctly") {
        // Add text
        eventDispatcher.dispatch<KeyInputEvent>('t');
        eventDispatcher.dispatch<KeyInputEvent>('e');
        eventDispatcher.dispatch<KeyInputEvent>('s');
        eventDispatcher.dispatch<KeyInputEvent>('t');
        REQUIRE(console.getInputBuffer() == "test");
        
        // Multiple backspaces
        eventDispatcher.dispatch<KeyDownEvent>(KEY_BACKSPACE);
        eventDispatcher.dispatch<KeyDownEvent>(KEY_BACKSPACE);
        REQUIRE(console.getInputBuffer() == "te");
    }
    
    SECTION("Backspace on empty buffer does nothing") {
        REQUIRE(console.getInputBuffer().empty());
        
        eventDispatcher.dispatch<KeyDownEvent>(KEY_BACKSPACE);
        REQUIRE(console.getInputBuffer().empty());
    }
    
    SECTION("Backspace dispatches ConsoleInputBufferEvent") {
        eventDispatcher.dispatch<KeyInputEvent>('a');
        eventTracker.clearDispatchedEvents();

        eventDispatcher.dispatch<KeyDownEvent>(KEY_BACKSPACE);
        REQUIRE(eventTracker.wasEventDispatched("ConsoleInputBufferEvent"));
    }
}

TEST_CASE("Console command processing", "[window][console][commands]") {
    ConsoleTestFixture fixture;
    Console& console = fixture.getConsole();
    EventDispatcher& eventDispatcher = fixture.getEventDispatcher();
    EventTracker& eventTracker = fixture.getEventTracker();

    // Open console
    console.toggle();
    eventTracker.clearDispatchedEvents();
    
    SECTION("Enter key processes command") {
        // Type a command
        eventDispatcher.dispatch<KeyInputEvent>('h');
        eventDispatcher.dispatch<KeyInputEvent>('e');
        eventDispatcher.dispatch<KeyInputEvent>('l');
        eventDispatcher.dispatch<KeyInputEvent>('p');
        REQUIRE(console.getInputBuffer() == "help");
        
        eventTracker.clearDispatchedEvents();

        // Press enter
        eventDispatcher.dispatch<KeyDownEvent>(KEY_ENTER);

        // Command should be dispatched and buffer cleared
        REQUIRE(eventTracker.wasEventDispatched("ConsoleCommandEvent"));
        REQUIRE(console.getInputBuffer().empty());
    }

    SECTION("Empty command can be processed") {
        REQUIRE(console.getInputBuffer().empty());

        eventDispatcher.dispatch<KeyDownEvent>(KEY_ENTER);

        REQUIRE(eventTracker.wasEventDispatched("ConsoleCommandEvent"));
        REQUIRE(console.getInputBuffer().empty());
    }
    
    SECTION("Command is added to history") {
        // Type and execute first command
        eventDispatcher.dispatch<KeyInputEvent>('c');
        eventDispatcher.dispatch<KeyInputEvent>('m');
        eventDispatcher.dispatch<KeyInputEvent>('d');
        eventDispatcher.dispatch<KeyInputEvent>('1');
        eventDispatcher.dispatch<KeyDownEvent>(KEY_ENTER);
        
        // Type and execute second command
        eventDispatcher.dispatch<KeyInputEvent>('c');
        eventDispatcher.dispatch<KeyInputEvent>('m');
        eventDispatcher.dispatch<KeyInputEvent>('d');
        eventDispatcher.dispatch<KeyInputEvent>('2');
        eventDispatcher.dispatch<KeyDownEvent>(KEY_ENTER);
        
        // Note: We can't directly access command history as it's private,
        // but we can verify the behavior through the public interface
        REQUIRE(console.getInputBuffer().empty());
    }
}

TEST_CASE("Console log handling", "[window][console][logging]") {
    ConsoleTestFixture fixture;
    Console& console = fixture.getConsole();
    EventDispatcher& eventDispatcher = fixture.getEventDispatcher();
    
    SECTION("ConsoleLogEvent adds to output log") {
        REQUIRE(console.getOutputLog().empty());
        
        eventDispatcher.dispatch<ConsoleLogEvent>("Test log message");
        
        REQUIRE(console.getOutputLog().size() == 1);
        REQUIRE(console.getOutputLog()[0] == "Test log message");
    }
    
    SECTION("Multiple log events accumulate") {
        eventDispatcher.dispatch<ConsoleLogEvent>("First message");
        eventDispatcher.dispatch<ConsoleLogEvent>("Second message");
        eventDispatcher.dispatch<ConsoleLogEvent>("Third message");
        
        const auto& log = console.getOutputLog();
        REQUIRE(log.size() == 3);
        REQUIRE(log[0] == "First message");
        REQUIRE(log[1] == "Second message");
        REQUIRE(log[2] == "Third message");
    }
    
    SECTION("Empty log messages handled") {
        eventDispatcher.dispatch<ConsoleLogEvent>("");
        
        REQUIRE(console.getOutputLog().size() == 1);
        REQUIRE(console.getOutputLog()[0] == "");
    }
    
    SECTION("Long log messages handled") {
        std::string longMessage(1000, 'x');
        eventDispatcher.dispatch<ConsoleLogEvent>(longMessage);
        
        REQUIRE(console.getOutputLog().size() == 1);
        REQUIRE(console.getOutputLog()[0] == longMessage);
    }
}

TEST_CASE("Console processInput method", "[window][console][process_input]") {
    ConsoleTestFixture fixture;
    Console& console = fixture.getConsole();
    EventTracker& eventTracker = fixture.getEventTracker();

    SECTION("processInput dispatches ConsoleCommandEvent") {
        console.processInput("test command");

        REQUIRE(eventTracker.wasEventDispatched("ConsoleCommandEvent"));
    }

    SECTION("processInput with empty string") {
        console.processInput("");

        REQUIRE(eventTracker.wasEventDispatched("ConsoleCommandEvent"));
    }

    SECTION("processInput with special characters") {
        console.processInput("command with spaces and !@#$%");

        REQUIRE(eventTracker.wasEventDispatched("ConsoleCommandEvent"));
    }

    SECTION("processInput doesn't affect console state") {
        bool initialState = console.isOpen();
        std::string initialBuffer = console.getInputBuffer();

        console.processInput("some command");

        REQUIRE(console.isOpen() == initialState);
        REQUIRE(console.getInputBuffer() == initialBuffer);
    }
}

TEST_CASE("Console integration scenarios", "[window][console][integration]") {
    ConsoleTestFixture fixture;
    Console& console = fixture.getConsole();
    EventDispatcher& eventDispatcher = fixture.getEventDispatcher();
    EventTracker& eventTracker = fixture.getEventTracker();

    SECTION("Complete command workflow") {
        // Open console
        console.toggle();
        REQUIRE(console.isOpen() == true);

        eventTracker.clearDispatchedEvents();

        // Type a command
        eventDispatcher.dispatch<KeyInputEvent>('h');
        eventDispatcher.dispatch<KeyInputEvent>('e');
        eventDispatcher.dispatch<KeyInputEvent>('l');
        eventDispatcher.dispatch<KeyInputEvent>('p');

        // Verify buffer state
        REQUIRE(console.getInputBuffer() == "help");

        // Execute command
        eventDispatcher.dispatch<KeyDownEvent>(KEY_ENTER);

        // Verify command was processed
        REQUIRE(eventTracker.wasEventDispatched("ConsoleCommandEvent"));
        REQUIRE(console.getInputBuffer().empty());

        // Close console
        console.toggle();
        REQUIRE(console.isOpen() == false);
    }

    SECTION("Command with backspace corrections") {
        console.toggle();
        eventTracker.clearDispatchedEvents();

        // Type with mistakes and corrections
        eventDispatcher.dispatch<KeyInputEvent>('h');
        eventDispatcher.dispatch<KeyInputEvent>('e');
        eventDispatcher.dispatch<KeyInputEvent>('l');
        eventDispatcher.dispatch<KeyInputEvent>('x'); // mistake
        eventDispatcher.dispatch<KeyDownEvent>(KEY_BACKSPACE); // correct
        eventDispatcher.dispatch<KeyInputEvent>('p');

        REQUIRE(console.getInputBuffer() == "help");

        // Execute
        eventDispatcher.dispatch<KeyDownEvent>(KEY_ENTER);
        REQUIRE(console.getInputBuffer().empty());
    }

    SECTION("Multiple commands in sequence") {
        console.toggle();
        eventTracker.clearDispatchedEvents();

        // First command
        eventDispatcher.dispatch<KeyInputEvent>('c');
        eventDispatcher.dispatch<KeyInputEvent>('m');
        eventDispatcher.dispatch<KeyInputEvent>('d');
        eventDispatcher.dispatch<KeyInputEvent>('1');
        eventDispatcher.dispatch<KeyDownEvent>(KEY_ENTER);

        // Second command
        eventDispatcher.dispatch<KeyInputEvent>('c');
        eventDispatcher.dispatch<KeyInputEvent>('m');
        eventDispatcher.dispatch<KeyInputEvent>('d');
        eventDispatcher.dispatch<KeyInputEvent>('2');
        eventDispatcher.dispatch<KeyDownEvent>(KEY_ENTER);

        // Both should have been processed
        REQUIRE(console.getInputBuffer().empty());
    }

    SECTION("Logging while console is open") {
        console.toggle();

        // Add some log messages
        eventDispatcher.dispatch<ConsoleLogEvent>("System started");
        eventDispatcher.dispatch<ConsoleLogEvent>("Loading assets...");
        eventDispatcher.dispatch<ConsoleLogEvent>("Ready");

        const auto& log = console.getOutputLog();
        REQUIRE(log.size() == 3);
        REQUIRE(log[0] == "System started");
        REQUIRE(log[1] == "Loading assets...");
        REQUIRE(log[2] == "Ready");
    }
}

TEST_CASE("Console edge cases and robustness", "[window][console][edge_cases]") {
    ConsoleTestFixture fixture;
    Console& console = fixture.getConsole();
    EventDispatcher& eventDispatcher = fixture.getEventDispatcher();

    SECTION("Very long input buffer") {
        console.toggle();
        // No need to clear events for this test

        // Type a very long command
        std::string longCommand(1000, 'a');
        for (char c : longCommand) {
            eventDispatcher.dispatch<KeyInputEvent>(c);
        }

        REQUIRE(console.getInputBuffer() == longCommand);

        // Should still be able to execute
        eventDispatcher.dispatch<KeyDownEvent>(KEY_ENTER);
        REQUIRE(console.getInputBuffer().empty());
    }

    SECTION("Rapid key events") {
        console.toggle();
        // No need to clear events for this test

        // Simulate rapid typing
        for (int i = 0; i < 100; ++i) {
            eventDispatcher.dispatch<KeyInputEvent>('a' + (i % 26));
        }

        REQUIRE(console.getInputBuffer().length() == 100);

        // Clear with backspaces
        for (int i = 0; i < 100; ++i) {
            eventDispatcher.dispatch<KeyDownEvent>(KEY_BACKSPACE);
        }

        REQUIRE(console.getInputBuffer().empty());
    }

    SECTION("Unicode and special characters") {
        console.toggle();
        // No need to clear events for this test

        // Test various character ranges
        eventDispatcher.dispatch<KeyInputEvent>(0x20); // Space
        eventDispatcher.dispatch<KeyInputEvent>(0x7E); // Tilde
        eventDispatcher.dispatch<KeyInputEvent>(0x21); // Exclamation
        eventDispatcher.dispatch<KeyInputEvent>(0x40); // At symbol

        REQUIRE(console.getInputBuffer().length() == 4);
    }

    SECTION("Null character handling") {
        console.toggle();
        // No need to clear events for this test

        eventDispatcher.dispatch<KeyInputEvent>(0); // Null character
        eventDispatcher.dispatch<KeyInputEvent>('a');

        // Should handle null character gracefully
        REQUIRE(console.getInputBuffer().length() == 2);
    }

    SECTION("Many log entries") {
        // Add many log entries
        for (int i = 0; i < 1000; ++i) {
            eventDispatcher.dispatch<ConsoleLogEvent>("Log entry " + std::to_string(i));
        }

        const auto& log = console.getOutputLog();
        REQUIRE(log.size() == 1000);
        REQUIRE(log[0] == "Log entry 0");
        REQUIRE(log[999] == "Log entry 999");
    }

    SECTION("Toggle during input") {
        console.toggle(); // Open

        // Start typing
        eventDispatcher.dispatch<KeyInputEvent>('h');
        eventDispatcher.dispatch<KeyInputEvent>('e');
        REQUIRE(console.getInputBuffer() == "he");

        // Close console
        console.toggle();
        REQUIRE(console.isOpen() == false);

        // Input should be preserved
        REQUIRE(console.getInputBuffer() == "he");

        // Reopen and continue
        console.toggle();
        eventDispatcher.dispatch<KeyInputEvent>('l');
        eventDispatcher.dispatch<KeyInputEvent>('p');
        REQUIRE(console.getInputBuffer() == "help");
    }
}

TEST_CASE("Console event handling edge cases", "[window][console][events]") {
    ConsoleTestFixture fixture;
    Console& console = fixture.getConsole();
    EventDispatcher& eventDispatcher = fixture.getEventDispatcher();

    SECTION("Unknown key codes ignored gracefully") {
        console.toggle();
        // No need to clear events for this test

        // Send unknown key down event
        eventDispatcher.dispatch<KeyDownEvent>(-1);
        eventDispatcher.dispatch<KeyDownEvent>(999);

        // Should not crash or affect buffer
        REQUIRE(console.getInputBuffer().empty());
    }

    SECTION("Rapid toggle operations") {
        for (int i = 0; i < 100; ++i) {
            console.toggle();
        }

        // Should end up closed (started closed, 100 toggles = even number)
        REQUIRE(console.isOpen() == false);
    }

    SECTION("Event handling with no ServiceLocator") {
        // This test verifies graceful handling if ServiceLocator is not properly set up
        // Note: In our test fixture, ServiceLocator is properly set up, so this tests
        // the normal case. In a real scenario, missing ServiceLocator would cause issues.

        console.toggle();
        REQUIRE(console.isOpen() == true);
    }
}
