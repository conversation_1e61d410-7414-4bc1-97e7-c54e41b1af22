# IronFrost CMake Configuration File

@PACKAGE_INIT@

# Check if the required components are available
set(IronFrost_FOUND TRUE)

# Find required dependencies
include(CMakeFindDependencyMacro)

# Core dependencies
find_dependency(glfw3 REQUIRED)
find_dependency(glad REQUIRED)
find_dependency(glm REQUIRED)
find_dependency(assimp REQUIRED)
find_dependency(PhysFS REQUIRED)
find_dependency(Lua REQUIRED)
find_dependency(sol2 REQUIRED)
find_dependency(nlohmann_json REQUIRED)
find_dependency(Stb REQUIRED)
find_dependency(Freetype REQUIRED)

# Optional dependencies for testing
if(@BUILD_TESTS@)
    find_dependency(Catch2 3)
endif()

# Include the targets file
include("${CMAKE_CURRENT_LIST_DIR}/IronFrostTargets.cmake")

# Define the main target alias
if(TARGET IronFrost::IronFrost)
    # Main executable target already exists
else()
    add_executable(IronFrost::IronFrost IMPORTED)
    set_target_properties(IronFrost::IronFrost PROPERTIES
        IMPORTED_LOCATION "${PACKAGE_PREFIX_DIR}/@CMAKE_INSTALL_BINDIR@/IronFrost@CMAKE_EXECUTABLE_SUFFIX@"
    )
endif()

# Define library aliases for easier usage
if(NOT TARGET IronFrost::Window)
    add_library(IronFrost::Window ALIAS Window)
endif()

if(NOT TARGET IronFrost::VFS)
    add_library(IronFrost::VFS ALIAS VFS)
endif()

if(NOT TARGET IronFrost::Utils)
    add_library(IronFrost::Utils ALIAS Utils)
endif()

if(NOT TARGET IronFrost::Assets)
    add_library(IronFrost::Assets ALIAS Assets)
endif()

if(NOT TARGET IronFrost::Renderer)
    add_library(IronFrost::Renderer ALIAS Renderer)
endif()

if(NOT TARGET IronFrost::GUI)
    add_library(IronFrost::GUI ALIAS GUI)
endif()

if(NOT TARGET IronFrost::Scripts)
    add_library(IronFrost::Scripts ALIAS Scripts)
endif()

if(NOT TARGET IronFrost::Scene)
    add_library(IronFrost::Scene ALIAS Scene)
endif()

# Set useful variables
set(IronFrost_VERSION @PROJECT_VERSION@)
set(IronFrost_VERSION_MAJOR @PROJECT_VERSION_MAJOR@)
set(IronFrost_VERSION_MINOR @PROJECT_VERSION_MINOR@)
set(IronFrost_VERSION_PATCH @PROJECT_VERSION_PATCH@)

# Set paths
set(IronFrost_INCLUDE_DIRS "${PACKAGE_PREFIX_DIR}/@CMAKE_INSTALL_INCLUDEDIR@/IronFrost")
set(IronFrost_DATA_DIR "${PACKAGE_PREFIX_DIR}/@CMAKE_INSTALL_DATADIR@/IronFrost")
set(IronFrost_DOC_DIR "${PACKAGE_PREFIX_DIR}/@CMAKE_INSTALL_DOCDIR@")

# Component check
set(_IronFrost_supported_components Window VFS Utils Assets Renderer GUI Scripts Scene)

foreach(_comp ${IronFrost_FIND_COMPONENTS})
    if(NOT _comp IN_LIST _IronFrost_supported_components)
        set(IronFrost_FOUND FALSE)
        set(IronFrost_NOT_FOUND_MESSAGE "Unsupported component: ${_comp}")
        break()
    endif()
    
    if(NOT TARGET IronFrost::${_comp})
        set(IronFrost_FOUND FALSE)
        set(IronFrost_NOT_FOUND_MESSAGE "Component not found: ${_comp}")
        break()
    endif()
    
    set(IronFrost_${_comp}_FOUND TRUE)
endforeach()

# Print status message
if(IronFrost_FOUND)
    if(NOT IronFrost_FIND_QUIETLY)
        message(STATUS "Found IronFrost: ${IronFrost_VERSION}")
        if(IronFrost_FIND_COMPONENTS)
            message(STATUS "  Components: ${IronFrost_FIND_COMPONENTS}")
        endif()
    endif()
endif()

check_required_components(IronFrost)
