#!/bin/bash
# run-static-analysis.sh - Run clang-tidy static analysis locally

set -e  # Exit on any error

echo "🔍 Running Static Analysis (clang-tidy) locally..."
echo "=================================================="

# Check if clang-tidy is installed
if ! command -v clang-tidy &> /dev/null; then
    echo "❌ clang-tidy is not installed!"
    echo ""
    echo "Install instructions:"
    echo "  macOS:   brew install llvm"
    echo "  Ubuntu:  sudo apt-get install clang-tidy"
    echo "  Windows: Install LLVM from https://llvm.org/builds/"
    exit 1
fi

# Check clang-tidy version
CLANG_TIDY_VERSION=$(clang-tidy --version | head -n1)
echo "✅ Found: $CLANG_TIDY_VERSION"

# Detect which clang-tidy we're using and set up proper include paths
if command -v /opt/homebrew/opt/llvm/bin/clang-tidy &> /dev/null; then
    CLANG_TIDY="/opt/homebrew/opt/llvm/bin/clang-tidy"
    CLANG_CXX="/opt/homebrew/opt/llvm/bin/clang++"
    echo "✅ Using Homebrew LLVM toolchain"
elif command -v /usr/local/bin/clang-tidy &> /dev/null; then
    CLANG_TIDY="/usr/local/bin/clang-tidy"
    CLANG_CXX="/usr/local/bin/clang++"
    echo "✅ Using Homebrew LLVM toolchain"
else
    CLANG_TIDY="clang-tidy"
    CLANG_CXX="clang++"
    echo "✅ Using system clang toolchain"
fi

# Get standard library include paths from the clang++ we're using
echo "🔍 Detecting standard library paths..."
STD_INCLUDE_PATHS=$($CLANG_CXX -v -E -x c++ - < /dev/null 2>&1 | sed -n '/#include <...> search starts here:/,/End of search list./p' | grep '^ ' | sed 's/^ //')

# Convert to clang-tidy extra-arg format
STD_INCLUDES=""
for path in $STD_INCLUDE_PATHS; do
    STD_INCLUDES="$STD_INCLUDES --extra-arg=-isystem --extra-arg=$path"
done

echo "✅ Standard library paths detected"

# Check if .clang-tidy config exists
if [ ! -f ".clang-tidy" ]; then
    echo "❌ .clang-tidy configuration file not found!"
    exit 1
fi

echo "✅ Found .clang-tidy configuration"

# Check if build directory exists
if [ ! -d "build" ]; then
    echo "❌ Build directory not found!"
    echo "Please run cmake configuration first:"
    echo "  cmake --preset Debug"
    exit 1
fi

# Find the build directory (Debug or Release)
BUILD_DIR=""
if [ -d "build/Debug" ]; then
    BUILD_DIR="build/Debug"
elif [ -d "build/Release" ]; then
    BUILD_DIR="build/Release"
else
    echo "❌ No configured build directory found!"
    echo "Please run cmake configuration first:"
    echo "  cmake --preset Debug"
    exit 1
fi

echo "✅ Using build directory: $BUILD_DIR"

# Check if compile_commands.json exists
if [ ! -f "$BUILD_DIR/compile_commands.json" ]; then
    echo "❌ compile_commands.json not found in $BUILD_DIR"
    echo "Make sure CMAKE_EXPORT_COMPILE_COMMANDS is enabled"
    echo "Trying to regenerate build with compile commands..."

    # Determine which preset was used
    if [ "$BUILD_DIR" = "build/Debug" ]; then
        PRESET="Debug"
    else
        PRESET="Release"
    fi

    echo "Reconfiguring with preset: $PRESET"
    cmake --preset "$PRESET" -DCMAKE_EXPORT_COMPILE_COMMANDS=ON

    if [ ! -f "$BUILD_DIR/compile_commands.json" ]; then
        echo "❌ Failed to generate compile_commands.json"
        echo "Please run: cmake --preset $PRESET -DCMAKE_EXPORT_COMPILE_COMMANDS=ON"
        exit 1
    fi
fi

echo "✅ Found compile_commands.json"

# Create output directory for results
mkdir -p "static-analysis-results"

echo ""
echo "🚀 Running clang-tidy analysis..."
echo "This may take a few minutes..."

# Find all C++ source files
CPP_FILES=$(find src -name "*.cpp" -o -name "*.cc" -o -name "*.cxx")
HEADER_FILES=$(find src -name "*.hpp" -o -name "*.h")

# Count files
CPP_COUNT=$(echo "$CPP_FILES" | wc -l)
HEADER_COUNT=$(echo "$HEADER_FILES" | wc -l)

echo "📊 Analyzing $CPP_COUNT source files and $HEADER_COUNT header files..."

# Run clang-tidy on source files
echo ""
echo "🔍 Analyzing source files..."
TIDY_OUTPUT="static-analysis-results/clang-tidy-output.txt"
TIDY_ERRORS="static-analysis-results/clang-tidy-errors.txt"
TIDY_COMBINED="static-analysis-results/clang-tidy-combined.txt"

# Clear previous results
> "$TIDY_OUTPUT"
> "$TIDY_ERRORS"
> "$TIDY_COMBINED"

# Run clang-tidy on each file individually to avoid crashes
TOTAL_FILES=$(echo "$CPP_FILES" | wc -l | tr -d ' ')
CURRENT_FILE=0
FAILED_FILES=0

echo "$CPP_FILES" | while read -r file; do
    if [ -n "$file" ]; then
        CURRENT_FILE=$((CURRENT_FILE + 1))
        echo "[$CURRENT_FILE/$TOTAL_FILES] Processing $file..."

        # Run clang-tidy on individual file with proper include paths
        if $CLANG_TIDY -p "$BUILD_DIR" $STD_INCLUDES "$file" >> "$TIDY_OUTPUT" 2>> "$TIDY_ERRORS"; then
            echo "  ✅ Completed"
        else
            echo "  ❌ Failed or had issues"
            FAILED_FILES=$((FAILED_FILES + 1))
        fi
    fi
done

if [ "$FAILED_FILES" -eq 0 ]; then
    echo "✅ Static analysis completed successfully!"
    ANALYSIS_SUCCESS=true
else
    echo "⚠️  Static analysis completed with $FAILED_FILES failed files"
    ANALYSIS_SUCCESS=false
fi

# Combine output and errors for analysis
cat "$TIDY_OUTPUT" "$TIDY_ERRORS" > "$TIDY_COMBINED" 2>/dev/null

# Show summary
echo ""
echo "📋 Analysis Summary:"
echo "===================="

# Debug: Show file sizes
echo "Debug info:"
echo "  Output file size: $(wc -l < "$TIDY_OUTPUT" 2>/dev/null || echo "0") lines"
echo "  Error file size: $(wc -l < "$TIDY_ERRORS" 2>/dev/null || echo "0") lines"
echo "  Combined file size: $(wc -l < "$TIDY_COMBINED" 2>/dev/null || echo "0") lines"

# Count warnings and errors from combined output
WARNING_COUNT=$(grep "warning:" "$TIDY_COMBINED" 2>/dev/null | wc -l | tr -d ' ')
ERROR_COUNT=$(grep "error:" "$TIDY_COMBINED" 2>/dev/null | wc -l | tr -d ' ')
CONFIG_ERROR_COUNT=$(grep "Error parsing.*clang-tidy" "$TIDY_ERRORS" 2>/dev/null | wc -l | tr -d ' ')

# Ensure counts are numbers
WARNING_COUNT=${WARNING_COUNT:-0}
ERROR_COUNT=${ERROR_COUNT:-0}
CONFIG_ERROR_COUNT=${CONFIG_ERROR_COUNT:-0}

echo "Code Analysis Warnings: $WARNING_COUNT"
echo "Code Analysis Errors: $ERROR_COUNT"
echo "Configuration Errors: $CONFIG_ERROR_COUNT"

# Show configuration errors first if any
if [ "$CONFIG_ERROR_COUNT" -gt 0 ]; then
    echo ""
    echo "❌ Configuration Errors Found:"
    echo "=============================="
    grep "Error parsing.*clang-tidy" "$TIDY_ERRORS" || true
    echo ""
    echo "Fix .clang-tidy configuration before proceeding!"
fi

# Show most common issues
echo ""
echo "🔝 Most Common Issues:"
if [ -s "$TIDY_COMBINED" ]; then
    grep "warning:\|error:" "$TIDY_COMBINED" | \
    sed 's/.*\[\(.*\)\].*/\1/' | \
    sort | uniq -c | sort -nr | head -10
else
    echo "No issues found! 🎉"
fi

echo ""
echo "📁 Results saved to:"
echo "  - Combined output: $TIDY_COMBINED"
echo "  - Stdout only: $TIDY_OUTPUT"
echo "  - Stderr only: $TIDY_ERRORS"

# Show first few warnings/errors for quick review
if [ -s "$TIDY_COMBINED" ]; then
    echo ""
    echo "🔍 First 10 issues (see full output in $TIDY_COMBINED):"
    echo "====================================================="
    grep -n "warning:\|error:" "$TIDY_COMBINED" | head -10
fi

# Show stderr content if it has useful info
if [ -s "$TIDY_ERRORS" ]; then
    echo ""
    echo "⚠️  Stderr content (first 20 lines):"
    echo "===================================="
    head -20 "$TIDY_ERRORS"
fi

# Show stdout content if it has useful info
if [ -s "$TIDY_OUTPUT" ]; then
    echo ""
    echo "📄 Stdout content (first 20 lines):"
    echo "===================================="
    head -20 "$TIDY_OUTPUT"
fi

echo ""
echo "✅ Static analysis complete!"
echo ""
echo "💡 Tips:"
echo "  - Review the full output in static-analysis-results/"
echo "  - Fix issues one category at a time"
echo "  - Update .clang-tidy to disable unwanted checks"
echo "  - Run this script regularly during development"
