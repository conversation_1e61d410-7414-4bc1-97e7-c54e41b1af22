#!/bin/bash

# IronFrost Test Runner Script
# This script builds and runs tests for the IronFrost game engine

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Default values
BUILD_TYPE="Debug"
CLEAN_BUILD=false
VERBOSE=false
RUN_SPECIFIC_TEST=""

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# Function to show usage
show_usage() {
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  -h, --help          Show this help message"
    echo "  -c, --clean         Clean build directory before building"
    echo "  -r, --release       Build in Release mode (default: Debug)"
    echo "  -v, --verbose       Verbose output"
    echo "  -t, --test NAME     Run specific test by name"
    echo ""
    echo "Examples:"
    echo "  $0                  # Build and run all tests in Debug mode"
    echo "  $0 --clean          # Clean build and run all tests"
    echo "  $0 --release        # Build and run tests in Release mode"
    echo "  $0 -t \"DeltaTimeCalculator basic functionality\"  # Run specific test"
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_usage
            exit 0
            ;;
        -c|--clean)
            CLEAN_BUILD=true
            shift
            ;;
        -r|--release)
            BUILD_TYPE="Release"
            shift
            ;;
        -v|--verbose)
            VERBOSE=true
            shift
            ;;
        -t|--test)
            RUN_SPECIFIC_TEST="$2"
            shift 2
            ;;
        *)
            print_error "Unknown option: $1"
            show_usage
            exit 1
            ;;
    esac
done

# Set VCPKG_ROOT if not already set
if [ -z "$VCPKG_ROOT" ]; then
    export VCPKG_ROOT="/Users/<USER>/vcpkg"
    print_status "Setting VCPKG_ROOT to $VCPKG_ROOT"
fi

# Clean build if requested
if [ "$CLEAN_BUILD" = true ]; then
    print_status "Cleaning build directory..."
    rm -rf "build/$BUILD_TYPE"
fi

# Configure the project
print_status "Configuring project for $BUILD_TYPE build..."
if [ "$VERBOSE" = true ]; then
    cmake --preset $BUILD_TYPE -DBUILD_TESTS=ON
else
    cmake --preset $BUILD_TYPE -DBUILD_TESTS=ON > /dev/null
fi

# Build the project
print_status "Building project..."
if [ "$VERBOSE" = true ]; then
    cmake --build "build/$BUILD_TYPE"
else
    cmake --build "build/$BUILD_TYPE" > /dev/null
fi

print_success "Build completed successfully!"

# Run tests
print_status "Running tests..."
cd "build/$BUILD_TYPE"

if [ -n "$RUN_SPECIFIC_TEST" ]; then
    print_status "Running specific test: $RUN_SPECIFIC_TEST"
    ./IronFrostTests "$RUN_SPECIFIC_TEST"
else
    if [ "$VERBOSE" = true ]; then
        ctest --verbose
    else
        ctest
    fi
fi

print_success "All tests completed!"
