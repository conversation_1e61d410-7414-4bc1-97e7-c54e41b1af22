# Development Scripts

This directory contains essential utility scripts for maintaining and analyzing the IronFrost game engine codebase.

## 📋 Include Organization Scripts

### `check-includes.sh`
**Purpose:** Comprehensive checker for missing C++ standard library includes.

**Usage:**
```bash
./scripts/check-includes.sh
```

**Features:**
- Checks for 70+ common C++ standard library patterns
- Organized by categories (containers, strings, algorithms, etc.)
- Identifies missing headers that could cause compilation issues
- Helps prevent cross-platform compilation problems

**Example Output:**
```
❌ Missing <string> in: src/gui/gui.hpp (std::string)
❌ Missing <vector> in: src/renderer/renderer.cpp (std::vector)
```

---

### `organize-includes-simple.py`
**Purpose:** Safe analysis tool that shows organized includes without modifying files.

**Usage:**
```bash
# Analyze a single file
python3 scripts/organize-includes-simple.py src/gui/gui.hpp

# Analyze all files in directory
python3 scripts/organize-includes-simple.py src/
```

**Features:**
- **Safe:** Only analyzes and displays, doesn't modify files
- Shows current include organization
- Prints properly organized includes for manual copy-paste
- Recommended for reviewing changes before applying

**Include Order Applied:**
1. Related header (for .cpp files)
2. C standard library (`<c...>`)
3. C++ standard library (`<...>`) - alphabetically
4. Third-party libraries - alphabetically
5. Local includes (`"..."`) - alphabetically

---

### `organize-includes.sh`
**Purpose:** Lightweight manual analysis tool for reviewing current include organization.

**Usage:**
```bash
./scripts/organize-includes.sh
```

**Features:**
- Shows current include organization in all files
- Categorizes includes as: C STD, C++ STD, 3RD PARTY, LOCAL
- Provides guidance on proper ordering
- Quick overview of current state

---

## 🔍 Static Analysis Scripts

### `run-static-analysis.sh`
**Purpose:** Run clang-tidy static analysis locally to match CI environment.

**Usage:**
```bash
./scripts/run-static-analysis.sh
```

**Features:**
- Checks for clang-tidy installation
- Validates .clang-tidy configuration
- Runs analysis on all C++ source files
- Generates detailed reports in `static-analysis-results/`
- Shows summary of warnings and errors
- Matches CI static analysis job

**Prerequisites:**
```bash
# Install clang-tidy
brew install llvm              # macOS
sudo apt-get install clang-tidy # Ubuntu

# Configure project first
cmake --preset Debug
```

**Quick single-file analysis:**
```bash
# Analyze a specific file quickly
clang-tidy -p build/Debug src/path/to/file.cpp
```

---

### `test-clang-tidy-config.sh`
**Purpose:** Quick test of .clang-tidy configuration syntax.

**Usage:**
```bash
./scripts/test-clang-tidy-config.sh
```

**Features:**
- Validates .clang-tidy syntax
- Shows number of enabled checks
- Lists sample checks
- Quick validation before running full analysis

---

## 🧪 Testing Scripts

### `run_tests.sh`
**Purpose:** Comprehensive test runner that handles building and running the test suite.

**Usage:**
```bash
./scripts/run_tests.sh [OPTIONS]
```

**Options:**
- `-h, --help` - Show help message
- `-c, --clean` - Clean build directory before building
- `-r, --release` - Build in Release mode (default: Debug)
- `-v, --verbose` - Show verbose build and test output
- `-t, --test NAME` - Run specific test by name

**Features:**
- Automatic vcpkg environment setup
- Colored output for better readability
- Support for both Debug and Release builds
- Individual test execution capability
- Clean build option for fresh starts

**Examples:**
```bash
./scripts/run_tests.sh                    # Quick test run (Debug mode)
./scripts/run_tests.sh --clean            # Clean build + test
./scripts/run_tests.sh --release          # Release mode testing
./scripts/run_tests.sh --verbose          # Detailed output
./scripts/run_tests.sh -t "DeltaTimeCalculator basic functionality"  # Specific test
```

---

## 🚀 Quick Start

1. **Check for missing includes:**
   ```bash
   ./scripts/check-includes.sh
   ```

2. **Review current organization:**
   ```bash
   ./scripts/organize-includes.sh
   ```

3. **Preview organized includes (safe):**
   ```bash
   python3 scripts/organize-includes-simple.py src/
   ```

4. **Run static analysis:**
   ```bash
   ./scripts/test-clang-tidy-config.sh    # Test config first
   ./scripts/run-static-analysis.sh       # Full analysis
   ```

5. **Run tests:**
   ```bash
   ./scripts/run_tests.sh                 # Run all tests
   ./scripts/run_tests.sh --clean         # Clean build + test
   ```

## 📝 Best Practices

### Include Order Standard:
```cpp
// Related header (for .cpp files only)
#include "corresponding_header.hpp"

// C standard library
#include <cmath>
#include <cstring>

// C++ standard library  
#include <algorithm>
#include <memory>
#include <string>
#include <vector>

// Third-party libraries
#include <glad/glad.h>
#include <glm/glm.hpp>
#include <sol/sol.hpp>

// Local includes
#include "../utils/string_id.hpp"
#include "widgets/widget.hpp"
```

### Benefits:
- **Consistency:** All files follow the same pattern
- **Readability:** Clear separation between include types
- **Maintainability:** Easier to spot missing/duplicate includes
- **Compilation:** Reduces header dependency issues
- **Standards:** Follows Google C++ Style Guide

## 🔧 Troubleshooting

### Permission Issues:
```bash
chmod +x scripts/*.sh scripts/*.py
```

### Python Requirements:
- Python 3.6+
- No external dependencies required

### Common Issues:
- **File encoding:** Scripts assume UTF-8 encoding
- **Line endings:** Works with both Unix and Windows line endings
- **Large files:** May be slow on very large codebases

## 📚 References

- [Google C++ Style Guide - Names and Order of Includes](https://google.github.io/styleguide/cppguide.html#Names_and_Order_of_Includes)
- [LLVM Coding Standards - #include Style](https://llvm.org/docs/CodingStandards.html#include-style)
