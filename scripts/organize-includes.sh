#!/bin/bash
# organize-includes.sh - Simple include organization checker

echo "🔍 Analyzing include organization in C++ files..."

check_file_includes() {
    local file="$1"
    echo "📄 Checking: $file"
    
    # Extract includes and show current order
    grep -n "^#include" "$file" | while IFS=: read -r line_num include_line; do
        # Categorize the include
        if [[ "$include_line" =~ \#include\ \<c[a-z]+ ]]; then
            echo "  $line_num: [C STD] $include_line"
        elif [[ "$include_line" =~ \#include\ \<[^/]+\> ]]; then
            echo "  $line_num: [C++ STD] $include_line"
        elif [[ "$include_line" =~ \#include\ \<.*/.*\> ]]; then
            echo "  $line_num: [3RD PARTY] $include_line"
        elif [[ "$include_line" =~ \#include\ \".*\" ]]; then
            echo "  $line_num: [LOCAL] $include_line"
        else
            echo "  $line_num: [UNKNOWN] $include_line"
        fi
    done
    echo ""
}

# Check organization of includes
echo "Current include organization:"
echo "============================="

find src/ -name "*.hpp" -o -name "*.h" -o -name "*.cpp" -o -name "*.cc" | while read file; do
    if grep -q "^#include" "$file"; then
        check_file_includes "$file"
    fi
done

echo ""
echo "📋 Recommended include order:"
echo "1. Related header (for .cpp files)"
echo "2. C standard library (<c...>)"
echo "3. C++ standard library (<...>) - alphabetically"
echo "4. Third-party libraries (<.../...>) - alphabetically"
echo "5. Local includes (\"...\") - alphabetically"
echo ""
echo "💡 Use the Python script 'organize-includes.py' to automatically fix the order"
echo "   Usage: python3 organize-includes.py [directory]"
