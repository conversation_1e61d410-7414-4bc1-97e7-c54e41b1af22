#!/bin/bash
# test-clang-tidy-config.sh - Test .clang-tidy configuration

echo "🔧 Testing .clang-tidy configuration..."

# Check if clang-tidy is installed
if ! command -v clang-tidy &> /dev/null; then
    echo "❌ clang-tidy is not installed!"
    exit 1
fi

# Check if .clang-tidy exists
if [ ! -f ".clang-tidy" ]; then
    echo "❌ .clang-tidy file not found!"
    exit 1
fi

echo "✅ Found .clang-tidy configuration"

# Test the configuration by running clang-tidy with --list-checks
echo "🔍 Testing configuration syntax..."

if clang-tidy --list-checks --config-file=.clang-tidy > /dev/null 2>&1; then
    echo "✅ Configuration syntax is valid!"
    
    # Show enabled checks count
    CHECK_COUNT=$(clang-tidy --list-checks --config-file=.clang-tidy 2>/dev/null | grep -c "^  ")
    echo "📊 Enabled checks: $CHECK_COUNT"
    
    echo ""
    echo "🎯 Sample of enabled checks:"
    clang-tidy --list-checks --config-file=.clang-tidy 2>/dev/null | head -20
    
else
    echo "❌ Configuration has syntax errors!"
    echo ""
    echo "Error details:"
    clang-tidy --list-checks --config-file=.clang-tidy
    exit 1
fi

echo ""
echo "✅ .clang-tidy configuration is valid!"
