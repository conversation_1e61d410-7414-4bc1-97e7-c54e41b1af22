#!/usr/bin/env python3
"""
organize-includes-simple.py - Simple and reliable C++ include organizer

This script extracts includes, categorizes them, and prints the organized version
for manual copy-paste. This avoids file corruption issues.
"""

import os
import re
import sys

def categorize_include(include_line, current_file):
    """Categorize an include line"""
    include_line = include_line.strip()
    
    # Extract the include path
    match = re.match(r'#include\s*[<"](.*?)[>"]', include_line)
    if not match:
        return 'unknown', include_line
    
    include_path = match.group(1)
    
    # Related header (for .cpp files)
    if current_file.endswith('.cpp'):
        base_name = os.path.splitext(os.path.basename(current_file))[0]
        if include_path.endswith(f'{base_name}.hpp') or include_path.endswith(f'{base_name}.h'):
            return 'related', include_line
    
    # C standard library
    c_headers = ['cassert', 'cctype', 'cerrno', 'cfenv', 'cfloat', 'cinttypes', 
                'climits', 'clocale', 'cmath', 'csetjmp', 'csignal', 'cstdarg',
                'cstddef', 'cstdint', 'cstdio', 'cstdlib', 'cstring', 'ctime',
                'cuchar', 'cwchar', 'cwctype']
    if include_path in c_headers:
        return 'c_std', include_line
    
    # C++ standard library
    cpp_std_headers = [
        'algorithm', 'any', 'array', 'atomic', 'bitset', 'chrono', 'codecvt',
        'complex', 'condition_variable', 'deque', 'exception', 'execution',
        'filesystem', 'forward_list', 'fstream', 'functional', 'future',
        'initializer_list', 'iomanip', 'ios', 'iosfwd', 'iostream', 'istream',
        'iterator', 'limits', 'list', 'locale', 'map', 'memory', 'memory_resource',
        'mutex', 'new', 'numeric', 'optional', 'ostream', 'queue', 'random',
        'ratio', 'regex', 'scoped_allocator', 'set', 'shared_mutex', 'sstream',
        'stack', 'stdexcept', 'streambuf', 'string', 'string_view', 'strstream',
        'system_error', 'thread', 'tuple', 'type_traits', 'typeindex', 'typeinfo',
        'unordered_map', 'unordered_set', 'utility', 'valarray', 'variant', 'vector'
    ]
    
    if include_path in cpp_std_headers:
        return 'cpp_std', include_line
    
    # Local includes (quoted)
    if include_line.strip().startswith('#include "'):
        return 'local', include_line
    
    # Third-party libraries (everything else with angle brackets)
    if include_line.strip().startswith('#include <'):
        return 'third_party', include_line
    
    return 'unknown', include_line

def analyze_file(file_path):
    """Analyze includes in a file and print organized version"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()
    except Exception as e:
        print(f"Error reading {file_path}: {e}")
        return
    
    # Find includes
    includes = []
    for line in lines:
        if line.strip().startswith('#include'):
            includes.append(line.strip())
    
    if not includes:
        print(f"No includes found in {file_path}")
        return
    
    # Categorize includes
    categorized = {
        'related': [],
        'c_std': [],
        'cpp_std': [],
        'third_party': [],
        'local': [],
        'unknown': []
    }
    
    for include in includes:
        category, line = categorize_include(include, file_path)
        categorized[category].append(line)
    
    # Sort each category
    for category in categorized:
        categorized[category].sort(key=lambda x: x.lower())
    
    # Print organized includes
    print(f"\n📄 Organized includes for: {file_path}")
    print("=" * 60)
    
    sections = [
        ('related', None),
        ('c_std', '// C standard library'),
        ('cpp_std', '// C++ standard library'),
        ('third_party', '// Third-party libraries'),
        ('local', '// Local includes')
    ]
    
    first_section = True
    for section_name, header in sections:
        if categorized[section_name]:
            if not first_section:
                print()
            
            if header:
                print(header)
            
            for include in categorized[section_name]:
                print(include)
            first_section = False
    
    if categorized['unknown']:
        if not first_section:
            print()
        print('// Unknown includes')
        for include in categorized['unknown']:
            print(include)
    
    print("=" * 60)

def main():
    """Main function"""
    if len(sys.argv) < 2:
        print("Usage: python3 organize-includes-simple.py <file1> [file2] ...")
        print("       python3 organize-includes-simple.py src/  (analyze all files)")
        return 1
    
    target = sys.argv[1]
    
    if os.path.isfile(target):
        # Single file
        analyze_file(target)
    elif os.path.isdir(target):
        # Directory - find all C++ files
        cpp_files = []
        for root, dirs, files in os.walk(target):
            for file in files:
                if file.endswith(('.hpp', '.h', '.cpp', '.cc', '.cxx')):
                    cpp_files.append(os.path.join(root, file))
        
        print(f"Found {len(cpp_files)} C++ files")
        for file_path in cpp_files:
            analyze_file(file_path)
    else:
        # Multiple files
        for file_path in sys.argv[1:]:
            if os.path.isfile(file_path):
                analyze_file(file_path)

if __name__ == '__main__':
    sys.exit(main())
