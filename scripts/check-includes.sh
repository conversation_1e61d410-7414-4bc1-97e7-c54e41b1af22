#!/bin/bash
# check_includes.sh - Comprehensive C++ standard library include checker

echo "🔍 Checking for missing standard library includes..."

# Function to check for a pattern and required include
check_include() {
    local pattern="$1"
    local header="$2"
    local description="$3"

    find src/ -name "*.hpp" -o -name "*.h" -o -name "*.cpp" -o -name "*.cc" | xargs grep -l "$pattern" 2>/dev/null | while read file; do
        if ! grep -q "#include <$header>" "$file"; then
            echo "❌ Missing <$header> in: $file ($description)"
        fi
    done
}

echo "📦 Checking container headers..."
check_include "std::vector" "vector" "std::vector"
check_include "std::array" "array" "std::array"
check_include "std::list" "list" "std::list"
check_include "std::deque" "deque" "std::deque"
check_include "std::queue" "queue" "std::queue"
check_include "std::stack" "stack" "std::stack"
check_include "std::set" "set" "std::set"
check_include "std::map" "map" "std::map"
check_include "std::unordered_set" "unordered_set" "std::unordered_set"
check_include "std::unordered_map" "unordered_map" "std::unordered_map"

echo "📝 Checking string and stream headers..."
check_include "std::string" "string" "std::string"
check_include "std::wstring" "string" "std::wstring"
check_include "std::stringstream\|std::ostringstream\|std::istringstream" "sstream" "string streams"
check_include "std::cout\|std::cin\|std::cerr\|std::clog" "iostream" "iostream objects"
check_include "std::ifstream\|std::ofstream\|std::fstream" "fstream" "file streams"

echo "🧠 Checking memory management..."
check_include "std::unique_ptr\|std::shared_ptr\|std::weak_ptr\|std::make_unique\|std::make_shared" "memory" "smart pointers"

echo "⚙️ Checking functional programming..."
check_include "std::function" "functional" "std::function"
check_include "std::bind" "functional" "std::bind"
check_include "std::hash" "functional" "std::hash"

echo "🔄 Checking algorithms..."
check_include "std::sort\|std::find\|std::for_each\|std::transform\|std::copy" "algorithm" "algorithms"
check_include "std::min\|std::max\|std::clamp" "algorithm" "min/max functions"

echo "🔢 Checking utility and numeric..."
check_include "std::pair\|std::make_pair" "utility" "std::pair"
check_include "std::move\|std::forward" "utility" "move semantics"
check_include "std::swap" "utility" "std::swap"
check_include "std::accumulate\|std::inner_product" "numeric" "numeric algorithms"

echo "❗ Checking exception handling..."
check_include "std::exception\|std::runtime_error\|std::logic_error" "exception" "exception classes"
check_include "std::invalid_argument\|std::out_of_range" "stdexcept" "standard exceptions"

echo "🧵 Checking threading..."
check_include "std::thread" "thread" "std::thread"
check_include "std::mutex\|std::lock_guard\|std::unique_lock" "mutex" "mutex types"
check_include "std::atomic" "atomic" "std::atomic"
check_include "std::condition_variable" "condition_variable" "condition variables"
check_include "std::future\|std::promise\|std::async" "future" "future/promise"

echo "⏰ Checking time and random..."
check_include "std::chrono" "chrono" "time utilities"
check_include "std::random_device\|std::mt19937\|std::uniform_int_distribution" "random" "random number generation"

echo "📏 Checking limits and type traits..."
check_include "std::numeric_limits" "limits" "numeric limits"
check_include "std::is_same\|std::enable_if\|std::decay" "type_traits" "type traits"

echo "🔧 Checking C standard library..."
check_include "std::size_t\|std::ptrdiff_t" "cstddef" "size_t/ptrdiff_t"
check_include "std::malloc\|std::free\|std::calloc\|std::realloc" "cstdlib" "C memory functions"
check_include "std::printf\|std::sprintf\|std::scanf" "cstdio" "C I/O functions"
check_include "std::strlen\|std::strcpy\|std::strcmp" "cstring" "C string functions"
check_include "std::memcpy\|std::memset\|std::memcmp" "cstring" "C memory functions"
check_include "std::abs\|std::sqrt\|std::sin\|std::cos\|std::pow" "cmath" "C math functions"

echo "🆕 Checking C++17/20 features..."
check_include "std::optional" "optional" "std::optional"
check_include "std::variant" "variant" "std::variant"
check_include "std::any" "any" "std::any"
check_include "std::filesystem" "filesystem" "filesystem utilities"

echo ""
echo "✅ Include check complete!"
echo "💡 Tip: Fix these includes to avoid compilation issues across different environments"
