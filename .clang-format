# clang-format configuration for IronFrost Game Engine
# Based on LLVM style with customizations for the project

BasedOnStyle: LLVM

# Basic formatting
IndentWidth: 2
TabWidth: 2
UseTab: Never
ColumnLimit: 120

# Language settings
Language: Cpp
Standard: c++20

# Indentation
IndentCaseLabels: true
IndentPPDirectives: BeforeHash
IndentWrappedFunctionNames: false
IndentAccessModifiers: false
AccessModifierOffset: -2

# Alignment
AlignAfterOpenBracket: Align
AlignConsecutiveAssignments: false
AlignConsecutiveDeclarations: false
AlignConsecutiveMacros: false
AlignEscapedNewlines: Right
AlignOperands: true
AlignTrailingComments: true

# Allow settings
AllowAllArgumentsOnNextLine: true
AllowAllConstructorInitializersOnNextLine: true
AllowAllParametersOfDeclarationOnNextLine: true
AllowShortBlocksOnASingleLine: Empty
AllowShortCaseLabelsOnASingleLine: false
AllowShortFunctionsOnASingleLine: Empty
AllowShortIfStatementsOnASingleLine: Never
AllowShortLambdasOnASingleLine: All
AllowShortLoopsOnASingleLine: false

# Breaking
AlwaysBreakAfterReturnType: None
AlwaysBreakBeforeMultilineStrings: false
AlwaysBreakTemplateDeclarations: Yes
BreakBeforeBinaryOperators: None
BreakBeforeBraces: Attach
BreakBeforeTernaryOperators: true
BreakConstructorInitializers: BeforeColon
BreakInheritanceList: BeforeColon
BreakStringLiterals: true

# Spacing
SpaceAfterCStyleCast: false
SpaceAfterLogicalNot: false
SpaceAfterTemplateKeyword: true
SpaceBeforeAssignmentOperators: true
SpaceBeforeCpp11BracedList: false
SpaceBeforeCtorInitializerColon: true
SpaceBeforeInheritanceColon: true
SpaceBeforeParens: ControlStatements
SpaceBeforeRangeBasedForLoopColon: true
SpaceInEmptyParentheses: false
SpacesBeforeTrailingComments: 1
SpacesInAngles: false
SpacesInCStyleCastParentheses: false
SpacesInContainerLiterals: true
SpacesInParentheses: false
SpacesInSquareBrackets: false

# Pointer and reference alignment
PointerAlignment: Left
ReferenceAlignment: Left

# Include sorting
SortIncludes: true
IncludeBlocks: Regroup
IncludeCategories:
  # System headers
  - Regex: '^<[^/]*>'
    Priority: 1
  # C++ standard library
  - Regex: '^<[^/]*\.h>'
    Priority: 2
  # Third-party libraries
  - Regex: '^<.*>'
    Priority: 3
  # Project headers
  - Regex: '^".*"'
    Priority: 4

# Other settings
BinPackArguments: true
BinPackParameters: true
CompactNamespaces: false
ConstructorInitializerAllOnOneLineOrOnePerLine: false
ConstructorInitializerIndentWidth: 4
ContinuationIndentWidth: 4
Cpp11BracedListStyle: true
DerivePointerAlignment: false
ExperimentalAutoDetectBinPacking: false
FixNamespaceComments: true
KeepEmptyLinesAtTheStartOfBlocks: true
MaxEmptyLinesToKeep: 1
NamespaceIndentation: None
PenaltyBreakAssignment: 2
PenaltyBreakBeforeFirstCallParameter: 19
PenaltyBreakComment: 300
PenaltyBreakFirstLessLess: 120
PenaltyBreakString: 1000
PenaltyBreakTemplateDeclaration: 10
PenaltyExcessCharacter: 1000000
PenaltyReturnTypeOnItsOwnLine: 60
ReflowComments: true
SortUsingDeclarations: true

# Macros and attributes
AttributeMacros: []
ForEachMacros: []
StatementMacros: []
TypenameMacros: []

# Comments
CommentPragmas: '^ IWYU pragma:'
