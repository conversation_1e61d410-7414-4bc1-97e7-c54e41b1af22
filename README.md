# IronFrost Game Engine

A modern C++20 game engine featuring modular architecture, OpenGL rendering, Lua scripting, and comprehensive asset management.

## Features

- **Modern C++20**: Built with modern C++ standards and best practices
- **Modular Architecture**: Clean separation of concerns with well-defined interfaces
- **OpenGL Rendering**: Hardware-accelerated 3D graphics with shader support
- **Lua Scripting**: Flexible scripting system with comprehensive API bindings
- **Asset Management**: Efficient loading and management of textures, models, shaders, and fonts
- **Virtual File System**: Abstracted file access with PhysFS integration
- **Event System**: Decoupled communication between engine components
- **Scene Management**: Hierarchical scene graph with camera and lighting systems
- **GUI System**: Immediate-mode GUI with widget support
- **Console System**: In-game developer console with command processing

## Requirements

### System Requirements
- **Operating System**: Windows 10/11, Linux, macOS
- **Compiler**: 
  - MSVC 2022 (Windows)
  - GCC 11+ (Linux)
  - Clang 14+ (macOS)
- **CMake**: Version 3.29.0 or higher
- **vcpkg**: For dependency management

### Dependencies
All dependencies are managed through vcpkg:
- **GLFW 3.4+**: Window and input management
- **OpenGL**: Graphics rendering (via GLAD)
- **GLM 1.0.1+**: Mathematics library
- **Assimp 5.4.3+**: 3D model loading
- **PhysFS 3.2.0+**: Virtual file system
- **Lua 5.4.7+**: Scripting engine (with Sol2 bindings)
- **nlohmann/json 3.12.0+**: JSON parsing
- **STB**: Image loading and processing
- **FreeType 2.13.3+**: Font rendering

## Quick Start

### 1. Clone the Repository
```bash
git clone https://github.com/yourusername/IronFrost.git
cd IronFrost
```

### 2. Install vcpkg (if not already installed)
```bash
# Windows
git clone https://github.com/Microsoft/vcpkg.git
cd vcpkg
.\bootstrap-vcpkg.bat
.\vcpkg integrate install

# Linux/macOS
git clone https://github.com/Microsoft/vcpkg.git
cd vcpkg
./bootstrap-vcpkg.sh
./vcpkg integrate install
```

### 3. Set Environment Variable
```bash
# Set VCPKG_ROOT to your vcpkg installation directory
export VCPKG_ROOT=/path/to/vcpkg  # Linux/macOS
set VCPKG_ROOT=C:\path\to\vcpkg   # Windows
```

### 4. Build the Project
```bash
# Configure
cmake --preset Debug

# Build
cmake --build build/Debug

# Or use Release preset
cmake --preset Release
cmake --build build/Release
```

### 5. Run the Engine
```bash
# From build directory
cd build/Debug
./IronFrost  # Linux/macOS
IronFrost.exe  # Windows
```

## Project Structure

```
IronFrost/
├── src/                    # Source code
│   ├── application.cpp     # Main application class
│   ├── assets/            # Asset management system
│   ├── config/            # Configuration management
│   ├── events/            # Event system
│   ├── gui/               # GUI system
│   ├── renderer/          # Rendering system
│   ├── scene/             # Scene management
│   ├── scripts/           # Scripting system
│   ├── services/          # Service locator pattern
│   ├── utils/             # Utility classes
│   ├── vfs/               # Virtual file system
│   └── window/            # Window and input management
├── data/                  # Game assets and configuration
│   ├── config/            # Engine configuration
│   └── core/              # Core assets (shaders, scripts, etc.)
├── build/                 # Build output directory
├── CMakeLists.txt         # CMake configuration
├── CMakePresets.json      # CMake presets
├── vcpkg.json            # Dependency configuration
└── README.md             # This file
```

## Architecture Overview

### Core Systems

#### Application Layer
- **Application**: Main application class managing engine lifecycle
- **Config**: Configuration management and asset loading

#### Rendering System
- **IRenderer**: Abstract renderer interface with OpenGL implementation
- **Resource Manager**: GPU resource management and caching
- **GUI Renderer**: Immediate-mode GUI rendering
- **Post-processing**: Shader-based post-processing effects

#### Asset Management
- **AssetsManager**: Centralized asset loading and caching
- **VFS (Virtual File System)**: Abstracted file access with archive support
- **Loaders**: Specialized loaders for different asset types

#### Scene System
- **Scene Manager**: Scene loading, switching, and management
- **Scene Graph**: Hierarchical object organization
- **Camera**: 3D camera with movement and rotation
- **Lighting**: Scene lighting management

#### Scripting
- **Lua Integration**: Sol2-based Lua scripting with comprehensive API
- **Script Engine**: Script execution and lifecycle management
- **API Bindings**: Engine functionality exposed to scripts

#### Event System
- **Event Dispatcher**: Type-safe event handling with async support
- **Service Locator**: Dependency injection and service management

## Usage Examples

### Basic Scene Setup
```cpp
#include "application.hpp"

int main() {
    IronFrost::Application app;
    app.initialize();
    app.run();
    return 0;
}
```

### Lua Scripting
```lua
-- Camera movement script
local camera = scene:getCamera()
local keyboard = window:getKeyboard()

if keyboard:isKeyDown(KeyType.W) then
    camera:moveForward(10.0, deltaTime)
end

if keyboard:isKeyDown(KeyType.S) then
    camera:moveBackward(10.0, deltaTime)
end
```

### Asset Loading
```cpp
// Load and use assets
AssetsManager& assets = getAssetsManager();
const auto& shader = assets.loadShader("basic", "shaders/basic.glsl");
const auto& texture = assets.loadImage("wall", "textures/wall.png");
const auto& model = assets.loadModel("cube", "models/cube.obj");
```

## Controls

- **ESC**: Exit application
- **~ (Tilde)**: Toggle developer console
- **WASD**: Camera movement (in scripts)
- **QE**: Camera rotation left/right (in scripts)
- **RF**: Camera rotation up/down (in scripts)

## Contributing

We welcome contributions! Please see [CONTRIBUTING.md](CONTRIBUTING.md) for guidelines.

### Development Setup
1. Follow the Quick Start guide above
2. Install additional development tools:
   - clang-format for code formatting
   - clang-tidy for static analysis
   - Doxygen for documentation generation

### Development Scripts
The `scripts/` directory contains utility tools for code maintenance:

- **`check-includes.sh`**: Check for missing C++ standard library includes
- **`organize-includes-simple.py`**: Safe analysis tool for reviewing include organization
- **`organize-includes.sh`**: Lightweight analysis of current include structure
- **`run-static-analysis.sh`**: Run clang-tidy static analysis locally
- **`test-clang-tidy-config.sh`**: Test .clang-tidy configuration syntax

See [scripts/README.md](scripts/README.md) for detailed usage instructions.

### Code Style
- Follow the existing code style
- Use meaningful variable and function names
- Add comments for complex logic
- Write unit tests for new features

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## Acknowledgments

- **GLFW**: Cross-platform window and input handling
- **OpenGL**: Graphics rendering API
- **Lua**: Embedded scripting language
- **Assimp**: 3D model loading library
- **PhysFS**: Virtual file system
- **GLM**: OpenGL Mathematics library
- **nlohmann/json**: JSON parsing library
- **STB**: Single-file public domain libraries
- **FreeType**: Font rendering library

## Roadmap

- [ ] Audio system integration
- [ ] Physics engine integration
- [ ] Advanced rendering features (PBR, deferred rendering)
- [ ] Multi-threading support
- [ ] Networking capabilities
- [ ] Mobile platform support
- [ ] Visual scripting editor
- [ ] Asset streaming and LOD system

## Support

For questions, issues, or contributions:
- Create an issue on GitHub
- Check the documentation
- Review existing code examples

---

**IronFrost Game Engine** - Building the future of game development, one frame at a time.
