name: CI

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

env:
  # Customize the CMake build type here (Release, Debug, RelWithDebInfo, etc.)
  BUILD_TYPE: Release

jobs:
  build-and-test:
    strategy:
      matrix:
        os: [ubuntu-latest, windows-latest, macos-13]
        build_type: [Debug, Release]
        include:
          - os: ubuntu-latest
            vcpkg_triplet: x64-linux
          - os: windows-latest
            vcpkg_triplet: x64-windows
          - os: macos-13
            vcpkg_triplet: x64-osx

    runs-on: ${{ matrix.os }}

    steps:
    - name: Checkout repository
      uses: actions/checkout@v4
      with:
        submodules: recursive

    - name: Setup CMake and Ninja
      uses: lukka/get-cmake@latest

    - name: Setup MSVC (Windows only)
      if: matrix.os == 'windows-latest'
      uses: ilammy/msvc-dev-cmd@v1

    - name: Install system dependencies (Ubuntu)
      if: matrix.os == 'ubuntu-latest'
      run: |
        sudo apt-get update
        sudo apt-get install -y \
          build-essential \
          pkg-config \
          libx11-dev \
          libxrandr-dev \
          libxinerama-dev \
          libxcursor-dev \
          libxi-dev \
          libgl1-mesa-dev \
          libglu1-mesa-dev \
          libasound2-dev \
          xorg-dev

    - name: Install system dependencies (macOS)
      if: matrix.os == 'macos-13'
      run: |
        # Install build tools and dependencies
        brew install ninja pkg-config
        # Ensure Xcode command line tools are available
        xcode-select --install || true

    - name: Cache vcpkg
      uses: actions/cache@v4
      with:
        path: |
          ${{ github.workspace }}/vcpkg
          ~/.cache/vcpkg
        key: ${{ runner.os }}-${{ matrix.vcpkg_triplet }}-vcpkg-x11-${{ hashFiles('vcpkg.json') }}
        restore-keys: |
          ${{ runner.os }}-${{ matrix.vcpkg_triplet }}-vcpkg-x11-

    - name: Setup vcpkg
      shell: bash
      run: |
        VCPKG_DIR="${{ github.workspace }}/vcpkg"
        if [ ! -d "$VCPKG_DIR" ]; then
          git clone https://github.com/Microsoft/vcpkg.git "$VCPKG_DIR"
        fi
        cd "$VCPKG_DIR"

        # Clean any existing installations to ensure fresh build with X11
        if [ -d "installed" ]; then
          echo "Cleaning existing vcpkg installations..."
          rm -rf installed
        fi
        if [ -d "buildtrees" ]; then
          echo "Cleaning existing vcpkg buildtrees..."
          rm -rf buildtrees
        fi

        if [ "${{ runner.os }}" = "Windows" ]; then
          cmd //c "bootstrap-vcpkg.bat"
        else
          ./bootstrap-vcpkg.sh
        fi

    - name: Verify X11 installation
      if: matrix.os == 'ubuntu-latest'
      shell: bash
      run: |
        echo "Checking for X11 libraries..."
        pkg-config --exists x11 && echo "X11 found via pkg-config" || echo "X11 NOT found via pkg-config"
        ls -la /usr/include/X11/ || echo "X11 headers not found"
        ls -la /usr/lib/x86_64-linux-gnu/libX11* || echo "X11 libraries not found"

    - name: Install vcpkg dependencies
      shell: bash
      run: |
        cd "${{ github.workspace }}"
        echo "Current directory: $(pwd)"
        echo "Contents:"
        ls -la
        echo "vcpkg.json contents:"
        cat vcpkg.json
        echo "Installing dependencies for triplet: ${{ matrix.vcpkg_triplet }}"

        VCPKG_EXE="${{ github.workspace }}/vcpkg/vcpkg"
        if [ "${{ runner.os }}" = "Windows" ]; then
          VCPKG_EXE="${{ github.workspace }}/vcpkg/vcpkg.exe"
        fi
        "$VCPKG_EXE" install --triplet ${{ matrix.vcpkg_triplet }}



    - name: Configure CMake
      shell: bash
      env:
        VCPKG_ROOT: ${{ github.workspace }}/vcpkg
      run: |
        VCPKG_TOOLCHAIN="${{ github.workspace }}/vcpkg/scripts/buildsystems/vcpkg.cmake"
        cmake --preset ${{ matrix.build_type }} \
          -DCMAKE_BUILD_TYPE=${{ matrix.build_type }} \
          -DCMAKE_TOOLCHAIN_FILE="$VCPKG_TOOLCHAIN" \
          -DVCPKG_TARGET_TRIPLET=${{ matrix.vcpkg_triplet }} \
          -DBUILD_TESTS=ON \
          -DENABLE_CLANG_TIDY=OFF

    - name: Build
      shell: bash
      run: cmake --build build/${{ matrix.build_type }} --config ${{ matrix.build_type }} --parallel

    # - name: Run tests
    #   working-directory: build/${{ matrix.build_type }}
    #   run: ctest --output-on-failure --parallel --build-config ${{ matrix.build_type }}

    # - name: Upload test results
    #   if: always()
    #   uses: actions/upload-artifact@v4
    #   with:
    #     name: test-results-${{ matrix.os }}-${{ matrix.build_type }}
    #     path: |
    #       build/${{ matrix.build_type }}/Testing/
    #       build/${{ matrix.build_type }}/**/*.log
    #

  static-analysis:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout repository
      uses: actions/checkout@v4

    - name: Setup CMake and Ninja
      uses: lukka/get-cmake@latest

    - name: Install system dependencies
      run: |
        sudo apt-get update
        sudo apt-get install -y \
          build-essential \
          pkg-config \
          libx11-dev \
          libxrandr-dev \
          libxinerama-dev \
          libxcursor-dev \
          libxi-dev \
          libgl1-mesa-dev \
          libglu1-mesa-dev \
          libasound2-dev \
          xorg-dev

    - name: Cache vcpkg
      uses: actions/cache@v4
      with:
        path: |
          ${{ github.workspace }}/vcpkg
          ~/.cache/vcpkg
        key: ubuntu-x64-linux-vcpkg-x11-${{ hashFiles('vcpkg.json') }}
        restore-keys: |
          ubuntu-x64-linux-vcpkg-x11-

    - name: Setup vcpkg
      shell: bash
      run: |
        VCPKG_DIR="${{ github.workspace }}/vcpkg"
        if [ ! -d "$VCPKG_DIR" ]; then
          git clone https://github.com/Microsoft/vcpkg.git "$VCPKG_DIR"
        fi
        cd "$VCPKG_DIR"
        ./bootstrap-vcpkg.sh

    - name: Install vcpkg dependencies
      shell: bash
      run: |
        cd "${{ github.workspace }}"
        "${{ github.workspace }}/vcpkg/vcpkg" install --triplet x64-linux

    - name: Install clang-tidy
      run: |
        sudo apt-get update
        sudo apt-get install -y clang-tidy



    - name: Configure CMake for static analysis
      shell: bash
      env:
        VCPKG_ROOT: ${{ github.workspace }}/vcpkg
      run: |
        VCPKG_TOOLCHAIN="${{ github.workspace }}/vcpkg/scripts/buildsystems/vcpkg.cmake"
        cmake --preset Release \
          -DCMAKE_BUILD_TYPE=Debug \
          -DCMAKE_TOOLCHAIN_FILE="$VCPKG_TOOLCHAIN" \
          -DVCPKG_TARGET_TRIPLET=x64-linux \
          -DBUILD_TESTS=ON \
          -DCMAKE_EXPORT_COMPILE_COMMANDS=ON \
          -DENABLE_CLANG_TIDY=OFF

    - name: Run static analysis
      shell: bash
      run: |
        # Make script executable
        chmod +x scripts/run-static-analysis.sh
        # Run our standardized static analysis script
        ./scripts/run-static-analysis.sh

    - name: Upload static analysis results
      if: always()
      uses: actions/upload-artifact@v4
      with:
        name: static-analysis-results
        path: |
          static-analysis-results/
          .clang-tidy

  code-formatting:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout repository
      uses: actions/checkout@v4

    - name: Install clang-format
      run: |
        sudo apt-get update
        sudo apt-get install -y clang-format

    - name: Check code formatting
      run: |
        # Find all C++ source and header files
        files=$(find src -name "*.cpp" -o -name "*.hpp" -o -name "*.cc" -o -name "*.cxx" -o -name "*.h")
        if [ -n "$files" ]; then
          echo "Checking formatting for files:"
          echo "$files"
          echo "$files" | xargs clang-format --dry-run --Werror
        else
          echo "No C++ files found to check"
        fi

  documentation:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout repository
      uses: actions/checkout@v4

    - name: Install Doxygen
      run: |
        sudo apt-get update
        sudo apt-get install -y doxygen graphviz

    - name: Generate documentation
      run: |
        doxygen Doxyfile
        # Check if documentation was generated
        if [ ! -d "docs/html" ]; then
          echo "Error: Documentation was not generated properly"
          exit 1
        fi

    - name: Upload documentation
      uses: actions/upload-artifact@v4
      with:
        name: documentation
        path: docs/

    - name: Deploy documentation to GitHub Pages
      if: github.ref == 'refs/heads/main' && github.event_name == 'push'
      uses: peaceiris/actions-gh-pages@v3
      with:
        github_token: ${{ secrets.GITHUB_TOKEN }}
        publish_dir: ./docs/html

  security-scan:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout repository
      uses: actions/checkout@v4

    - name: Run CodeQL Analysis
      uses: github/codeql-action/init@v3
      with:
        languages: cpp

    - name: Setup CMake
      uses: lukka/get-cmake@latest

    - name: Install system dependencies
      run: |
        sudo apt-get update
        sudo apt-get install -y \
          build-essential \
          pkg-config \
          libx11-dev \
          libxrandr-dev \
          libxinerama-dev \
          libxcursor-dev \
          libxi-dev \
          libgl1-mesa-dev \
          libglu1-mesa-dev \
          libasound2-dev \
          xorg-dev

    - name: Setup vcpkg
      shell: bash
      run: |
        VCPKG_DIR="${{ github.workspace }}/vcpkg"
        if [ ! -d "$VCPKG_DIR" ]; then
          git clone https://github.com/Microsoft/vcpkg.git "$VCPKG_DIR"
        fi
        cd "$VCPKG_DIR"
        ./bootstrap-vcpkg.sh

    - name: Install vcpkg dependencies
      shell: bash
      run: |
        cd "${{ github.workspace }}"
        "${{ github.workspace }}/vcpkg/vcpkg" install --triplet x64-linux



    - name: Configure for CodeQL
      shell: bash
      env:
        VCPKG_ROOT: ${{ github.workspace }}/vcpkg
      run: |
        VCPKG_TOOLCHAIN="${{ github.workspace }}/vcpkg/scripts/buildsystems/vcpkg.cmake"
        cmake --preset Release \
          -DCMAKE_BUILD_TYPE=Release \
          -DCMAKE_TOOLCHAIN_FILE="$VCPKG_TOOLCHAIN" \
          -DVCPKG_TARGET_TRIPLET=x64-linux \
          -DBUILD_TESTS=OFF

    - name: Build for CodeQL
      shell: bash
      run: cmake --build build/Release --parallel

    - name: Perform CodeQL Analysis
      uses: github/codeql-action/analyze@v3

  release:
    if: github.ref == 'refs/heads/main' && github.event_name == 'push'
    needs: [build-and-test, static-analysis, code-formatting]
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout repository
      uses: actions/checkout@v4
      with:
        fetch-depth: 0  # Fetch all history for proper versioning

    - name: Setup CMake
      uses: lukka/get-cmake@latest

    - name: Install system dependencies
      run: |
        sudo apt-get update
        sudo apt-get install -y \
          build-essential \
          pkg-config \
          libx11-dev \
          libxrandr-dev \
          libxinerama-dev \
          libxcursor-dev \
          libxi-dev \
          libgl1-mesa-dev \
          libglu1-mesa-dev \
          libasound2-dev \
          xorg-dev

    - name: Setup vcpkg
      shell: bash
      run: |
        VCPKG_DIR="${{ github.workspace }}/vcpkg"
        if [ ! -d "$VCPKG_DIR" ]; then
          git clone https://github.com/Microsoft/vcpkg.git "$VCPKG_DIR"
        fi
        cd "$VCPKG_DIR"
        ./bootstrap-vcpkg.sh

    - name: Install vcpkg dependencies
      shell: bash
      run: |
        cd "${{ github.workspace }}"
        "${{ github.workspace }}/vcpkg/vcpkg" install --triplet x64-linux



    - name: Configure release package
      shell: bash
      env:
        VCPKG_ROOT: ${{ github.workspace }}/vcpkg
      run: |
        VCPKG_TOOLCHAIN="${{ github.workspace }}/vcpkg/scripts/buildsystems/vcpkg.cmake"
        cmake --preset Release \
          -DCMAKE_BUILD_TYPE=Release \
          -DCMAKE_TOOLCHAIN_FILE="$VCPKG_TOOLCHAIN" \
          -DVCPKG_TARGET_TRIPLET=x64-linux \
          -DBUILD_TESTS=OFF

    - name: Build release package
      shell: bash
      run: cmake --build build/Release --parallel

    - name: Package release
      shell: bash
      run: |
        cd build/Release && cpack

    - name: Upload release artifacts
      uses: actions/upload-artifact@v4
      with:
        name: release-packages
        path: build/Release/*.tar.gz
