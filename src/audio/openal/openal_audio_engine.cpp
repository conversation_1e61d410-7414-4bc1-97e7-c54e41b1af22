#include "openal_audio_engine.hpp"

// C++ standard library
#include <iostream>
#include <memory>

// Suppress OpenAL deprecation warnings on macOS (OpenAL still works fine)
#ifdef __APPLE__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Wdeprecated-declarations"
#endif

namespace IronFrost {
  OpenALAudioEngine::OpenALAudioEngine()
    : IAudioEngine(), m_device(nullptr), m_context(nullptr),
      m_musicSource(0), m_musicVolume(1.0f), m_musicPlaying(false), m_musicPaused(false) {}

  OpenALAudioEngine::~OpenALAudioEngine() {
    shutdown();
  }

  bool OpenALAudioEngine::initialize() {
    std::cout << "Initializing OpenAL Audio Engine..." << std::endl;

    // Open the default audio device
    m_device = alcOpenDevice(nullptr);
    if (!m_device) {
      std::cerr << "Failed to open OpenAL device" << std::endl;
      return false;
    }

    m_context = alcCreateContext(m_device, nullptr);
    if (!m_context) {
      std::cerr << "Failed to create OpenAL context" << std::endl;
      alcCloseDevice(m_device);
      m_device = nullptr;
      return false;
    }

    if (!alcMakeContextCurrent(m_context)) {
      std::cerr << "Failed to make OpenAL context current" << std::endl;
      shutdown();
      return false;
    }

    if (!checkALCError(m_device, "Context creation")) {
      shutdown();
      return false;
    }

    std::cout << "OpenAL Vendor: " << alGetString(AL_VENDOR) << std::endl;
    std::cout << "OpenAL Version: " << alGetString(AL_VERSION) << std::endl;
    std::cout << "OpenAL Renderer: " << alGetString(AL_RENDERER) << std::endl;

    alGenSources(1, &m_musicSource);
    if (!checkALError("Generate music source")) {
      shutdown();
      return false;
    }

    std::cout << "Simplified OpenAL Audio Engine initialized successfully" << std::endl;
    return true;
  }

  void OpenALAudioEngine::shutdown() {
    std::cout << "Shutting down Simplified OpenAL Audio Engine..." << std::endl;

    stopMusic();

    if (m_musicSource != 0) {
      alDeleteSources(1, &m_musicSource);
      m_musicSource = 0;
    }

    for (auto& [id, buffer] : m_buffers) {
      alDeleteBuffers(1, &buffer.bufferId);
    }

    m_buffers.clear();
    m_audioClips.clear();

    if (m_context) {
      alcMakeContextCurrent(nullptr);
      alcDestroyContext(m_context);
      m_context = nullptr;
    }

    if (m_device) {
      alcCloseDevice(m_device);
      m_device = nullptr;
    }

    std::cout << "OpenAL Audio Engine shut down" << std::endl;
  }

  bool OpenALAudioEngine::loadAudioClip(const StringID& id, const AudioData& audioData) {
    if (isAudioClipLoaded(id)) {
      std::cout << "Audio clip already loaded: " << StringID::getString(id) << std::endl;
      return true;
    }

    try {
      ALuint bufferId;
      alGenBuffers(1, &bufferId);
      if (!checkALError("Generate buffer")) {
        return false;
      }

      ALenum format = getOpenALFormat(audioData.channels, 16);  // Always 16-bit from our conversion
      if (format == AL_NONE) {
        std::cerr << "Unsupported audio format: " << audioData.channels << " channels" << std::endl;
        alDeleteBuffers(1, &bufferId);
        return false;
      }

      std::vector<int16_t> samples16bit;
      samples16bit.reserve(audioData.samples.size());

      for (float sample : audioData.samples) {
        sample = std::max(-1.0f, std::min(1.0f, sample));
        samples16bit.push_back(static_cast<int16_t>(sample * 32767.0f));
      }

      alBufferData(bufferId, format, samples16bit.data(),
                   static_cast<ALsizei>(samples16bit.size() * sizeof(int16_t)),
                   static_cast<ALsizei>(audioData.sampleRate));

      if (!checkALError("Buffer data")) {
        alDeleteBuffers(1, &bufferId);
        return false;
      }

      auto audioClip = std::make_unique<AudioData>(audioData.samples, audioData.sampleRate, audioData.channels, audioData.duration);

      m_buffers[id] = {bufferId, id};
      m_audioClips[id] = std::move(audioClip);

      std::cout << "Loaded audio clip: " << StringID::getString(id) << std::endl;
      return true;

    } catch (const std::exception& e) {
      std::cerr << "Exception loading audio clip " << StringID::getString(id) << ": " << e.what() << std::endl;
      return false;
    }
  }

  void OpenALAudioEngine::unloadAudioClip(const StringID& id) {
    auto bufferIt = m_buffers.find(id);
    if (bufferIt != m_buffers.end()) {
      alDeleteBuffers(1, &bufferIt->second.bufferId);
      m_buffers.erase(bufferIt);
    }

    auto clipIt = m_audioClips.find(id);
    if (clipIt != m_audioClips.end()) {
      m_audioClips.erase(clipIt);
    }

    std::cout << "Unloaded audio clip: " << StringID::getString(id) << std::endl;
  }

  void OpenALAudioEngine::playMusic(const StringID& clipId, bool looping) {
    stopMusic();

    auto bufferIt = m_buffers.find(clipId);
    if (bufferIt == m_buffers.end()) {
      std::cerr << "Audio clip not loaded: " << StringID::getString(clipId) << std::endl;
      return;
    }

    alSourcei(m_musicSource, AL_BUFFER, static_cast<ALint>(bufferIt->second.bufferId));
    alSourcei(m_musicSource, AL_LOOPING, looping ? AL_TRUE : AL_FALSE);
    alSourcef(m_musicSource, AL_GAIN, m_musicVolume);
    
    alSourcePlay(m_musicSource);
    
    if (checkALError("Play music")) {
      m_currentMusicClip = clipId;
      m_musicPlaying = true;
      m_musicPaused = false;
      std::cout << "Playing music: " << StringID::getString(clipId) << std::endl;
    }
  }

  void OpenALAudioEngine::stopMusic() {
    if (m_musicSource != 0) {
      alSourceStop(m_musicSource);
      checkALError("Stop music");
    }
    
    m_musicPlaying = false;
    m_musicPaused = false;
    m_currentMusicClip = StringID("");
  }

  void OpenALAudioEngine::pauseMusic() {
    if (m_musicPlaying && !m_musicPaused) {
      alSourcePause(m_musicSource);
      if (checkALError("Pause music")) {
        m_musicPaused = true;
      }
    }
  }

  void OpenALAudioEngine::resumeMusic() {
    if (m_musicPaused) {
      alSourcePlay(m_musicSource);
      if (checkALError("Resume music")) {
        m_musicPaused = false;
      }
    }
  }

  void OpenALAudioEngine::setMusicVolume(float volume) {
    m_musicVolume = std::max(0.0f, std::min(1.0f, volume));
    if (m_musicSource != 0) {
      alSourcef(m_musicSource, AL_GAIN, m_musicVolume);
      checkALError("Set music volume");
    }
  }

  float OpenALAudioEngine::getMusicVolume() const {
    return m_musicVolume;
  }

  bool OpenALAudioEngine::isMusicPlaying() const {
    if (m_musicSource == 0) return false;
    
    ALint state;
    alGetSourcei(m_musicSource, AL_SOURCE_STATE, &state);
    return (state == AL_PLAYING);
  }

  bool OpenALAudioEngine::isMusicPaused() const {
    if (m_musicSource == 0) return false;
    
    ALint state;
    alGetSourcei(m_musicSource, AL_SOURCE_STATE, &state);
    return (state == AL_PAUSED);
  }

  // Utility functions
  bool OpenALAudioEngine::checkALError(const std::string& operation) {
    ALenum error = alGetError();
    if (error != AL_NO_ERROR) {
      std::cerr << "OpenAL error in " << operation << ": " << error << std::endl;
      return false;
    }
    return true;
  }

  bool OpenALAudioEngine::checkALCError(ALCdevice* device, const std::string& operation) {
    ALCenum error = alcGetError(device);
    if (error != ALC_NO_ERROR) {
      std::cerr << "OpenAL context error in " << operation << ": " << error << std::endl;
      return false;
    }
    return true;
  }

  ALenum OpenALAudioEngine::getOpenALFormat(int channels, int bitsPerSample) {
    if (channels == 1) {
      return (bitsPerSample == 8) ? AL_FORMAT_MONO8 : AL_FORMAT_MONO16;
    } else if (channels == 2) {
      return (bitsPerSample == 8) ? AL_FORMAT_STEREO8 : AL_FORMAT_STEREO16;
    }
    return AL_NONE;  // Unsupported format
  }
}

#ifdef __APPLE__
#pragma clang diagnostic pop
#endif
