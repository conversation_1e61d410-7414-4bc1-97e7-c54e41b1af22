#ifndef __IF__OPENAL_AUDIO_ENGINE_HPP
#define __IF__OPENAL_AUDIO_ENGINE_HPP

// C++ standard library
#include <unordered_map>
#include <vector>

// Third-party libraries
#ifdef __APPLE__
#include <OpenAL/al.h>
#include <OpenAL/alc.h>
#else
#include <AL/al.h>
#include <AL/alc.h>
#endif

// Local includes
#include "../audio_engine.hpp"

namespace IronFrost {
  struct OpenALBuffer {
    ALuint bufferId;
    StringID clipId;
  };

  class OpenALAudioEngine : public IAudioEngine {
    private:
      ALCdevice* m_device;
      ALCcontext* m_context;

      std::unordered_map<StringID, OpenALBuffer> m_buffers;

      // Single music source (simplified)
      ALuint m_musicSource;
      StringID m_currentMusicClip;
      float m_musicVolume;
      bool m_musicPlaying;
      bool m_musicPaused;

      // OpenAL utility functions
      bool checkALError(const std::string& operation);
      bool checkALCError(ALCdevice* device, const std::string& operation);
      ALenum getOpenALFormat(int channels, int bitsPerSample);

    public:
      explicit OpenALAudioEngine();
      ~OpenALAudioEngine() override;

      // Core audio operations
      bool initialize() override;
      void shutdown() override;

      // Audio clip management
      bool loadAudioClip(const StringID& id, const AudioData& audioData) override;
      void unloadAudioClip(const StringID& id) override;

      // Simple music playback (one track at a time)
      void playMusic(const StringID& clipId, bool looping = false) override;
      void stopMusic() override;
      void pauseMusic() override;
      void resumeMusic() override;

      // Basic volume control (0.0 to 1.0)
      void setMusicVolume(float volume) override;
      float getMusicVolume() const override;

      // Simple state queries
      bool isMusicPlaying() const override;
      bool isMusicPaused() const override;
  };
}

#endif
