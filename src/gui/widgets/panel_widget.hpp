#ifndef __IF__PANEL_WIDGET_HPP
#define __IF__PANEL_WIDGET_HPP

// C++ standard library
#include <exception>
#include <functional>
#include <iostream>
#include <memory>
#include <string>
#include <unordered_map>
#include <utility>

// Local includes
#include "../../renderer/fallback_resources.hpp"
#include "../../renderer/gui/gui_primitives.hpp"
#include "../../renderer/gui/gui_layers.hpp"
#include "../../renderer/pixel_rect.hpp"
#include "mixins/colorable.hpp"
#include "mixins/borderable.hpp"
#include "widget.hpp"

namespace IronFrost {
  class PanelWidget : public Widget, public Colorable, public Borderable {
    private:
      std::unordered_map<StringID, std::unique_ptr<Widget>> m_widgets;
      bool m_renderBackground{false};

    public: 
      PanelWidget() : 
        Widget() 
      {} 

      PanelWidget(glm::vec2 _position, glm::vec2 _size) :
        Widget(_position, _size)
      {}

      bool isDirty() const override {
        bool dirty = Widget::isDirty();
        for (auto& [id, widget] : m_widgets) {
          dirty |= widget->isDirty();
        }
        return dirty;
      }

      void markDirty() override {
        Widget::markDirty();
        for (auto& [id, widget] : m_widgets) {
          widget->markDirty();
        }
      }

      void markClean() override {
        Widget::markClean();
        for (auto& [id, widget] : m_widgets) {
          widget->markClean();
        }
      }

      void update(float _deltaTime, GameContext& _gameContext) override {
        if (!isVisible()) return;

        Widget::update(_deltaTime, _gameContext);
        for (auto& [id, widget] : m_widgets) {
          widget->update(_deltaTime, _gameContext);
        }
      }

      void addWidget(const StringID& _id, std::unique_ptr<Widget> _widget) {
        m_widgets.try_emplace(_id, std::move(_widget));
        markDirty();
      }

      void addWidgetRaw(const StringID& _id, Widget* _widget) {
        m_widgets.try_emplace(_id, std::unique_ptr<Widget>(_widget));
        markDirty();
      }

      void removeWidget(const StringID& _id) {
        m_widgets.erase(_id);
        markDirty();
      }

      Widget& getWidget(const StringID& _id) const {
        auto it = m_widgets.find(_id);
        if (it != m_widgets.end()) {
            return *it->second.get();
        }
        throw std::runtime_error("Widget not found");
      }

      void traverseWidgets(std::function<void(Widget&)> _func) {
        for (auto& [id, widget] : m_widgets) {
          _func(*widget);
        }
      }

      std::string getType() const override {
        return "panel";
      }

      void setRenderBackground(bool renderBackground) {
        m_renderBackground = renderBackground;
        markDirty();
      }

      bool getRenderBackground() const {
        return m_renderBackground;
      }

      void build(IResourceManager& resourceManager, GUIPrimitiveBuilder& builder) override {
        const auto& panelPos = getPosition();
        const auto& panelSize = getSize();

        QuadBuilder quadBuilder(resourceManager);

        // Render background if enabled
        if (m_renderBackground) {
          const TextureHandle& fallbackTexture = resourceManager.get<TextureHandle>(FallbackResources::FALLBACK_TEXTURE_NAME);

          quadBuilder.setTransform(getPosition(), getSize(), getRotation())
            .setColor(Color(getColor(), getAlpha()))
            .setTexture(fallbackTexture, glm::vec4(0.0f, 0.0f, 1.0f, 1.0f))
            .setLayer(LAYER_BACKGROUND)
            .setBorder(getBorder(), getBorderColor())
            .build(builder);
        }

        builder.add(GUIPrimitives::ClipPush{
          .rect = glm::vec4(panelPos, panelSize)
        });

        builder.pushLayer();

        for (auto& [id, widget] : m_widgets) {
          const auto& childPos = widget->getPosition();
          const auto originalPos = childPos;

          widget->setPosition(panelPos + childPos);
          widget->build(resourceManager, builder);
          widget->setPosition(originalPos);
        }

        builder.popLayer();
        builder.add(GUIPrimitives::ClipPop{});
      }

      void traverse(std::function<void(Widget&)> func) override {
        func(*this);
        for (auto& [id, widget] : m_widgets) {
          widget->traverse(func);
        }
      }
  };
}

#endif
