#ifndef __IF__BORDERABLE_MIXIN_HPP
#define __IF__BORDERABLE_MIXIN_HPP

// Third-party libraries
#include <glm/glm.hpp>

// Local includes
#include "../../../renderer/color.hpp"

namespace IronFrost {
  class Borderable {
    private:
      glm::vec4 m_border{0.0f, 0.0f, 0.0f, 0.0f}; // {top, right, bottom, left} - CSS-style
      Color m_borderColor{0.0f, 0.0f, 0.0f, 1.0f};

    public:
      Borderable() = default;

      void setBorder(const glm::vec4& border, const Color& color) {
        m_border = border;
        m_borderColor = color;
      }

      const glm::vec4& getBorder() const {
        return m_border;
      }

      const Color& getBorderColor() const {
        return m_borderColor;
      }
  };
}

#endif
