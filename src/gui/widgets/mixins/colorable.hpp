#ifndef __IF__COLORABLE_MIXIN_HPP
#define __IF__COLORABLE_MIXIN_HPP

// Third-party libraries
#include <glm/glm.hpp>

namespace IronFrost {
  class Colorable {
    private:
      glm::vec3 m_color{1.0f, 1.0f, 1.0f};
      float m_alpha{1.0f};

    public:
      Colorable() = default;

      void setColor(const glm::vec3& _color) {
        m_color = _color;
      }

      const glm::vec3& getColor() const {
        return m_color;
      }

      void setAlpha(float _alpha) {
        m_alpha = _alpha;
      }

      const float& getAlpha() const {
        return m_alpha;
      }

  };
}

#endif
