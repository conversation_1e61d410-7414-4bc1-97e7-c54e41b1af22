#ifndef __IF__CALLBACKABLE_MIXIN_HPP
#define __IF__CALLBACKABLE_MIXIN_HPP

// C++ standard library
#include <functional>
#include <string>
#include <unordered_map>

namespace IronFrost {
  class Callbackable {
    private:
      std::unordered_map<std::string, std::function<void()>> m_callbacks;

    public:
      Callbackable() = default;

      void setCallback(const std::string& name, std::function<void()> callback) {
        m_callbacks[name] = callback;
      }

      void triggerCallback(const std::string& name) {
        auto it = m_callbacks.find(name);
        if (it != m_callbacks.end()) {
          it->second();
        }
      }
  };
}

#endif
