#ifndef __IF__TRANSFORMABLE_MIXIN_HPP
#define __IF__TRANSFORMABLE_MIXIN_HPP

// Third-party libraries
#include <glm/glm.hpp>
#include <glm/gtc/matrix_transform.hpp>

namespace IronFrost {
  class Transformable {
    private:
      glm::vec2 m_position{0.0f, 0.0f};
      glm::vec2 m_size{10.0f, 10.0f};
      float m_rotation{0.0f};

      bool m_dirty{true};

      glm::mat4 m_transform{1.0f};
    public:
      Transformable() = default;
      Transformable(const glm::vec2& _position, const glm::vec2& _size, float _rotation) : 
        m_position(_position), m_size(_size), m_rotation(_rotation) {}

      virtual bool isDirty() const {
        return m_dirty;
      }

      virtual void markDirty() {
        m_dirty = true;
      }

      virtual void markClean() {
        m_dirty = false;
      }

      void setPosition(const glm::vec2& _position) {
        m_position = _position;
        markDirty();
      }

      void setSize(const glm::vec2& _size) {
        m_size = _size;
        markDirty();
      }
      
      void setRotation(float _rotation) {
        m_rotation = _rotation;
        markDirty();
      }

      const glm::vec2& getPosition() const {
        return m_position;
      }

      const glm::vec2& getSize() const {
        return m_size;
      }

      float getRotation() const {
        return m_rotation;
      }

      const glm::mat4& getTransform() {
        if (!m_dirty)
          return m_transform;

        m_transform = glm::mat4(1.0f);
        m_transform = glm::translate(m_transform, glm::vec3(m_position + glm::vec2(m_size.x/2.0, m_size.y/2.0), 0.0f));
        m_transform = glm::rotate(m_transform, glm::radians(m_rotation), glm::vec3(0.0f, 0.0f, 1.0f));
        m_transform = glm::scale(m_transform, glm::vec3(m_size, 1.0f));

        return m_transform;
      }
  };
}

#endif
