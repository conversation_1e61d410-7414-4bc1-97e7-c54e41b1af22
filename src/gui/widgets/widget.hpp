#ifndef __IF__WIDGET_HPP
#define __IF__WIDGET_HPP

// C++ standard library
#include <string>

// Third-party libraries
#include <glm/glm.hpp>

// Local includes
#include "../../renderer/renderer.hpp"
#include "../../renderer/gui/gui_primitive_builder.hpp"
#include "../../utils/game_context.hpp"
#include "mixins/callbackable.hpp"
#include "mixins/transformable.hpp"

namespace IronFrost {
  class Widget : public Callbackable, public Transformable {
    private:
      bool m_visible{true};
      bool m_hovered{false};
      bool m_focused{false};
      bool m_pressed{false};
    
    public:
      Widget() = default;
      Widget(const glm::vec2& position, const glm::vec2& size);

      virtual ~Widget() = default;

      virtual void update(float deltaTime, GameContext& gameContext);

      bool isVisible() const;
      void setVisible(bool visible);
      bool isHovered() const;
      bool isFocused() const;
      bool isPressed() const;

      virtual std::string getType() const;

      virtual void build(IResourceManager& resourceManager, GUIPrimitiveBuilder& builder);

      virtual void traverse(std::function<void(Widget&)> func);
  };
}

#endif
