#ifndef __IF__GUI_HPP
#define __IF__GUI_HPP

// C++ standard library
#include <functional>
#include <memory>
#include <unordered_map>

// Local includes
#include "../utils/string_id.hpp"
#include "widgets/widget.hpp"

namespace IronFrost {
  struct GameContext;

  class GUI {
    private:
      std::unordered_map<StringID, std::unique_ptr<Widget>> m_widgets;
    public:
      GUI() = default;
      GUI(const GUI &) = delete;

      ~GUI() = default;

      void update(float deltaTime, GameContext& gameContext);

      void addWidget(const StringID& id, std::unique_ptr<Widget> widget);
      void addWidgetRaw(const StringID& id, Widget* widget);
      void removeWidget(const StringID& id);
      Widget& getWidget(const StringID& id) const;

      void traverseWidgets(const std::function<void(Widget &)>& func);

      void clearAllWidgets();
  };
}

#endif
