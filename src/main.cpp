// C++ standard library
#include <filesystem>
#include <iostream>
#include <memory>

// Local includes
#include "app/application.hpp"
#include "app/application_builder.hpp"

int main()
{
  using namespace IronFrost;

  #ifndef NDEBUG
    try
    {
      std::filesystem::path cwd = std::filesystem::current_path();
      std::cout << "Current working directory: " << cwd << '\n';
    }
    catch (const std::filesystem::filesystem_error &e)
    {
      std::cerr << "Error retrieving current working directory: " << e.what() << '\n';
    }
  #endif

  auto application = ApplicationBuilder()
    .withWindow(1280, 720, "IronFrost")
    .withRenderer(RENDERER_TYPE::OPENGL)
    .withScriptEngine(SCRIPT_ENGINE_TYPE::LUA)
    .withScenes("scenes/config.json", "default")
    .build();

  application->run();

  return 0;
}
