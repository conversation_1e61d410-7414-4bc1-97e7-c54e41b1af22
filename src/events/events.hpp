#ifndef __IF__EVENTS_HPP
#define __IF__EVENTS_HPP

// C++ standard library
#include <functional>
#include <optional>
#include <string>

namespace IronFrost
{
  class Event {
    private:
    public:
      virtual ~Event() = default;
      virtual std::string toString() const = 0;
  };

  class EventWithCallback : public Event {
    private:
      std::optional<std::function<void()>> m_callback;

    public:
      explicit EventWithCallback(std::optional<std::function<void()>> callback = std::nullopt) : 
        m_callback(callback) 
      {}

      void callCallback() const {
        if (m_callback) {
          (*m_callback)();
        }
      }
  };

  class SceneUpdateEvent : public Event {
    public:
      std::string toString() const override {
        return "SceneUpdateEvent";
      }
  };

  class SceneLoadedEvent : public Event {
    private:
      void *m_scene;

    public:
      explicit SceneLoadedEvent(void *scene) : m_scene(scene) {}

      std::string toString() const override {
        return "SceneLoadedEvent";
      }

      void *getScene() const {
        return m_scene;
      }
  };

  class SceneUnloadedEvent : public Event {
    public:
      std::string toString() const override {
        return "SceneUnloadedEvent";
      }
  };

  class ConsoleOpenEvent : public Event {
    public:
      std::string toString() const override {
        return "ConsoleOpenEvent";
      }
  };

  class ConsoleCloseEvent : public Event {
    public:
      std::string toString() const override {
        return "ConsoleCloseEvent";
      }
  };

  class ConsoleCommandEvent : public Event {
    private:
      std::string m_command;

    public:
      explicit ConsoleCommandEvent(const std::string& command) : m_command(command) {}

      std::string toString() const override {
        return "ConsoleCommandEvent";
      }

      const std::string& getCommand() const {
        return m_command;
      }
  };

  class ConsoleInputBufferEvent : public Event {
    private:
      std::string m_input;

    public:
      explicit ConsoleInputBufferEvent(const std::string& input) : m_input(input) {}

      std::string toString() const override {
        return "ConsoleInputBufferEvent";
      }

      const std::string& getInput() const {
        return m_input;
      }
  };

  class ConsoleLogEvent : public Event {
    private:
      std::string m_log;

    public:
      explicit ConsoleLogEvent(const std::string& log) : m_log(log) {}

      std::string toString() const override {
        return "ConsoleLogEvent";
      }

      const std::string& getLog() const {
        return m_log;
      }
  };

  class WindowCloseEvent : public Event {
    public:
      std::string toString() const override {
        return "WindowCloseEvent";
      }
  };

  class WindowResizeEvent : public Event {
    private:
      int m_width;
      int m_height;

    public:
      explicit WindowResizeEvent(int width, int height) : m_width(width), m_height(height) {}

      std::string toString() const override {
        return "WindowResizeEvent";
      }

      int getWidth() const {
        return m_width;
      }

      int getHeight() const {
        return m_height;
      }

      float getAspectRatio() const {
        return static_cast<float>(m_width) / static_cast<float>(m_height);
      }
  };
}

#endif
