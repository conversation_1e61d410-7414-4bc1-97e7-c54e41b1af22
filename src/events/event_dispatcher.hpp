#ifndef __IF__EVENT_DISPATCHER_HPP
#define __IF__EVENT_DISPATCHER_HPP

// C++ standard library
#include <functional>
#include <iostream>
#include <memory>
#include <mutex>
#include <typeindex>
#include <unordered_map>
#include <utility>
#include <vector>

// Local includes
#include "../services/service.hpp"
#include "event_queue.hpp"
#include "events.hpp"

namespace IronFrost {
  struct EventListenerHandle {
    std::type_index eventType{typeid(Event)};
    unsigned long listenerId;
  };

  class EventDispatcher : public IService {
    private:
      unsigned long m_nextListenerId{0};

      std::unordered_map<std::type_index, std::unordered_map<unsigned long, std::function<void(const Event &)>>> m_listeners;
      std::mutex m_mutex;

      EventQueue m_eventQueue;

    public:
      ~EventDispatcher() {
        m_listeners.clear();
      }

      template <typename EventType>
      EventListenerHandle registerListener(std::function<void(const EventType &)> listener) {
        unsigned long id = m_nextListenerId++;
        auto &listenerMap = m_listeners[typeid(EventType)];

        listenerMap[id] = [listener](const Event &event) {
          const EventType* typedEvent = dynamic_cast<const EventType*>(&event);
          if (typedEvent) {
            listener(*typedEvent);
          } else {
            // This should never happen if our type system is correct
            std::cerr << "EventDispatcher: Failed to cast event to expected type" << std::endl;
          }
        };

        return EventListenerHandle { typeid(EventType), id };
      }

      template <typename EventType>
      void unregisterListener(EventListenerHandle handle) {
        auto& listenerMap = m_listeners[typeid(EventType)];
        listenerMap.erase(handle.listenerId);
      }

      template <typename EventType, typename... Args>
      void dispatch(Args&&... args) {
        auto event = std::make_shared<EventType>(std::forward<Args>(args)...);

        auto it = m_listeners.find(typeid(EventType));
        if (it != m_listeners.end()) {
          for (const auto& [id, listener] : it->second) {
            listener(*event);
          }
        }
      }

      template <typename EventType, typename... Args>
      void dispatchAsync(Args&&... args) {
        auto event = std::make_shared<EventType>(std::forward<Args>(args)...);
        {
          std::lock_guard<std::mutex> lock(m_mutex); 
          m_eventQueue.push(event);
        }
      }

      void processEvents() {
        while (!m_eventQueue.empty()) {
          auto event = m_eventQueue.pop();

          const auto& eventRef = *event;
          const auto eventType = std::type_index(typeid(eventRef));

          auto it = m_listeners.find(eventType);
          if (it != m_listeners.end()) {
            for (const auto& [id, listener] : it->second) {
              listener(*event);
            }
          }
        }
      }
  };
}

#endif
