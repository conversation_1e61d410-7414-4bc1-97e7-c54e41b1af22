#ifndef __IF__EVENT_QUEUE_HPP
#define __IF__EVENT_QUEUE_HPP

// C++ standard library
#include <condition_variable>
#include <functional>
#include <memory>
#include <mutex>
#include <queue>

// Local includes
#include "events.hpp"

namespace IronFrost {
  class EventQueue {
    private:
      std::queue<std::shared_ptr<Event>> m_events;
      std::mutex m_mutex;
      std::condition_variable m_cond;

    public:
      void push(std::shared_ptr<Event> event) {
        std::lock_guard<std::mutex> lock(m_mutex);

        m_events.push(event);
        m_cond.notify_one();
      }

      std::shared_ptr<Event> pop() {
        std::unique_lock<std::mutex> lock(m_mutex);

        m_cond.wait(lock, [this] { return !m_events.empty(); });

        auto event = m_events.front();
        m_events.pop();

        return event;
      }

      bool empty() {
        std::lock_guard<std::mutex> lock(m_mutex);

        return m_events.empty();
      }
  };
}

#endif
