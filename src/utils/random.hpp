#ifndef __IF__RANDOM_HPP
#define __IF__RANDOM_HPP

// C++ standard library
#include <chrono>
#include <random>

namespace IronFrost {
  class Random {
    private: 
      std::mt19937 m_gen;

    public:
      Random() : m_gen(static_cast<unsigned int>(std::chrono::system_clock::now().time_since_epoch().count())) {}

      int getInt(int min, int max) {
        std::uniform_int_distribution<int> dist(min, max);
        return dist(m_gen);
      }

      double getDouble(double min, double max) {
        std::uniform_real_distribution<double> dist(min, max);
        return dist(m_gen);
      }
  };
}

#endif
