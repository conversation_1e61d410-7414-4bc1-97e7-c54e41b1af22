#ifndef __IF__COLLISION_MATH_HPP
#define __IF__COLLISION_MATH_HPP

// Third-party libraries
#include <glm/glm.hpp>
#define GLM_ENABLE_EXPERIMENTAL
#include <glm/gtx/norm.hpp>

// C++ standard library
#include <array>
#include <cfloat>
#include <optional>

// Local includes
#include "hash.hpp"

#include "collision_math/plane.hpp"
#include "collision_math/ray.hpp"
#include "collision_math/aabb.hpp"
#include "collision_math/sphere.hpp"

namespace IronFrost {
  namespace CollisionMath {
    struct SweepHit {
      float time{1.0f};
      glm::vec3 normal{0.0f, 1.0f, 0.0f};
      glm::vec3 position{0.0f};

      bool startsOverlapping{false};
    };

    struct RayAABBHit {
      float tEntry{0.0f};
      float tExit{0.0f};

      int enterAxis{0};
    };

    struct RaySphereHit {
      float tEntry{0.0f};
      float tExit{0.0f};
    };

    /**
     * Ray-plane intersection
     * Returns the parameter t where intersection point = ray.origin + t * ray.direction
     * Returns nullopt if ray is parallel to plane or intersection is behind ray origin
     */
    inline std::optional<float> rayPlaneIntersection(const Ray& ray, const Plane& plane) {
      float denominator = glm::dot(ray.direction, plane.normal);

      // Ray is parallel to plane
      if (std::abs(denominator) < 1e-6f) {
        return std::nullopt;
      }

      float t = (plane.distance - glm::dot(ray.origin, plane.normal)) / denominator;

      // Intersection is behind ray origin
      if (t < 0.0f) {
        return std::nullopt;
      }

      return t;
    }

    inline std::optional<RayAABBHit> rayAABBSegmentIntersection(const Ray& ray, float maxDistance, const AABB& aabb) {
      // Early exit for invalid ray segment
      if (maxDistance <= 0.0f) return std::nullopt;
      
      constexpr float epsilon = 1e-8f;
      // Track entry/exit times and which axis we entered through
      float entryTime = 0.0f, exitTime = maxDistance;
      int entryAxis = -1;

      // Test intersection with each axis-aligned slab (X, Y, Z)
      for (int axisIndex = 0; axisIndex < 3; ++axisIndex) {
        // Ray parallel to slab - check if ray origin is within slab bounds
        if (std::abs(ray.direction[axisIndex]) < epsilon) {
          if (ray.origin[axisIndex] < aabb.min[axisIndex] || ray.origin[axisIndex] > aabb.max[axisIndex]) {
            return std::nullopt; // Ray misses AABB entirely
          }
          continue; // Ray is within slab, check next axis
        }

        // Calculate intersection times with near and far slab planes
        float inverseDirection = 1.0f / ray.direction[axisIndex];
        float nearPlaneTime = (aabb.min[axisIndex] - ray.origin[axisIndex]) * inverseDirection; // Near plane
        float farPlaneTime = (aabb.max[axisIndex] - ray.origin[axisIndex]) * inverseDirection; // Far plane

        // Ensure nearPlaneTime is near, farPlaneTime is far (swap if ray direction is negative)
        if (nearPlaneTime > farPlaneTime) std::swap(nearPlaneTime, farPlaneTime);

        // Update entry time and track which axis we entered through
        if (nearPlaneTime > entryTime) {
          entryTime = nearPlaneTime;
          entryAxis = axisIndex;
        }
        // Update exit time (intersection ends when we exit any slab)
        exitTime = std::min(exitTime, farPlaneTime);

        // No intersection if entry > exit or if intersection is behind ray
        if (entryTime > exitTime || exitTime < 0.0f) {
          return std::nullopt;
        }
      }

      // Clamp entry time to ray start (handle rays starting inside AABB)
      entryTime = std::max(entryTime, 0.0f);
      // Normalize times to [0,1] range based on maxDistance
      float inverseMaxDistance = 1.0f / maxDistance;
      
      return RayAABBHit{entryTime * inverseMaxDistance, exitTime * inverseMaxDistance, entryAxis};
    }

    inline std::optional<RaySphereHit> raySphereSegmentIntersection(const Ray& ray, float maxDistance, const Sphere& sphere) {
      const glm::vec3 rayToSphere = ray.origin - sphere.center;
      const float rayDotOffset = glm::dot(rayToSphere, ray.direction);
      const float radiusSquared = sphere.radius * sphere.radius;
      const float offsetLengthSquared = glm::dot(rayToSphere, rayToSphere);

      // Ray starts inside sphere
      if (offsetLengthSquared <= radiusSquared) {
        return RaySphereHit{0.0f, 0.0f};
      }

      // Calculate discriminant
      const float discriminant = rayDotOffset * rayDotOffset - (offsetLengthSquared - radiusSquared);
      if (discriminant < 0.0f) return std::nullopt;

      // Calculate intersection times
      const float sqrtDiscriminant = std::sqrt(discriminant);
      float entryTime = -rayDotOffset - sqrtDiscriminant;
      float exitTime = -rayDotOffset + sqrtDiscriminant;

      // Both intersections behind ray or entry beyond max distance
      if (exitTime < 0.0f || entryTime > maxDistance) return std::nullopt;

      // Clamp to valid range
      return RaySphereHit{std::max(entryTime, 0.0f), std::min(exitTime, maxDistance)};
    }

    /**
     * Check if a sphere intersects with a plane
     * Returns true if the sphere intersects or is below the plane
     */
    inline bool spherePlaneIntersection(const Sphere& sphere, const Plane& plane) {
      float distance = distanceToPlane(sphere.center, plane);
      return distance <= sphere.radius;
    }

    /**
     * Check if an AABB intersects with a plane
     */
    inline bool aabbPlaneIntersection(const AABB& aabb, const Plane& plane) {
      glm::vec3 center   = aabb.getCenter();
      glm::vec3 halfSize = (aabb.max - aabb.min) * 0.5f;

      float d = distanceToPlane(center, plane);
      float r = glm::dot(glm::abs(plane.normal), halfSize);

      return std::abs(d) <= r;
    }

    /**
     * Check if two planes intersect
     * Returns true unless they are parallel and different planes
     */
    inline bool planePlaneIntersection(const Plane& planeA, const Plane& planeB) {
      // Two planes intersect unless they are parallel
      // Check if normals are parallel (cross product is zero)
      glm::vec3 cross = glm::cross(planeA.normal, planeB.normal);
      float crossLength = glm::length(cross);

      // If cross product is very small, planes are parallel
      constexpr float epsilon = 1e-6f;
      if (crossLength < epsilon) {
        // Parallel planes intersect only if they are the same plane
        return std::abs(planeA.distance - planeB.distance) < epsilon;
      }

      // Non-parallel planes always intersect
      return true;
    }

    /**
     * Check if a sphere intersects with an AABB
     */
    inline bool sphereAABBIntersection(const Sphere& sphere, const AABB& aabb) {
      float distance = distanceToAABB(sphere.center, aabb);
      return distance <= sphere.radius;
    }

    /**
     * Get the penetration depth of a sphere into a plane
     * Returns positive value if sphere is penetrating the plane
     */
    inline float spherePlanePenetration(const Sphere& sphere, const Plane& plane) {
      float distance = distanceToPlane(sphere.center, plane);
      if (distance < sphere.radius) {
        return sphere.radius - distance;
      }
      return 0.0f;
    }

    inline float sphereSpherePenetration(const Sphere& sphereA, const Sphere& sphereB) {
      float distance = glm::length(sphereA.center - sphereB.center);
      float radiusSum = sphereA.radius + sphereB.radius;
      return (distance < radiusSum) ? radiusSum - distance : 0.0f;
    }

    inline float sphereAABBPenetration(const Sphere& sphere, const AABB& aabb) {
      float distance = distanceToAABB(sphere.center, aabb);
      return (distance < sphere.radius) ? sphere.radius - distance : 0.0f;
    }

    /**
     * Resolve sphere-plane collision by moving the sphere out of the plane
     * Returns the corrected sphere center position
     */
    inline glm::vec3 resolveSpherePlaneCollision(const Sphere& sphere, const Plane& plane) {
      float penetration = spherePlanePenetration(sphere, plane);
      if (penetration > 0.0f) {
        return sphere.center + plane.normal * penetration;
      }
      return sphere.center;
    }

    inline std::optional<SweepHit> sweepSpherePlaneCollision(const Sphere& sphere, const Plane& plane, const glm::vec3& delta) {
      constexpr float epsilon = 1e-8f;

      // Assume plane is already normalized, skip normalization for optimization
      const glm::vec3& planeNormal = plane.normal;
      float planeDistance = plane.distance;

      // Signed distance from sphere center to plane
      float initialDistance = glm::dot(sphere.center, planeNormal) - planeDistance;

      // Initial overlap check
      if (std::abs(initialDistance) <= sphere.radius) {
        glm::vec3 normal = (initialDistance >= 0.0f) ? planeNormal : -planeNormal;
        return SweepHit{0.0f, normal, sphere.center - normal * sphere.radius, true};
      }

      // Early exit for zero movement
      float movementLengthSquared = glm::length2(delta);
      if (movementLengthSquared < epsilon) return std::nullopt;

      float movementLength = std::sqrt(movementLengthSquared);
      glm::vec3 movementDirection = delta / movementLength;
      float velocityAlongNormal = glm::dot(movementDirection, planeNormal);
      
      // Parallel to plane check
      if (std::abs(velocityAlongNormal) < epsilon) return std::nullopt;

      // Calculate offset plane and hit normal
      float offsetPlaneDistance = (velocityAlongNormal < 0.0f) ? planeDistance + sphere.radius : planeDistance - sphere.radius;
      glm::vec3 hitNormal = (velocityAlongNormal < 0.0f) ? planeNormal : -planeNormal;

      // Ray-plane intersection
      auto intersectionDistance = rayPlaneIntersection(Ray{sphere.center, movementDirection}, Plane{planeNormal, offsetPlaneDistance});
      if (!intersectionDistance || *intersectionDistance > movementLength) return std::nullopt;

      float normalizedTime = *intersectionDistance / movementLength;
      return SweepHit{normalizedTime, hitNormal, sphere.center + normalizedTime * delta - hitNormal * sphere.radius};
    }

    /**
     * Resolve AABB-plane collision by moving the AABB out of the plane
     * Returns the corrected AABB center position
     */
    inline glm::vec3 resolveAABBPlaneCollision(const AABB& aabb, const Plane& plane) {
      // Find the closest point on the AABB to the plane
      glm::vec3 center = aabb.getCenter();
      glm::vec3 halfSize = (aabb.max - aabb.min) * 0.5f;

      // Calculate the distance from AABB center to plane
      float centerDistance = distanceToPlane(center, plane);

      // Calculate how far the AABB extends towards the plane
      float aabbExtent = glm::dot(glm::abs(plane.normal), halfSize);

      // If the AABB is penetrating the plane, push it out
      if (centerDistance < aabbExtent) {
        float penetration = aabbExtent - centerDistance;
        return center + plane.normal * (penetration + 1e-4f); // Small epsilon to avoid floating point issues
      }

      return center;
    }

    inline glm::vec3 resolveSphereSphereCollision(const Sphere& sphereA, const Sphere& sphereB) {
      constexpr float epsilon = 1e-4f;

      // Calculate separation vector from B to A
      glm::vec3 separationVector = sphereA.center - sphereB.center;
      float distanceBetweenCenters = glm::length(separationVector);
      float combinedRadius = sphereA.radius + sphereB.radius;

      // No collision if spheres don't overlap
      if (distanceBetweenCenters >= combinedRadius) {
        return sphereA.center; // No collision, return original position
      }

      // Normal collision case - spheres are separated
      if (distanceBetweenCenters > epsilon) {
        float penetrationDepth = combinedRadius - distanceBetweenCenters;
        glm::vec3 pushDirection = separationVector / distanceBetweenCenters;
        return sphereA.center + pushDirection * (penetrationDepth + epsilon);
      } else {
        // Special case: sphere centers are coincident - push along arbitrary axis
        return sphereA.center + glm::vec3(combinedRadius + epsilon, 0.0f, 0.0f);
      }
    }

    inline glm::vec3 resolveSphereAABBCollision(const Sphere& sphere, const AABB& aabb) {
      constexpr float epsilon = 1e-4f;

      // Find the closest point on AABB surface to sphere center
      glm::vec3 closestPoint = closestPointOnAABB(sphere.center, aabb);
      glm::vec3 sphereToClosestPoint = sphere.center - closestPoint;
      float distanceToClosestPoint = glm::length(sphereToClosestPoint);

      // No collision if sphere doesn't reach AABB
      if (distanceToClosestPoint >= sphere.radius) {
        return sphere.center; // No collision, return original position
      }

      // Normal collision case - sphere touching AABB surface
      if (distanceToClosestPoint > epsilon) {
        float penetrationDepth = sphere.radius - distanceToClosestPoint;
        glm::vec3 pushDirection = sphereToClosestPoint / distanceToClosestPoint;
        return sphere.center + pushDirection * (penetrationDepth + epsilon);
      }

      // Special case: sphere center inside AABB - push to nearest face
      glm::vec3 aabbCenter = aabb.getCenter();
      glm::vec3 sphereRelativeToCenter = sphere.center - aabbCenter;
      glm::vec3 aabbHalfExtents = (aabb.max - aabb.min) * 0.5f;
      glm::vec3 distanceToFaces = aabbHalfExtents - glm::abs(sphereRelativeToCenter);

      // Find the nearest face (axis with minimum distance)
      int nearestFaceAxis = (distanceToFaces.x <= distanceToFaces.y && distanceToFaces.x <= distanceToFaces.z) ? 0 : 
                           (distanceToFaces.y <= distanceToFaces.z) ? 1 : 2;
      
      glm::vec3 pushDirection(0.0f);
      pushDirection[nearestFaceAxis] = (sphereRelativeToCenter[nearestFaceAxis] >= 0.0f) ? 1.0f : -1.0f;
      
      return sphere.center + pushDirection * (sphere.radius + epsilon);
    }

    inline std::optional<SweepHit> sweepSphereAABBCollision(const Sphere& sphere, const AABB& aabb, const glm::vec3& delta) {
      // Expand AABB by sphere radius to reduce sphere-AABB to point-AABB collision
      const AABB sphereInflatedAABB = inflateAABB(aabb, sphere.radius);

      // Check if sphere is already overlapping AABB at start
      if (isPointInAABB(sphere.center, sphereInflatedAABB)) {
        glm::vec3 separationNormal = insideAABBOutwardNormal(sphereInflatedAABB, sphere.center);
        return SweepHit{0.0f, separationNormal, sphere.center - separationNormal * sphere.radius, true};
      }

      // Early exit for zero movement
      const float movementLengthSquared = glm::length2(delta);
      if (movementLengthSquared < 1e-12f) return std::nullopt;

      // Cast ray from sphere center along movement direction
      const float movementLength = std::sqrt(movementLengthSquared);
      glm::vec3 movementDirection = delta / movementLength;
      auto rayHitResult = rayAABBSegmentIntersection(Ray(sphere.center, movementDirection), movementLength, sphereInflatedAABB);
      if (!rayHitResult) return std::nullopt;

      // Calculate collision normal based on which AABB face was hit
      glm::vec3 collisionNormal{0.0f};
      collisionNormal[rayHitResult->enterAxis] = (delta[rayHitResult->enterAxis] > 0.0f) ? -1.0f : 1.0f;
      
      // Calculate final collision time and contact point
      float collisionTime = rayHitResult->tEntry;
      glm::vec3 sphereCenterAtCollision = sphere.center + collisionTime * delta;
      glm::vec3 contactPoint = sphereCenterAtCollision - collisionNormal * sphere.radius;
      
      return SweepHit{collisionTime, collisionNormal, contactPoint};
    }

    inline glm::vec3 resolveAABBSphereCollision(const AABB& aabb, const Sphere& sphere) {
      constexpr float epsilon = 1e-4f;

      // Find closest point on AABB surface to sphere center
      glm::vec3 closestPoint = closestPointOnAABB(sphere.center, aabb);
      glm::vec3 sphereToClosestPoint = closestPoint - sphere.center;
      float distanceToClosestPoint = glm::length(sphereToClosestPoint);
      
      glm::vec3 aabbCenter = aabb.getCenter();
      
      // No collision if sphere doesn't reach AABB
      if (distanceToClosestPoint >= sphere.radius) {
        return aabbCenter; // No collision, return original position
      }

      // Normal collision case - sphere touching AABB surface
      if (distanceToClosestPoint > epsilon) {
        float penetrationDepth = sphere.radius - distanceToClosestPoint;
        glm::vec3 pushDirection = sphereToClosestPoint / distanceToClosestPoint;
        return aabbCenter + pushDirection * (penetrationDepth + epsilon);
      }

      // Special case: sphere center inside AABB - push along dominant axis
      glm::vec3 centerToCenter = aabbCenter - sphere.center;
      glm::vec3 absoluteCenterDistance = glm::abs(centerToCenter);
      
      // Find the axis with maximum separation (easiest escape route)
      int dominantAxis = (absoluteCenterDistance.x >= absoluteCenterDistance.y && absoluteCenterDistance.x >= absoluteCenterDistance.z) ? 0 : 
                        (absoluteCenterDistance.y >= absoluteCenterDistance.z) ? 1 : 2;
      
      glm::vec3 pushDirection(0.0f);
      pushDirection[dominantAxis] = (centerToCenter[dominantAxis] >= 0.0f) ? 1.0f : -1.0f;
      
      return aabbCenter + pushDirection * (sphere.radius + epsilon);
    }

    inline glm::vec3 resolveAABBAABBCollision(const AABB& aabbA, const AABB& aabbB) {
      constexpr float epsilon = 1e-4f;

      // Get centers and calculate separation vector
      glm::vec3 centerA = aabbA.getCenter();
      glm::vec3 centerB = aabbB.getCenter();
      glm::vec3 separationVector = centerA - centerB;
      
      // Calculate half-extents (distance from center to edge)
      glm::vec3 halfExtentsA = (aabbA.max - aabbA.min) * 0.5f;
      glm::vec3 halfExtentsB = (aabbB.max - aabbB.min) * 0.5f;
      
      // Calculate overlap on each axis (positive = overlapping)
      glm::vec3 overlapAmounts = halfExtentsA + halfExtentsB - glm::abs(separationVector);

      // No collision if any axis has no overlap
      if (overlapAmounts.x <= 0.0f || overlapAmounts.y <= 0.0f || overlapAmounts.z <= 0.0f) {
        return centerA; // No collision, return original position
      }

      // Find axis with minimum overlap (shortest separation distance)
      int minOverlapAxis = (overlapAmounts.x <= overlapAmounts.y && overlapAmounts.x <= overlapAmounts.z) ? 0 : 
                          (overlapAmounts.y <= overlapAmounts.z) ? 1 : 2;
      
      // Determine separation direction (push away from B)
      float separationDirection = (separationVector[minOverlapAxis] > 0.0f) ? 1.0f : 
                                 (separationVector[minOverlapAxis] < 0.0f ? -1.0f : 1.0f);
      
      // Move A along the minimum overlap axis to resolve collision
      centerA[minOverlapAxis] += separationDirection * (overlapAmounts[minOverlapAxis] + epsilon);
      
      return centerA;
    }
  } // namespace CollisionMath
} // namespace IronFrost


#endif
