#ifndef __IF__STRING_ID_HPP
#define __IF__STRING_ID_HPP

// C standard library
#include <cstddef>
#include <cstdint>

// C++ standard library
#include <atomic>
#include <functional>
#include <iomanip>
#include <mutex>
#include <set>
#include <sstream>
#include <string>
#include <unordered_map>

namespace IronFrost {
  class StringID {
    private:
      std::uint32_t m_id;

      static inline std::atomic<std::uint32_t> s_nextID{0};
      static inline std::unordered_map<std::string, std::uint32_t> s_stringToID;
      static inline std::unordered_map<std::uint32_t, std::string> s_idToString;

      static inline std::mutex s_mutex;

    public:
      StringID() : m_id(0) {}

      explicit StringID(const std::string& _string) {
        std::lock_guard<std::mutex> lock(s_mutex);

        auto it = s_stringToID.find(_string);
        if (it == s_stringToID.end()) {
          m_id = s_nextID.fetch_add(1, std::memory_order_relaxed);

          s_stringToID.emplace(_string, m_id);
          s_idToString.emplace(m_id, _string);
        } else {
          m_id = it->second;
        }
      }

      StringID(const StringID& _other) noexcept : m_id(_other.m_id) {}
      StringID(StringID&& _other) noexcept : m_id(_other.m_id) { _other.m_id = 0; }
      StringID& operator=(const StringID& _other) noexcept {
        m_id = _other.m_id;
        return *this;
      }

      bool operator==(const StringID& _other) const noexcept { return m_id == _other.m_id; }
      bool operator!=(const StringID& _other) const noexcept { return m_id != _other.m_id; }

      static std::string getString(const StringID& _id) {
        std::lock_guard<std::mutex> lock(s_mutex);
        return s_idToString.count(_id.m_id) ? s_idToString.at(_id.m_id) : std::string("");
      }

      std::string toString() const {
        std::stringstream ss;
        ss << "0x" << std::setw(8) << std::setfill('0') << std::hex << m_id;
        return ss.str();
      }

      friend struct std::hash<StringID>;

      friend std::ostream& operator<<(std::ostream& _stream, const StringID& _id) {
        return _stream << _id.toString();
      }
  };
}

namespace std {
  template <>
  struct hash<IronFrost::StringID> {
    std::size_t operator()(const IronFrost::StringID& _stringID) const noexcept
    {
      return std::hash<std::uint32_t>{}(_stringID.m_id);
    }
  };
}

#endif
