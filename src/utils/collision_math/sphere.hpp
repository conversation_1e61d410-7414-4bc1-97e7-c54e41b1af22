#ifndef __IF__COLLISION_MATH_SPHERE_HPP
#define __IF__COLLISION_MATH_SPHERE_HPP

// Third-party libraries
#include <glm/glm.hpp>

namespace IronFrost {
  namespace CollisionMath {
    /**
     * Represents a sphere in 3D space
     */
    struct Sphere {
      glm::vec3 center;
      float radius;

      // Default constructor
      Sphere() : center(0.0f), radius(0.0f) {}

      Sphere(const glm::vec3& center, float radius) : center(center), radius(radius) {}
    };

    // ============================================================================
    // SPHERE FUNCTIONS
    // ============================================================================

    /**
     * Check if two spheres intersect
     */
    inline bool sphereIntersection(const Sphere& a, const Sphere& b) {
      float distance = glm::length(a.center - b.center);
      return distance <= a.radius + b.radius;
    }

    /**
     * Transform a sphere by a matrix
     */
    inline Sphere transformSphere(const Sphere& sphere, const glm::mat4& transform) {
      glm::vec3 center = glm::vec3(transform * glm::vec4(sphere.center, 1.0f));

      glm::vec3 scale;
      scale.x = glm::length(glm::vec3(transform[0]));
      scale.y = glm::length(glm::vec3(transform[1]));
      scale.z = glm::length(glm::vec3(transform[2]));

      float maxScale = std::max(scale.x, std::max(scale.y, scale.z));

      return Sphere(center, sphere.radius * maxScale);
    }
  }
}

#endif
