#ifndef __IF__COLLISION_MATH_RAY_HPP
#define __IF__COLLISION_MATH_RAY_HPP

// Third-party libraries
#include <glm/glm.hpp>

namespace IronFrost {
  namespace CollisionMath {
    /**
     * Represents a ray in 3D space
     */
    struct Ray {
      glm::vec3 origin;
      glm::vec3 direction;  // Should be normalized

      Ray(const glm::vec3& origin, const glm::vec3& direction)
        : origin(origin), direction(glm::normalize(direction)) {}
    };
  }
}

#endif
