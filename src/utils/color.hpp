#ifndef __IF__COLOR_HPP
#define __IF__COLOR_HPP

// Third-party libraries
#include <glm/glm.hpp>

namespace IronFrost {
  class Color {
    private:
      glm::vec4 m_color;

    public:
      Color(float r, float g, float b, float a = 1.0f) {
        m_color = glm::vec4(r, g, b, a);
      }

      Color(glm::vec4 color) {
        m_color = color;
      }

      Color(glm::vec3 color) {
        m_color = glm::vec4(color, 1.0f);
      }

      Color(glm::vec3 color, float a) {
        m_color = glm::vec4(color, a);
      }

      const glm::vec4& getColor() const {
        return m_color;
      }

      float getR() const {
        return m_color.r;
      }

      float getG() const {
        return m_color.g;
      }

      float getB() const {
        return m_color.b;
      }

      glm::vec3 getRGB() const {
        return glm::vec3(m_color.r, m_color.g, m_color.b);
      }

      float getA() const {
        return m_color.a;
      }

      glm::vec4 getRGBA() const {
        return m_color;
      }
  };
}

#endif
