#ifndef __IF__DELTA_TIME_CALCULATOR_HPP
#define __IF__DELTA_TIME_CALCULATOR_HPP

// C++ standard library
#include <chrono>
#include <functional>

using Clock = std::chrono::high_resolution_clock;

namespace IronFrost {
  class DeltaTimeCalculator {
    private:
      Clock::time_point m_lastTime;

      int m_fps{0};
      int m_frameCount{0};
      float m_timeAccumulator{0.0f};

    public:
      DeltaTimeCalculator() : m_lastTime(Clock::now()) {}

      void update(const std::function<void(double)>& callback);

      int getFPS() const;
  };
}

#endif
