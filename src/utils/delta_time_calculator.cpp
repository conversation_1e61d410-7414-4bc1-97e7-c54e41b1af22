#include "delta_time_calculator.hpp"

// C++ standard library
#include <chrono>
#include <functional>
#include <iostream>

namespace IronFrost {
  void DeltaTimeCalculator::update(const std::function<void(double)>& callback) {
    auto currentTime = std::chrono::high_resolution_clock::now();

    std::chrono::duration<float> elapsedTime = currentTime - m_lastTime;
    float deltaTime = elapsedTime.count();

    m_lastTime = currentTime;

    m_frameCount++;
    m_timeAccumulator += deltaTime;

    if (m_timeAccumulator > 1.0F) {
      m_fps = m_frameCount;
      m_frameCount = 0;
      m_timeAccumulator = 0.0F;

      std::cout << "FPS: " << m_fps << '\n';
    }

    callback(deltaTime);
  }

  int DeltaTimeCalculator::getFPS() const {
    return m_fps;
  }
}
