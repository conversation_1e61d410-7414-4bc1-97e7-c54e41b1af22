// C++ standard library
#include <exception>

// Third-party libraries
#include <glm/glm.hpp>
#include <nlohmann/json.hpp>

namespace nlohmann {
  template <>
  struct adl_serializer<glm::vec4> {
    static void from_json(const json& j, glm::vec4& v) {
      if (!j.is_array() || j.size() != 4)
        throw std::runtime_error("Expected JSON array of size 4 for glm::vec4");

      v.x = j[0].get<float>();
      v.y = j[1].get<float>();
      v.z = j[2].get<float>();
      v.w = j[3].get<float>();
    }
  };

  template <>
  struct adl_serializer<glm::vec3> {
    static void from_json(const json& j, glm::vec3& v) {
      if (!j.is_array() || j.size() != 3)
        throw std::runtime_error("Expected JSON array of size 3 for glm::vec3");

      v.x = j[0].get<float>();
      v.y = j[1].get<float>();
      v.z = j[2].get<float>();
    }
  };

  template <>
  struct adl_serializer<glm::vec2> {
    static void from_json(const json& j, glm::vec2& v) {
      if (!j.is_array() || j.size() != 2)
        throw std::runtime_error("Expected JSON array of size 2 for glm::vec2");

      v.x = j[0].get<float>();
      v.y = j[1].get<float>();
    }
  };
}
