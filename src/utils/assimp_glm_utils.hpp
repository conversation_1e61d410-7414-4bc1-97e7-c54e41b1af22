#ifndef __IF__ASSIMP_GLM_UTILS_HPP
#define __IF__ASSIMP_GLM_UTILS_HPP

// Third-party libraries
#include <assimp/matrix4x4.h>
#include <glm/glm.hpp>

namespace IronFrost {
  glm::mat4 convertAssimpMatrix(const aiMatrix4x4& aiMat) {
    return glm::transpose(glm::mat4(
        aiMat.a1, aiMat.a2, aiMat.a3, aiMat.a4,
        aiMat.b1, aiMat.b2, aiMat.b3, aiMat.b4,
        aiMat.c1, aiMat.c2, aiMat.c3, aiMat.c4,
        aiMat.d1, aiMat.d2, aiMat.d3, aiMat.d4
    ));
  }

  glm::vec3 convertAssimpVector(const aiVector3D& aiVec) {
    return glm::vec3(aiVec.x, aiVec.y, aiVec.z);
  }
}

#endif
