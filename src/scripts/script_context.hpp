#ifndef __IF__SCRIPT_CONTEXT_HPP
#define __IF__SCRIPT_CONTEXT_HPP

// C++ standard library
#include <memory>
#include <string>
#include <vector>

namespace IronFrost {
  class IScene;

  /**
   * Abstract interface for script contexts.
   * Provides script execution capabilities without exposing the underlying
   * script engine implementation (Lua, Python, etc.).
   */
  class IScriptContext {
    public:
      virtual ~IScriptContext() = default;

      /**
       * Initialize the script context.
       */
      virtual void initialize() = 0;

      /**
       * Execute initialization scripts (run once).
       */
      virtual void executeInitScripts(const std::vector<std::string>& scriptContents) = 0;

      /**
       * Register per-frame scripts for efficient execution.
       */
      virtual void registerPerFrameScripts(const std::vector<std::string>& scriptContents) = 0;

      /**
       * Execute per-frame scripts with given deltaTime.
       */
      virtual void executePerFrame(float deltaTime) = 0;
  };

  /**
   * Abstract interface for global script contexts.
   * Handles application-level scripting.
   */
  class IGlobalScriptContext : public virtual IScriptContext {
    public:
      virtual ~IGlobalScriptContext() = default;

      /**
       * Set the current scene object for global scripts to access.
       */
      virtual void setCurrentScene(const void* scene) = 0;
  };

  /**
   * Abstract interface for scene script contexts.
   * Handles scene-specific scripting.
   */
  class ISceneScriptContext : public virtual IScriptContext {
    public:
      virtual ~ISceneScriptContext() = default;
  };

}

#endif
