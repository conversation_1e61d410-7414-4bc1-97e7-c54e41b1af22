#ifndef __IF__LUA_GLOBAL_SCRIPT_CONTEXT_HPP
#define __IF__LUA_GLOBAL_SCRIPT_CONTEXT_HPP

// Local includes
#include "lua_script_context.hpp"

namespace IronFrost {

  /**
   * Global Lua script context for application-level scripting.
   * Handles global script execution with application-wide variables and state.
   */
  class LuaGlobalScriptContext : public LuaScriptContext, public IGlobalScriptContext {
  public:
    LuaGlobalScriptContext(Application& application, LuaAPIBinder& apiBinder);

    // Implement IGlobalScriptContext interface
    void setCurrentScene(const void* scene) override;

  protected:
    void setupContextVariables() override;
    std::string getPrintPrefix() const override;
  };

}

#endif
