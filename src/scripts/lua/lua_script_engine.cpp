#include "lua_script_engine.hpp"

// C++ standard library
#include <iostream>
#include <string>

// Local includes
#include "../../app/application.hpp"
#include "../../events/event_dispatcher.hpp"
#include "../../scene/scene.hpp"
#include "../../services/service_locator.hpp"
#include "lua_global_script_context.hpp"
#include "lua_scene_script_context.hpp"

namespace IronFrost {
  LuaScriptEngine::LuaScriptEngine(Application& application) :
    IScriptEngine(application)
  {
    LuaAPIBinder apiBinder(application, m_taskManager);
    m_globalContext = std::make_unique<LuaGlobalScriptContext>(application, apiBinder);
    m_globalContext->initialize();
  }

  LuaScriptEngine::~LuaScriptEngine() {
    // Clear all Lua references before destruction to avoid accessing destroyed Lua state
    if (m_globalContext) {
      auto* luaGlobalContext = static_cast<LuaGlobalScriptContext*>(m_globalContext.get());
      luaGlobalContext->clearLuaReferences();
    }
    m_taskManager.clearQueue();
  }

  void LuaScriptEngine::executeScriptFile(const std::string& scriptPath) {
    const std::string scriptCode = m_application.getVFS().readFile(scriptPath);
    executeScriptCode(scriptCode);
  }

  void LuaScriptEngine::executeScriptCode(const std::string& scriptCode) {
    m_globalContext->executeInitScripts({scriptCode});
  }

  void LuaScriptEngine::executeInitScripts(const std::vector<std::string>& scriptContents) {
    std::cout << "Executing init scripts from config" << '\n';
    m_globalContext->executeInitScripts(scriptContents);
  }

  void LuaScriptEngine::registerPerFrameScripts(const std::vector<std::string>& scriptContents) {
    std::cout << "Registering per-frame scripts from config" << '\n';
    m_globalContext->registerPerFrameScripts(scriptContents);
  }

  void LuaScriptEngine::registerListeners() {
    auto& eventDispatcher = ServiceLocator::getService<EventDispatcher>();

    eventDispatcher.registerListener<SceneLoadedEvent>(
      [&](const SceneLoadedEvent &event) {
        auto *scene = static_cast<IScene *>(event.getScene());

        m_globalContext->setCurrentScene(scene);
      });

    eventDispatcher.registerListener<SceneUnloadedEvent>(
      [&](const SceneUnloadedEvent & /* event */) {
        // Clear the scene reference to prevent dangling pointer access
        m_globalContext->setCurrentScene(nullptr);
      });

    eventDispatcher.registerListener<ConsoleCommandEvent>(
      [&](const ConsoleCommandEvent &event) {
        const std::string& command = event.getCommand();
        eventDispatcher.dispatch<ConsoleLogEvent>(" > " + command);

        try {
          m_globalContext->executeInitScripts({command});
        } catch (const std::exception& e) {
          eventDispatcher.dispatch<ConsoleLogEvent>(e.what());
        }
      });
  }

  void LuaScriptEngine::executeRegisteredScripts(float deltaTime) {
    m_globalContext->executePerFrame(deltaTime);
  }

  std::unique_ptr<ISceneScriptContext> LuaScriptEngine::createSceneScriptContext(const IScene& scene) {
    LuaAPIBinder apiBinder(m_application, m_taskManager);
    auto context = std::make_unique<LuaSceneScriptContext>(m_application, apiBinder, scene);
    context->initialize();
    return context;
  }


}
