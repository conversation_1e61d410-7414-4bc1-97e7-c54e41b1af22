#ifndef __IF__LUA_SCRIPT_ENGINE_HPP
#define __IF__LUA_SCRIPT_ENGINE_HPP

// C++ standard library
#include <string>

// Third-party libraries
#include <sol/sol.hpp>

// Local includes
#include "lua_api_binder.hpp"
#include "../script_context.hpp"
#include "../script_engine.hpp"

namespace IronFrost {
  class LuaScriptEngine : public IScriptEngine {
    private:
      std::unique_ptr<IGlobalScriptContext> m_globalContext;

      void executeRegisteredScripts(float deltaTime) override;

    public:
      LuaScriptEngine(Application& application);
      ~LuaScriptEngine();

      void executeScriptFile(const std::string& scriptPath) override;
      void executeScriptCode(const std::string& scriptCode) override;

      // Override to use global context instead of base class implementation
      void executeInitScripts(const std::vector<std::string>& scriptContents) override;
      void registerPerFrameScripts(const std::vector<std::string>& scriptContents) override;

      void registerListeners() override;

      std::unique_ptr<ISceneScriptContext> createSceneScriptContext(const IScene& scene) override;
  };
}

#endif
