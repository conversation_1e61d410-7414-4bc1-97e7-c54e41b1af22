#ifndef __IF__CONSOLE_API_HPP
#define __IF__CONSOLE_API_HPP

// C++ standard library
#include <string>

// Third-party libraries
#include <sol/sol.hpp>

// Local includes
#include "../../../window/console/console.hpp"

namespace IronFrost {
  namespace API {
    void bindConsoleAPI(sol::state& luaState, Console& console) {
      luaState["CONSOLE"] = luaState.create_table_with(
          "isOpen", [&]() { 
            return console.isOpen(); 
          },
          "toggle", [&]() { 
            console.toggle(); 
          });
          
    }
  }
}
#endif
