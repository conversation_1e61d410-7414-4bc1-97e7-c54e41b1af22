#ifndef __IF__GLM_API_HPP
#define __IF__GLM_API_HPP

// C++ standard library
#include <string>

// Third-party libraries
#include <glm/glm.hpp>
#include <sol/sol.hpp>

namespace IronFrost
{
  namespace API
  {
    void bindGLMAPI(sol::state& luaState)
    {
      luaState.new_usertype<glm::vec2>(
        "vec2",
        sol::constructors<glm::vec2(), glm::vec2(float, float)>(),
        "x", &glm::vec2::x,
        "y", &glm::vec2::y,
        "toString", [](glm::vec2 &vec) -> std::string {
          return "vec2(" + std::to_string(vec.x) + ", " + std::to_string(vec.y) + ")";
        }
      );

      luaState.new_usertype<glm::vec3>(
        "vec3",
        sol::constructors<glm::vec3(), glm::vec3(float, float, float)>(),
        "x", &glm::vec3::x,
        "y", &glm::vec3::y,
        "z", &glm::vec3::z,
        "toString", [](glm::vec3 &vec) -> std::string {
          return "vec3(" + std::to_string(vec.x) + ", " + std::to_string(vec.y) + ", " + std::to_string(vec.z) + ")";
        }
      );
        
      luaState.new_usertype<glm::vec4>(
        "vec4",
        sol::constructors<glm::vec4(), glm::vec4(float, float, float, float)>(),
        "x", &glm::vec4::x,
        "y", &glm::vec4::y,
        "z", &glm::vec4::z,
        "w", &glm::vec4::w,
        "toString", [](glm::vec4 &vec) -> std::string {
          return "vec4(" + std::to_string(vec.x) + ", " + std::to_string(vec.y) + ", " + std::to_string(vec.z) + ", " + std::to_string(vec.w) + ")";
        }
      );

      luaState.new_usertype<glm::mat4>(
        "mat4",
        sol::constructors<glm::mat4(), glm::mat4(float, float, float, float, float, float, float, float, float, float, float, float, float, float, float, float)>());
    }
  }
}

#endif
