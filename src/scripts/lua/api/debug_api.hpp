#ifndef __IF__DEBUG_API_HPP
#define __IF__DEBUG_API_HPP

// Third-party libraries
#include <sol/sol.hpp>

// Local includes
#include "../../../app/config/debug_config.hpp"

namespace IronFrost {
  namespace API {
    void bindDebugAPI(sol::state& luaState, DebugConfig& debugConfig) {
      luaState.new_enum<DebugFeature>("DebugFeature", {
        {"BOUNDING_BOXES", DebugFeature::BoundingBoxes},
      });

      luaState["DEBUG"] = luaState.create_table_with(
        "enable", [&](DebugFeature feature) { 
          debugConfig.enable(feature); 
        },
        "disable", [&](DebugFeature feature) { 
          debugConfig.disable(feature); 
        },
        "toggle", [&](DebugFeature feature) { 
          debugConfig.toggle(feature); 
        },
        "isEnabled", [&](DebugFeature feature) {
          return debugConfig.isEnabled(feature);
        });
    }
  }
}

#endif
