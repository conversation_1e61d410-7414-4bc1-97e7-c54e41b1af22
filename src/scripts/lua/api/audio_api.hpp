#ifndef __IF__AUDIO_API_HPP
#define __IF__AUDIO_API_HPP

// C++ standard library
#include <string>

// Third-party libraries
#include <sol/sol.hpp>

// Local includes
#include "../../../audio/audio_engine.hpp"
#include "../../../utils/string_id.hpp"

namespace IronFrost {
  namespace API {
    void bindAudioAPI(sol::state& luaState, IAudioEngine& audioEngine) {
      luaState["Audio"] = luaState.create_table_with(
          // Music playback controls
          "playMusic", sol::overload(
            [&](const std::string& clipName, bool looping) {
              audioEngine.playMusic(StringID(clipName), looping);
            },
            [&](const std::string& clipName) {
              audioEngine.playMusic(StringID(clipName), false);
            }
          ),
          "stopMusic", [&]() { 
            audioEngine.stopMusic(); 
          },
          "pauseMusic", [&]() { 
            audioEngine.pauseMusic(); 
          },
          "resumeMusic", [&]() { 
            audioEngine.resumeMusic(); 
          },
          
          // Volume controls (0.0 to 1.0)
          "setMusicVolume", [&](float volume) { 
            audioEngine.setMusicVolume(volume); 
          },
          "getMusicVolume", [&]() -> float { 
            return audioEngine.getMusicVolume(); 
          },
          
          // State queries
          "isMusicPlaying", [&]() -> bool { 
            return audioEngine.isMusicPlaying(); 
          },
          "isMusicPaused", [&]() -> bool { 
            return audioEngine.isMusicPaused(); 
          },
          
          // Audio clip management
          "isAudioClipLoaded", [&](const std::string& clipName) -> bool { 
            return audioEngine.isAudioClipLoaded(StringID(clipName)); 
          }
      );
    }
  }
}

#endif
