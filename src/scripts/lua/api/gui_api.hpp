#ifndef __IF__GUI_API_HPP
#define __IF__GUI_API_HPP

// C++ standard library
#include <exception>
#include <string>

// Third-party libraries
#include <sol/sol.hpp>

// Local includes
#include "../../../gui/gui.hpp"
#include "../../../gui/widgets/image_widget.hpp"
#include "../../../gui/widgets/label_widget.hpp"
#include "../../../gui/widgets/mixins/callbackable.hpp"
#include "../../../gui/widgets/mixins/colorable.hpp"
#include "../../../gui/widgets/mixins/transformable.hpp"
#include "../../../gui/widgets/panel_widget.hpp"

namespace IronFrost {
  namespace API {

    sol::object getWidgetObject(sol::state& lua, Widget& widget) {
      try {
        if (auto* label = dynamic_cast<LabelWidget*>(&widget)) {
          return sol::make_object(lua, label);
        }
        if (auto* image = dynamic_cast<ImageWidget*>(&widget)) {
          return sol::make_object(lua, image);
        }
        if (auto* panel = dynamic_cast<PanelWidget*>(&widget)) {
          return sol::make_object(lua, panel);
        }
        return sol::make_object(lua, widget);
      } catch (const std::exception& /* e */) {
        return sol::lua_nil;
      }
    };

    void bindGUIAPI(sol::state& luaState) {
      luaState.new_usertype<Callbackable>("Callbackable",
        sol::no_constructor,
        "setCallback", &Callbackable::setCallback,
        "triggerCallback", &Callbackable::triggerCallback 
      );

      luaState.new_usertype<Colorable>("Colorable",
        sol::no_constructor,
        "setColor", &Colorable::setColor,
        "getColor", &Colorable::getColor,
        "setAlpha", &Colorable::setAlpha,
        "getAlpha", &Colorable::getAlpha
      );

      luaState.new_usertype<Transformable>("Transformable",
        sol::no_constructor,
        "setPosition", &Transformable::setPosition,
        "getPosition", &Transformable::getPosition,
        "setSize", &Transformable::setSize,
        "getSize", &Transformable::getSize,
        "setRotation", &Transformable::setRotation,
        "getRotation", &Transformable::getRotation
      );

      luaState.new_usertype<Borderable>("Borderable",
        sol::no_constructor,
        "setBorder", &Borderable::setBorder,
        "getBorder", &Borderable::getBorder,
        "getBorderColor", &Borderable::getBorderColor
      );

      luaState.new_usertype<Widget>("Widget",
        sol::no_constructor,
        sol::base_classes, sol::bases<Callbackable, Transformable>(),
        "getType", &Widget::getType,
        "isVisible", &Widget::isVisible
      );
        
      luaState.new_usertype<LabelWidget>("LabelWidget",
        sol::constructors<LabelWidget(StringID, std::string)>(),
        sol::base_classes, sol::bases<Widget, Transformable, Colorable>(),
        "setText", &LabelWidget::setText,
        "getText", &LabelWidget::getText,
        "setFontName", [&](LabelWidget& labelWidget, const std::string& name) {
          return labelWidget.setFontName(StringID(name));
        },
        "getFontName", &LabelWidget::getFontName
      );

      luaState.new_usertype<ImageWidget>("ImageWidget",
        sol::constructors<ImageWidget(const StringID &)>(),
        sol::base_classes, sol::bases<Widget, Transformable, Colorable, Borderable, Callbackable>(),
        "setTextureName", [&](ImageWidget& ImageWidget, const std::string& name) {
          return ImageWidget.setTextureName(StringID(name));
        },
        "getTextureName", &ImageWidget::getTextureName
      );

      luaState.new_usertype<PanelWidget>("PanelWidget",
        sol::constructors<PanelWidget()>(),
        sol::base_classes, sol::bases<Widget, Colorable, Borderable>(),
        "addWidget", &PanelWidget::addWidgetRaw,
        "removeWidget", &PanelWidget::removeWidget,
        "getWidget", [&](const PanelWidget& panelWidget, const StringID& id) {
          return getWidgetObject(luaState, panelWidget.getWidget(id));
        }
      );

      luaState.new_usertype<GUI>("GUI",
        sol::constructors<GUI()>(),
        "addWidget", &GUI::addWidgetRaw,
        "getWidget", [&](const GUI& gui, const StringID& id) {
          return getWidgetObject(luaState, gui.getWidget(id));
        }
      );
    }
  }
}

#endif
