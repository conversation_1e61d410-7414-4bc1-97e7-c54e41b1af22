#ifndef __IF__WINDOW_API_HPP
#define __IF__WINDOW_API_HPP

// Third-party libraries
#include <sol/sol.hpp>

// Local includes
#include "../../../window/window.hpp"
#include "../../../window/keyboard_events.hpp"

namespace IronFrost {
  namespace API {
    void bindWindowAPI(sol::state& luaState, IWindow& window) {
      auto& eventDispatcher = ServiceLocator::getService<EventDispatcher>();

      luaState.new_enum<KeyType>("KeyType", {
        {"ESCAPE", KeyType::KEY_ESCAPE},
        {"ARROW_UP", KeyType::KEY_ARROW_UP},
        {"ARROW_DOWN", KeyType::KEY_ARROW_DOWN},
        {"ARROW_LEFT", KeyType::KEY_ARROW_LEFT},
        {"ARROW_RIGHT", KeyType::KEY_ARROW_RIGHT},
        {"TILDE", KeyType::KEY_TILDE},
        {"BACKSPACE", KeyType::KEY_BACKSPACE},
        {"ENTER", KeyType::KEY_ENTER},
        {"_0", KeyType::KEY_0},
        {"_1", KeyType::KEY_1},
        {"_2", KeyType::KEY_2},
        {"_3", KeyType::KEY_3},
        {"_4", KeyType::KEY_4},
        {"_5", KeyType::KEY_5},
        {"_6", KeyType::KEY_6},
        {"_7", KeyType::KEY_7},
        {"_8", KeyType::KEY_8},
        {"_9", KeyType::KEY_9},
        {"A", KeyType::KEY_A},
        {"B", KeyType::KEY_B},
        {"C", KeyType::KEY_C},
        {"D", KeyType::KEY_D},
        {"E", KeyType::KEY_E},
        {"F", KeyType::KEY_F},
        {"G", KeyType::KEY_G},
        {"H", KeyType::KEY_H},
        {"I", KeyType::KEY_I},
        {"J", KeyType::KEY_J},
        {"K", KeyType::KEY_K},
        {"L", KeyType::KEY_L},
        {"M", KeyType::KEY_M},
        {"N", KeyType::KEY_N},
        {"O", KeyType::KEY_O},
        {"P", KeyType::KEY_P},
        {"Q", KeyType::KEY_Q},
        {"R", KeyType::KEY_R},
        {"S", KeyType::KEY_S},
        {"T", KeyType::KEY_T},
        {"U", KeyType::KEY_U},
        {"V", KeyType::KEY_V},
        {"W", KeyType::KEY_W},
        {"X", KeyType::KEY_X},
        {"Y", KeyType::KEY_Y},
        {"Z", KeyType::KEY_Z},
        {"SPACE", KeyType::KEY_SPACE},
      });

      luaState.new_enum<MouseButtonType>("MouseButtonType", {
        {"LEFT", MouseButtonType::MOUSE_BUTTON_LEFT},
        {"MIDDLE", MouseButtonType::MOUSE_BUTTON_MIDDLE},
        {"RIGHT", MouseButtonType::MOUSE_BUTTON_RIGHT}
      });

      luaState.new_usertype<IKeyboard>("Keyboard",
        sol::no_constructor,
        "isKeyDown", &IKeyboard::isKeyDown,
        "isKeyUp", &IKeyboard::isKeyUp,
        "setCallback", [&](IKeyboard& keyboard, KeyType key, sol::function callback) {
          keyboard.setCallback(key, [callback]() {
            callback();
          });

          eventDispatcher.dispatch<KeyboardCallbackRegisteredEvent>(key, &luaState);
        },
        "removeCallback", &IKeyboard::removeCallback,
        "triggerCallback", &IKeyboard::triggerCallback);

      luaState.new_usertype<IMouse>("Mouse",
        sol::no_constructor,
        "getX", &IMouse::getXPos,
        "getY", &IMouse::getYPos,
        "getRelativeX", &IMouse::getRelativeXPos,
        "getRelativeY", &IMouse::getRelativeYPos,
        "resetRelativePosition", &IMouse::resetRelativePosition,
        "isButtonPressed", &IMouse::isButtonPressed,
        "isButtonReleased", &IMouse::isButtonReleased,
        "hideCursor", &IMouse::hideCursor,
        "showCursor", &IMouse::showCursor,
        "toggleCursor", &IMouse::toggleCursor);

      luaState.new_usertype<IWindow>("Window",
        sol::no_constructor,
        "getWidth", &IWindow::getWidth,
        "getHeight", &IWindow::getHeight,
        "getKeyboard", &IWindow::getKeyboard,
        "getMouse", &IWindow::getMouse);


      luaState["window"] = &window;
    }
  }
}
#endif
