#ifndef __IF__SCENE_API_HPP
#define __IF__SCENE_API_HPP

// C++ standard library
#include <string>

// Third-party libraries
#include <sol/sol.hpp>

// Local includes
#include "../../../scene/scene.hpp"
#include "../../../scene/scene_manager.hpp"

namespace IronFrost {
  class GameScene; // Forward declaration

  namespace API {
    void bindSceneAPI(sol::state& luaState, SceneManager& sceneManager) {
      luaState.new_usertype<Camera>("Camera",
        "setPosition", &Camera::setPosition,
        "setDirection", &Camera::setDirection,
        "setUp", &Camera::setUp,
        "setFov", &Camera::setFov,
        "setAspectRatio", &Camera::setAspectRatio,
        "setNear", &Camera::setNearPlane,
        "setFar", &Camera::setFarPlane,
        "moveForward", &Camera::moveForward,
        "moveBackward", &Camera::moveBackward,
        "moveLeft", &Camera::moveLeft,
        "moveRight", &Camera::moveRight,
        "turnLeft", &Camera::turnLeft,
        "turnRight", &Camera::turnRight,
        "turnUp", &Camera::turnUp,
        "turnDown", &Camera::turnDown,
        "mouseLookYaw", &Camera::mouseLookYaw,
        "mouseLookPitch", &Camera::mouseLookPitch,
        "calculateForwardMovement", &Camera::calculateForwardMovement,
        "calculateBackwardMovement", &Camera::calculateBackwardMovement,
        "calculateLeftMovement", &Camera::calculateLeftMovement,
        "calculateRightMovement", &Camera::calculateRightMovement,
        "setPositionIfValid", &Camera::setPositionIfValid,
        "getCollisionRadius", &Camera::getCollisionRadius,
        "setCollisionRadius", &Camera::setCollisionRadius);

      luaState.new_usertype<SceneNode>("SceneNode",
        "getPosition", &SceneNode::getPosition,
        "setPosition", &SceneNode::setPosition,
        "getRotation", &SceneNode::getRotation,
        "setRotation", &SceneNode::setRotation,
        "getScale", &SceneNode::getScale,
        "setScale", &SceneNode::setScale);

      luaState.new_usertype<ISceneGraph>("SceneGraph",
        "get", &ISceneGraph::get);

      luaState.new_usertype<CollisionHandler>("CollisionHandler",
        "resolveCameraMovement", &CollisionHandler::resolveCameraMovement);

      luaState.new_usertype<IScene>("Scene",
        "setPostprocessEffect", [](IScene& _scene, const std::string& _name) {
          auto& sceneRenderer = _scene.getSceneRenderer();
          return sceneRenderer.setPostprocessEffect(StringID(_name));
        },
        "getCamera", &IScene::getCamera,
        "getGUI", &IScene::getGUI,
        "getSceneGraph", [](IScene& _scene) -> ISceneGraph& {
          // Cast to GameScene to access scene graph
          auto* gameScene = dynamic_cast<GameScene*>(&_scene);
          if (!gameScene) {
            throw std::runtime_error("Scene graph only available for GameScene");
          }
          return gameScene->getSceneGraph();
        },
        "getCollisionHandler", [](IScene& _scene) -> CollisionHandler& {
          // Cast to GameScene to access scene graph
          auto* gameScene = dynamic_cast<GameScene*>(&_scene);
          if (!gameScene) {
            throw std::runtime_error("Collision handler only available for GameScene");
          }
          return gameScene->getCollisionHandler();
        },
        "getSceneName", &IScene::getSceneName);

      luaState["SCENES"] = luaState.create_table_with(
        "switchTo", [&](const std::string& _name) {
          sceneManager.switchToScene(StringID(_name));
        });
    }
  }
}

#endif
