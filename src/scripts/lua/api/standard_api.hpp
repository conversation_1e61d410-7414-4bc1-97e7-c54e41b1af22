#ifndef __IF__STANDARD_API_HPP
#define __IF__STANDARD_API_HPP

// C++ standard library
#include <chrono>
#include <sstream>
#include <string>

// Third-party libraries
#include <sol/sol.hpp>

// Local includes
#include "../../../services/service_locator.hpp"
#include "../../../utils/random.hpp"
#include "../../../utils/string_id.hpp"
#include "../../scheduled_task_manager.hpp"

namespace IronFrost {
  namespace API {
    void bindStandardAPI(sol::state& luaState, ScheduledTaskManager& taskManager) {
      luaState.new_usertype<StringID>(
        "StringID",
        sol::constructors<StringID(const std::string&)>(),
        "toHexID", &StringID::toString,
        "toString", [&](StringID& stringID) -> std::string {
          return StringID::getString(stringID);
        }
      );

      luaState.new_usertype<Random>(
        "Random",
        sol::constructors<Random()>(),
        "getInt", &Random::getInt,
        "getDouble", &Random::getDouble
      );

      luaState.set_function("setTimeout",
        [&](sol::function callback, int delay) {
          auto executeTime = std::chrono::steady_clock::now() + std::chrono::milliseconds(delay);

          taskManager.scheduleTask([callback]()
                                    { callback(); }, executeTime);
        });
    }
  }
}

#endif
