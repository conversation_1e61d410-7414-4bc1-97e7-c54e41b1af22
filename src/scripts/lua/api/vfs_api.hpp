#ifndef __IF__VFS_API_HPP
#define __IF__VFS_API_HPP

// C++ standard library
#include <string>

// Third-party libraries
#include <sol/sol.hpp>

// Local includes
#include "../../../vfs/vfs.hpp"

namespace IronFrost {
  namespace API {
    void bindVFSAPI(sol::state& luaState, IVFS& vfs) {
      luaState["VFS"] = luaState.create_table_with(
          "mount", [&](const std::string &path, const std::string &mountPoint) { 
            vfs.mount(path, mountPoint); 
          },
          "unmount", [&](const std::string &mountPoint) { 
            vfs.unmount(mountPoint); 
          },
          "exists", [&](const std::string &path) { 
            return vfs.exists(path); 
          },
          "readFile", [&](const std::string &path) { 
            return vfs.readFile(path); 
          },
          "readFileBinary", [&](const std::string &path) { 
            return vfs.readFileBinary(path); 
          },
          "writeFile", [&](const std::string &path, const std::string &content) { 
            return vfs.writeFile(path, content); 
          },
          "writeFileBinary", [&](const std::string &path, const std::vector<unsigned char> &content) { 
            return vfs.writeFileBinary(path, content); 
          },
          "listFiles", [&](const std::string &path) { 
            return vfs.listFiles(path); 
          });
    }
  }
}
#endif
