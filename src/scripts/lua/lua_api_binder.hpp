#ifndef __IF__LUA_API_BINDER_HPP
#define __IF__LUA_API_BINDER_HPP

// C++ standard library
#include <string>

// Third-party libraries
#include <sol/sol.hpp>

namespace IronFrost {
  class Application;
  class ScheduledTaskManager;

  /**
   * Centralized Lua API binding for consistent API across global and scene contexts.
   * Handles binding of all scripting APIs to Lua states.
   */
  class LuaAPIBinder {
  private:
    Application& m_application;
    ScheduledTaskManager& m_taskManager;

  public:
    LuaAPIBinder(Application& application, ScheduledTaskManager& taskManager);

    /**
     * Bind all standard APIs to a Lua state.
     * This includes standard functions, VFS, window, console, etc.
     */
    void bindAllAPIs(sol::state& luaState);

    /**
     * Bind a custom print function with optional prefix.
     * The print function outputs to both system console and game console.
     */
    void bindPrintFunction(sol::state& luaState, const std::string& prefix = "");

    /**
     * Override Lua's require function to use VFS instead of filesystem.
     */
    void bindVFSRequireFunction(sol::state& luaState);

  private:
    void bindCoreAPIs(sol::state& luaState);
  };
}

#endif
