#include "lua_global_script_context.hpp"

// Local includes
#include "../../app/application.hpp"

namespace IronFrost {

  LuaGlobalScriptContext::LuaGlobalScriptContext(Application& application, LuaAPIBinder& apiBinder)
    : LuaScriptContext(application, apiBinder) {
  }

  void LuaGlobalScriptContext::setCurrentScene(const void* scene) {
    m_luaState["scene"] = scene;
  }

  void LuaGlobalScriptContext::setupContextVariables() {
    // Set up global context variables
    // Could add things like:
    // m_luaState["appVersion"] = "1.0.0";
    // m_luaState["platform"] = "desktop";
    // etc.
  }

  std::string LuaGlobalScriptContext::getPrintPrefix() const {
    return ""; // No prefix for global scripts
  }

}
