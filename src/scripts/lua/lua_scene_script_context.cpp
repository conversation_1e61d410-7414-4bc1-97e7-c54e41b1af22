#include "lua_scene_script_context.hpp"

// Local includes
#include "lua_api_binder.hpp"
#include "../../utils/string_id.hpp"

namespace IronFrost {
  LuaSceneScriptContext::LuaSceneScriptContext(Application& application, LuaAPIBinder& apiBinder, const IScene& scene)
    : LuaScriptContext(application, apiBinder), m_scene(scene) {
  }

  void LuaSceneScriptContext::setupContextVariables() {
    std::string sceneNameStr = StringID::getString(m_scene.getSceneName());
    m_luaState["sceneName"] = sceneNameStr;
    m_luaState["scene"] = &m_scene;
  }

  std::string LuaSceneScriptContext::getPrintPrefix() const {
    std::string sceneNameStr = StringID::getString(m_scene.getSceneName());
    return "[Scene:" + sceneNameStr + "]";
  }
}
