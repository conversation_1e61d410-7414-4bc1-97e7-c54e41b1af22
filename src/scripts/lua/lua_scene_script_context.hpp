#ifndef __IF__LUA_SCENE_SCRIPT_CONTEXT_HPP
#define __IF__LUA_SCENE_SCRIPT_CONTEXT_HPP

// Local includes
#include "lua_script_context.hpp"
#include "../../scene/scene.hpp"

namespace IronFrost {

  /**
   * Scene-specific Lua script context.
   * Handles scene script execution with scene-specific variables and state.
   * Directly implements ISceneScriptContext with lifecycle management.
   */
  class LuaSceneScriptContext : public LuaScriptContext, public ISceneScriptContext {
  private:
    const IScene& m_scene;

  public:
    LuaSceneScriptContext(Application& application, LuaAPIBinder& apiBinder, const IScene& scene);

  protected:
    void setupContextVariables() override;
    std::string getPrintPrefix() const override;
  };

}

#endif
