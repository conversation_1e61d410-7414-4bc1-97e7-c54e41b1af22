#include "script_engine.hpp"

// C++ standard library
#include <exception>
#include <iostream>
#include <memory>
#include <string>
#include <vector>

// Local includes
#include "../app/application.hpp"
#include "../events/event_dispatcher.hpp"
#include "../vfs/vfs.hpp"
#include "lua/lua_script_engine.hpp"

namespace IronFrost {

  void IScriptEngine::executePerFrame(float deltaTime) {
    m_taskManager.executeScheduledTasks();
    executeRegisteredScripts(deltaTime);
  }

  void IScriptEngine::executeInitScripts(const std::vector<std::string>& scriptContents) {
    std::cout << "Executing init scripts from config" << '\n';
    for (const auto& scriptContent : scriptContents) {
      executeScriptCode(scriptContent);
    }
  }

  std::unique_ptr<IScriptEngine> IScriptEngine::create(SCRIPT_ENGINE_TYPE scriptEngineType, Application& application) {
    if (scriptEngineType == SCRIPT_ENGINE_TYPE::LUA) {
      return std::make_unique<LuaScriptEngine>(application);
    }

    throw std::runtime_error("Unknown script engine type");
  }
}
