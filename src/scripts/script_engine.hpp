#ifndef __IF__SCRIPTS_HPP
#define __IF__SCRIPTS_HPP

// C++ standard library
#include <memory>
#include <string>
#include <vector>

// Local includes
#include "scheduled_task_manager.hpp"
#include "script_engine_types.hpp"

namespace IronFrost
{

  class EventDispatcher;
  class Application;
  class IScene;
  class ISceneScriptContext;

  class IScriptEngine {
    protected:
      ScheduledTaskManager m_taskManager;

      Application& m_application;

      virtual void executeRegisteredScripts(float deltaTime) = 0;

    public:
      IScriptEngine(Application& application) : 
        m_application(application)
      {};

      virtual ~IScriptEngine() = default;

      virtual void executeScriptFile(const std::string& scriptPath) = 0;
      virtual void executeScriptCode(const std::string& scriptCode) = 0;
      virtual void registerPerFrameScripts(const std::vector<std::string>& scriptContents) = 0;
      virtual void executeInitScripts(const std::vector<std::string>& scriptContents);
      
      virtual void registerListeners() = 0;
      void executePerFrame(float deltaTime);

      virtual std::unique_ptr<ISceneScriptContext> createSceneScriptContext(const IScene& scene) = 0;
      
      static std::unique_ptr<IScriptEngine> create(SCRIPT_ENGINE_TYPE scriptEngineType, Application& application);
    };
}

#endif
