#ifndef __IF__SCENE_SCRIPT_MANAGER_HPP
#define __IF__SCENE_SCRIPT_MANAGER_HPP

// C++ standard library
#include <memory>
#include <string>
#include <vector>

// Local includes
#include "../scene/scene.hpp"

namespace IronFrost {

  /**
   * Abstract interface for scene-specific script handling.
   * Handles scene script contexts, initialization, and per-frame execution.
   */
  class ISceneScriptHandler {
    public:
      virtual ~ISceneScriptHandler() = default;

      /**
       * Create a new script context for the specified scene.
       * Destroys any existing context (scene switch).
       */
      virtual void createSceneContext(const IScene& scene) = 0;

      /**
       * Destroy the current script context.
       */
      virtual void destroySceneContext() = 0;

      /**
       * Execute initialization scripts for the current scene.
       */
      virtual void executeInitScripts(const std::vector<std::string>& scriptContents) = 0;

      /**
       * Register per-frame scripts for the current scene.
       */
      virtual void registerPerFrameScripts(const std::vector<std::string>& scriptContents) = 0;

      /**
       * Execute per-frame scripts for the current scene.
       */
      virtual void executePerFrame(float deltaTime) = 0;
  };


}

#endif
