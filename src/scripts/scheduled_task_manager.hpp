#ifndef __IF__TASKS_HPP
#define __IF__TASKS_HPP

// C++ standard library
#include <chrono>
#include <functional>
#include <iostream>
#include <queue>
#include <utility>

namespace IronFrost {
  struct Task {
    std::function<void()> func;
    std::chrono::steady_clock::time_point timeToExecute;

    bool operator<(const Task &other) const {
      return timeToExecute > other.timeToExecute;
    }
  };

  class ScheduledTaskManager {
  private:
    std::priority_queue<Task> m_taskQueue;

  public:
    ~ScheduledTaskManager() {
      clearQueue();
    }

    void scheduleTask(std::function<void()> func, std::chrono::steady_clock::time_point timeToExecute) {
      m_taskQueue.push({std::move(func), timeToExecute});
    }

    void executeScheduledTasks() {
      auto now = std::chrono::steady_clock::now();

      while (!m_taskQueue.empty() && m_taskQueue.top().timeToExecute <= now) {
        auto task = m_taskQueue.top();

        m_taskQueue.pop();
        task.func();
      }
    }

    void clearQueue() {
      std::cout << "Clearing task queue" << std::endl;

      while (!m_taskQueue.empty())
        m_taskQueue.pop();
    }
  };
}

#endif
