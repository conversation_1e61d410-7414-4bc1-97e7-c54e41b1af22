#ifndef __IF__WINDOW_HPP
#define __IF__WINDOW_HPP

// C++ standard library
#include <functional>
#include <memory>
#include <string>
#include <unordered_map>

// Third-party libraries
#include <glm/glm.hpp>

// Local includes
#include "keys.hpp"

namespace IronFrost {
  class EventDispatcher;

  class IKeyboard {
    private:
      std::unordered_map<KeyType, std::function<void()>> m_callbacks;

    public:
      IKeyboard();
      virtual ~IKeyboard() = default;

      virtual bool isKeyDown(KeyType _key) const = 0;
      virtual bool isKeyUp(KeyType _key) const = 0;

      void setCallback(KeyType name, std::function<void()> callback);
      void removeCallback(KeyType name);
      void triggerCallback(KeyType name);
      void clearAllCallbacks();
  };

  enum MouseButtonType {
    MOUSE_BUTTON_LEFT,
    MOUSE_BUTTON_RIGHT,
    MOUSE_BUTTON_MIDDLE,
    MOUSE_BUTTON_NUM_OF_TYPES
  };

  enum MouseButtonState {
    MOUSE_BUTTON_UP,
    MOUSE_BUTTON_DOWN,
    MOUSE_BUTTON_NUM_OF_STATES
  };

  class IMouse {
    public:
      virtual ~IMouse() = default;

      virtual double getXPos() const = 0;
      virtual double getYPos() const = 0;

      virtual double getRelativeXPos() const = 0;
      virtual double getRelativeYPos() const = 0;
      virtual void resetRelativePosition() = 0;

      virtual bool isButtonPressed(MouseButtonType _button) const = 0;
      virtual bool isButtonReleased(MouseButtonType _button) const = 0;

      virtual void hideCursor() = 0;
      virtual void showCursor() = 0;
      virtual void toggleCursor() = 0;
  };

  enum WINDOW_LIBRARY {
    GLFW
  };

  class IWindow {
    protected:
      glm::ivec2 m_size;

      IWindow(int width, int height) :
        m_size(glm::ivec2(width, height))
      {}

    public:
      virtual ~IWindow() = default;

      virtual void swapBuffers() const = 0;
      virtual void pollEvents() const = 0;

      virtual bool shouldClose() const = 0;
      virtual void close() const = 0;

      virtual void *getRawHandle() const = 0;

      virtual IKeyboard &getKeyboard() const = 0;
      virtual IMouse &getMouse() const = 0;

      int getWidth() const;
      int getHeight() const;
      float getAspectRatio() const;
      const glm::ivec2& getSize() const;

      static std::unique_ptr<IWindow> create(WINDOW_LIBRARY library, int width, int height, const std::string& title);
  };
}

#endif
