#ifndef __IF__CONSOLE_HPP
#define __IF__CONSOLE_HPP

// C++ standard library
#include <string>
#include <vector>

namespace IronFrost {
  class EventDispatcher;
  class KeyInputEvent;
  class KeyDownEvent;
  class ConsoleLogEvent;

  class Console {
    private:
      std::vector<std::string> m_commandHistory;
      std::vector<std::string> m_outputLog;

      bool m_isOpen{false};

      std::string m_inputBuffer;

      // Event listener registration methods
      void registerListeners();
      void registerKeyInputListener();
      void registerKeyDownListener();
      void registerConsoleLogListener();

      // Event handler methods
      void handleKeyInput(const KeyInputEvent& event);
      void handleKeyDown(const KeyDownEvent& event);
      void handleConsoleLog(const ConsoleLogEvent& event);

      // Key-specific handler methods
      void handleEnterKey();
      void handleBackspaceKey();

      // Utility methods
      void dispatchInputBufferEvent();

    public:
      Console();
      ~Console() = default;

      bool isOpen() const;
      const std::string& getInputBuffer() const;
      const std::vector<std::string>& getOutputLog() const;

      void processInput(const std::string& input);
      void toggle();
  };
}

#endif
