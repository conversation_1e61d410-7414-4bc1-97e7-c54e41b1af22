#include "console.hpp"

// C++ standard library
#include <iostream>
#include <string>
#include <vector>

// Local includes
#include "../../events/event_dispatcher.hpp"
#include "../../events/events.hpp"
#include "../../services/service_locator.hpp"
#include "../../window/keys.hpp"
#include "../../window/keyboard_events.hpp"

namespace IronFrost {
  void Console::registerListeners() {
    registerKeyInputListener();
    registerKeyDownListener();
    registerConsoleLogListener();
  }

  void Console::registerKeyInputListener() {
    auto& eventDispatcher = ServiceLocator::getService<EventDispatcher>();

    eventDispatcher.registerListener<KeyInputEvent>(
      [&](const KeyInputEvent &event) {
        handleKeyInput(event);
      });
  }

  void Console::registerKeyDownListener() {
    auto& eventDispatcher = ServiceLocator::getService<EventDispatcher>();

    eventDispatcher.registerListener<KeyDownEvent>(
      [&](const KeyDownEvent &event) {
        handleKeyDown(event);
      });
  }

  void Console::registerConsoleLogListener() {
    auto& eventDispatcher = ServiceLocator::getService<EventDispatcher>();

    eventDispatcher.registerListener<ConsoleLogEvent>(
      [&](const ConsoleLogEvent &event) {
        handleConsoleLog(event);
      });
  }

  void Console::handleKeyInput(const KeyInputEvent& event) {
    if (!m_isOpen) {
      return;
    }

    unsigned char inputChar = event.getInput();
    m_inputBuffer += static_cast<char>(inputChar);

    dispatchInputBufferEvent();
  }

  void Console::handleKeyDown(const KeyDownEvent& event) {
    if (!m_isOpen) {
      return;
    }

    if (event.getKey() == KEY_ENTER) {
      handleEnterKey();
    } else if (event.getKey() == KEY_BACKSPACE) {
      handleBackspaceKey();
    }

    dispatchInputBufferEvent();
  }

  void Console::handleConsoleLog(const ConsoleLogEvent& event) {
    m_outputLog.push_back(event.getLog());
  }

  void Console::handleEnterKey() {
    processInput(m_inputBuffer);
    m_commandHistory.push_back(m_inputBuffer);
    m_inputBuffer.clear();
  }

  void Console::handleBackspaceKey() {
    if (!m_inputBuffer.empty()) {
      m_inputBuffer.pop_back();
    }
  }

  void Console::dispatchInputBufferEvent() {
    auto& eventDispatcher = ServiceLocator::getService<EventDispatcher>();
    eventDispatcher.dispatch<ConsoleInputBufferEvent>(m_inputBuffer);
  }

  Console::Console() {
    registerListeners();
  }



  bool Console::isOpen() const {
    return m_isOpen;
  }

  const std::string & Console::getInputBuffer() const {
    return m_inputBuffer;
  }

  const std::vector<std::string>& Console::getOutputLog() const {
    return m_outputLog;
  }

  void Console::processInput(const std::string & input) {
    ServiceLocator::getService<EventDispatcher>().dispatch<ConsoleCommandEvent>(input);
  }

  void Console::toggle() {
    m_isOpen = !m_isOpen;
    
    auto& eventDispatcher = ServiceLocator::getService<EventDispatcher>();
    if(m_isOpen) {
      std::cout << "Console Open" << '\n';
      eventDispatcher.dispatch<ConsoleOpenEvent>();
    } else {
      std::cout << "Console Closed" << '\n';
      eventDispatcher.dispatch<ConsoleCloseEvent>();
    }
  }
}
