#ifndef __IF__KEYBOARD_EVENTS_HPP
#define __IF__KEYBOARD_EVENTS_HPP

// Local includes
#include "keys.hpp"
#include "../events/events.hpp"

namespace IronFrost {
  class KeyDownEvent : public Event {
    private:
      int m_key;

    public:
      explicit KeyDownEvent(int key) : m_key(key) {}

      std::string toString() const override {
        return "KeyDownEvent";
      }

      int getKey() const {
        return m_key;
      }
  };

  class KeyUpEvent : public Event {
    private:
      int m_key;

    public:
      explicit KeyUpEvent(int key) : m_key(key) {}

      std::string toString() const override {
        return "KeyUpEvent";
      }

      int getKey() const {
        return m_key;
      }
  };

  class KeyInputEvent : public Event {
    private:
      unsigned char m_input;

    public:
      explicit KeyInputEvent(unsigned char input) : m_input(input) {}

      std::string toString() const override {
        return "KeyInputEvent";
      }

      unsigned char getInput() const {
        return m_input;
      }
  };

  class KeyboardCallbackRegisteredEvent : public Event {
    private:
      KeyType m_key;

      void* m_context;

    public:
      explicit KeyboardCallbackRegisteredEvent(KeyType key, void* context = nullptr) : m_key(key), m_context(context) {}

      std::string toString() const override {
        return "KeyboardCallbackRegisteredEvent";
      }

      KeyType getKey() const {
        return m_key;
      }

      void* getContext() const {
        return m_context;
      }
  };

  class UnregisterKeyboardCallbackEvent : public Event {
    private:
      KeyType m_key;

    public:
      explicit UnregisterKeyboardCallbackEvent(KeyType key) : m_key(key) {}

      std::string toString() const override {
        return "UnregisterKeyboardCallbackEvent";
      }

      KeyType getKey() const {
        return m_key;
      }
  };
}

#endif
