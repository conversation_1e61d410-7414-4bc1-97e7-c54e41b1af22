#include "glfw_mouse.hpp"

// C++ standard library
#include <iostream>
#include <unordered_map>

// Local includes
#include "glfw_window.hpp"

namespace IronFrost {
  const std::unordered_map<int, MouseButtonType> GLFW_Mouse::buttonMap = {
      {GLFW_MOUSE_BUTTON_LEFT, MOUSE_BUTTON_LEFT},
      {GLFW_MOUSE_BUTTON_RIGHT, MOUSE_BUTTON_RIGHT},
      {GLFW_MOUSE_BUTTON_MIDDLE, MOUSE_BUTTON_MIDDLE}};

  void GLFW_Mouse::setupMouseMovementCallback(GLFWwindow *window) {
    glfwSetCursorPosCallback(window,
      [](GLFWwindow *window, double xpos, double ypos) {
        auto* glfwWindow = static_cast<GLFW_Window*>(glfwGetWindowUserPointer(window));
        auto& mouse = dynamic_cast<GLFW_Mouse&>(glfwWindow->getMouse());

        // Calculate new absolute position
        double newXPos = (xpos / glfwWindow->getWidth());
        double newYPos = (ypos / glfwWindow->getHeight());

        // Calculate relative movement
        mouse.m_relative_xpos += newXPos - mouse.m_xpos;
        mouse.m_relative_ypos += newYPos - mouse.m_ypos;

        // Update absolute position
        mouse.m_xpos = newXPos;
        mouse.m_ypos = newYPos;
      });
  }

  void GLFW_Mouse::setupMouseButtonCallback(GLFWwindow *window) {
    glfwSetMouseButtonCallback(window,
      [](GLFWwindow *window, int button, int action, int mods) {
        auto* glfwWindow = static_cast<GLFW_Window*>(glfwGetWindowUserPointer(window));
        auto& mouse = dynamic_cast<GLFW_Mouse&>(glfwWindow->getMouse());

        auto it = GLFW_Mouse::buttonMap.find(button);
        if (it != GLFW_Mouse::buttonMap.end()) {
          if (action == GLFW_PRESS) {
            mouse.updateButtonState(it->second, MouseButtonState::MOUSE_BUTTON_DOWN);
          } else if (action == GLFW_RELEASE) {
            mouse.updateButtonState(it->second, MouseButtonState::MOUSE_BUTTON_UP);
          }
        }
      });
  }

  void GLFW_Mouse::updateButtonState(MouseButtonType button, MouseButtonState state) {
    if (button >= 0 && button < MOUSE_BUTTON_NUM_OF_TYPES) {
      // NOLINTNEXTLINE(cppcoreguidelines-pro-bounds-constant-array-index) - Bounds checked above
      m_buttons[button] = state;
    }
  }

  GLFW_Mouse::GLFW_Mouse(GLFWwindow* window) : 
    m_window(window),
    m_xpos(0), m_ypos(0), m_relative_xpos(0), m_relative_ypos(0) 
  {
    if (glfwRawMouseMotionSupported() != 0) {
      glfwSetInputMode(window, GLFW_RAW_MOUSE_MOTION, GLFW_TRUE);
    }

    setupMouseMovementCallback(window);
    setupMouseButtonCallback(window);
  }

  double GLFW_Mouse::getXPos() const {
    return m_xpos;
  }

  double GLFW_Mouse::getYPos() const {
    return m_ypos;
  }

  double GLFW_Mouse::getRelativeXPos() const {
    return m_relative_xpos;
  }

  double GLFW_Mouse::getRelativeYPos() const {
    return m_relative_ypos;
  }

  void GLFW_Mouse::resetRelativePosition() {
    m_relative_xpos = 0.0;
    m_relative_ypos = 0.0;
  }

  bool GLFW_Mouse::isButtonPressed(MouseButtonType button) const {
    if (button >= 0 && button < MOUSE_BUTTON_NUM_OF_TYPES) {
      // NOLINTNEXTLINE(cppcoreguidelines-pro-bounds-constant-array-index) - Bounds checked above
      return m_buttons[button] == MouseButtonState::MOUSE_BUTTON_DOWN;
    }
    return false;
  }

  bool GLFW_Mouse::isButtonReleased(MouseButtonType button) const {
    if (button >= 0 && button < MOUSE_BUTTON_NUM_OF_TYPES) {
      // NOLINTNEXTLINE(cppcoreguidelines-pro-bounds-constant-array-index) - Bounds checked above
      return m_buttons[button] == MouseButtonState::MOUSE_BUTTON_UP;
    }
    return false;
  }

  void GLFW_Mouse::hideCursor() {
    glfwSetInputMode(m_window, GLFW_CURSOR, GLFW_CURSOR_HIDDEN);
  }

  void GLFW_Mouse::showCursor() {
    glfwSetInputMode(m_window, GLFW_CURSOR, GLFW_CURSOR_NORMAL);
  }

  void GLFW_Mouse::toggleCursor() {
    int cursorMode = glfwGetInputMode(m_window, GLFW_CURSOR);
    if (cursorMode == GLFW_CURSOR_NORMAL) {
      hideCursor();
    } else {
      showCursor();
    }
  }
}
