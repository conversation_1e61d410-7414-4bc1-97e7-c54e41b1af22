#ifndef __IF__GLFW_KEYBOARD_HPP
#define __IF__GLFW_KEYBOARD_HPP

#define GLFW_INCLUDE_NONE
// C++ standard library
#include <unordered_map>

// Third-party libraries
#include <GLFW/glfw3.h>

// Local includes
#include "../window.hpp"

namespace IronFrost {
  class GLFW_Keyboard : public IKeyboard {
    private:
      KeyState m_keys[KEY_NUM_OF_KEYS] = {KEY_UP};

      const static std::unordered_map<int, KeyType> keyMap;

      void setupKeyboardCallback(GLFWwindow* window);

    public:
      explicit GLFW_Keyboard(GLFWwindow* window);

      bool isKeyDown(KeyType key) const override;
      bool isKeyUp(KeyType key) const override;

      // Public methods for testing - these are called by GLFW callbacks
      void setKeyDown(KeyType key);
      void setKeyUp(KeyType key);
  };
}

#endif
