#include "glfw_window.hpp"

// C++ standard library
#include <iostream>
#include <memory>
#include <string>

// Local includes
#include "../../events/event_dispatcher.hpp"
#include "../../services/service_locator.hpp"
#include "glfw_keyboard.hpp"
#include "glfw_mouse.hpp"

namespace IronFrost {
  GLFW_Window::GLFW_Window(int width, int height, const std::string& title) :
    IWindow(width, height),
    m_window(glfwCreateWindow(width, height, title.c_str(), nullptr, nullptr)),
    m_keyboard(std::make_unique<GLFW_Keyboard>(m_window)),
    m_mouse(std::make_unique<GLFW_Mouse>(m_window))
  {

    // glfwSetInputMode(m_window, GLFW_CURSOR, GLFW_CURSOR_DISABLED);

    glfwSetWindowUserPointer(m_window, this);

    glfwSetFramebufferSizeCallback(m_window, 
      [](GLFWwindow *window, int width, int height) {
        auto *glfwWindow = static_cast<GLFW_Window *>(glfwGetWindowUserPointer(window));
        auto& eventDispatcher = ServiceLocator::getService<EventDispatcher>();

        glfwWindow->m_size = glm::vec2(width, height);
        eventDispatcher.dispatch<WindowResizeEvent>(width, height);
      });
  }

  std::unique_ptr<GLFW_Window> IronFrost::GLFW_Window::create(int width, int height, const std::string &title) {
    if (glfwInit() == 0) {
      return nullptr;
    }

    #if defined(__APPLE__)
      glfwWindowHint(GLFW_CONTEXT_VERSION_MAJOR, 4);
      glfwWindowHint(GLFW_CONTEXT_VERSION_MINOR, 1);
      glfwWindowHint(GLFW_OPENGL_PROFILE, GLFW_OPENGL_CORE_PROFILE);

      glfwWindowHint(GLFW_COCOA_RETINA_FRAMEBUFFER, GLFW_FALSE);

    #else
      glfwWindowHint(GLFW_CONTEXT_VERSION_MAJOR, 4);
      glfwWindowHint(GLFW_CONTEXT_VERSION_MINOR, 6);
      glfwWindowHint(GLFW_OPENGL_PROFILE, GLFW_OPENGL_CORE_PROFILE);
    #endif

    #ifndef NDEBUG
      glfwWindowHint(GLFW_OPENGL_DEBUG_CONTEXT, 1);
    #endif

    std::unique_ptr<GLFW_Window> window = std::unique_ptr<GLFW_Window>(new GLFW_Window(width, height, title));

    if (!window->isInitialized()) {
      glfwTerminate();
      return nullptr;
    }

    glfwMakeContextCurrent(window->m_window);

    return window;
  }

  GLFW_Window::~GLFW_Window() {
    if (m_window != nullptr) {
      glfwDestroyWindow(m_window);
      glfwTerminate();
    }
  }

  bool GLFW_Window::isInitialized() const {
    return m_window != nullptr;
  }

  void GLFW_Window::swapBuffers() const {
    glfwSwapBuffers(m_window);
  }

  void GLFW_Window::pollEvents() const {
    glfwPollEvents();
  }

  bool GLFW_Window::shouldClose() const {
    if (m_window != nullptr) {
      return glfwWindowShouldClose(m_window) != 0;
    }

    return true;
  }

  void GLFW_Window::close() const {
    ServiceLocator::getService<EventDispatcher>().dispatch<WindowCloseEvent>();
    glfwSetWindowShouldClose(m_window, GLFW_TRUE);
  }

  void* GLFW_Window::getRawHandle() const {
    // NOLINTNEXTLINE(cppcoreguidelines-pro-type-reinterpret-cast) - Required for GLFW function pointer interface
    return reinterpret_cast<void *>(glfwGetProcAddress);
  }

  IKeyboard& GLFW_Window::getKeyboard() const {
    return *m_keyboard;
  }

  IMouse& GLFW_Window::getMouse() const {
    return *m_mouse;
  }
}
