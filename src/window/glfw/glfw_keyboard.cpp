#include "glfw_keyboard.hpp"

// C++ standard library
#include <iostream>
#include <unordered_map>

// Local includes
#include "../../events/event_dispatcher.hpp"
#include "../../services/service_locator.hpp"
#include "../keyboard_events.hpp"
#include "glfw_window.hpp"

namespace IronFrost {
  const std::unordered_map<int, KeyType> GLFW_Keyboard::keyMap = {
    {GLFW_KEY_ESCAPE, KEY_ESCAPE},
    {GLFW_KEY_UP, KEY_ARROW_UP},
    {GLFW_KEY_DOWN, KEY_ARROW_DOWN},
    {GLFW_KEY_LEFT, KEY_ARROW_LEFT},
    {GLFW_KEY_RIGHT, KEY_ARROW_RIGHT},
    {GLFW_KEY_GRAVE_ACCENT, KEY_TILDE},
    {GLFW_KEY_BACKSPACE, <PERSON><PERSON><PERSON>_<PERSON><PERSON>KSPACE},
    {G<PERSON><PERSON>_KEY_ENTER, <PERSON><PERSON><PERSON>_<PERSON>NTER},
    {GLFW_KEY_0, KEY_0},
    {GLFW_KEY_1, KEY_1},
    {GLFW_KEY_2, KEY_2},
    {GLFW_KEY_3, KEY_3},
    {GLFW_KEY_4, KEY_4},
    {GLFW_KEY_5, KEY_5},
    {GLFW_KEY_6, KEY_6},
    {GLFW_KEY_7, KEY_7},
    {GLFW_KEY_8, KEY_8},
    {GLFW_KEY_9, KEY_9},
    {GLFW_KEY_A, KEY_A},
    {GLFW_KEY_B, KEY_B},
    {GLFW_KEY_C, KEY_C},
    {GLFW_KEY_D, KEY_D},
    {GLFW_KEY_E, KEY_E},
    {GLFW_KEY_F, KEY_F},
    {GLFW_KEY_G, KEY_G},
    {GLFW_KEY_H, KEY_H},
    {GLFW_KEY_I, KEY_I},
    {GLFW_KEY_J, KEY_J},
    {GLFW_KEY_K, KEY_K},
    {GLFW_KEY_L, KEY_L},
    {GLFW_KEY_M, KEY_M},
    {GLFW_KEY_N, KEY_N},
    {GLFW_KEY_O, KEY_O},
    {GLFW_KEY_P, KEY_P},
    {GLFW_KEY_Q, KEY_Q},
    {GLFW_KEY_R, KEY_R},
    {GLFW_KEY_S, KEY_S},
    {GLFW_KEY_T, KEY_T},
    {GLFW_KEY_U, KEY_U},
    {GLFW_KEY_V, KEY_V},
    {GLFW_KEY_W, KEY_W},
    {GLFW_KEY_X, KEY_X},
    {GLFW_KEY_Y, KEY_Y},
    {GLFW_KEY_Z, KEY_Z},
    {GLFW_KEY_SPACE, KEY_SPACE},
  };

  GLFW_Keyboard::GLFW_Keyboard(GLFWwindow* window) : IKeyboard() {
    setupKeyboardCallback(window);
  }

  void GLFW_Keyboard::setupKeyboardCallback(GLFWwindow *window) {
    glfwSetKeyCallback(window,
      [](GLFWwindow* window, int key, int scancode, int action, int mods) {
        const auto* glfwWindow = static_cast<const GLFW_Window *>(glfwGetWindowUserPointer(window));
        auto& keyboard = dynamic_cast<GLFW_Keyboard &>(glfwWindow->getKeyboard());
        auto& eventDispatcher = ServiceLocator::getService<EventDispatcher>();

        auto it = GLFW_Keyboard::keyMap.find(key);
        if (it != GLFW_Keyboard::keyMap.end()) {
          if (action == GLFW_PRESS) {
            eventDispatcher.dispatch<KeyDownEvent>(it->second);
            keyboard.setKeyDown(it->second);
          } else if (action == GLFW_RELEASE) {
            eventDispatcher.dispatch<KeyUpEvent>(it->second);
            keyboard.setKeyUp(it->second);
          }
        }
      });

    glfwSetCharCallback(window,
      [](GLFWwindow* window, unsigned int codepoint) {
        const auto* glfwWindow = static_cast<const GLFW_Window *>(glfwGetWindowUserPointer(window));
        auto& eventDispatcher = ServiceLocator::getService<EventDispatcher>();

        eventDispatcher.dispatch<KeyInputEvent>(codepoint);
      });
  }

  void GLFW_Keyboard::setKeyDown(KeyType key) {
    if(!isKeyDown(key)) {
      triggerCallback(key);
    }
    if (key >= 0 && key < KEY_NUM_OF_KEYS) {
      // NOLINTNEXTLINE(cppcoreguidelines-pro-bounds-constant-array-index) - Bounds checked above
      m_keys[key] = KeyState::KEY_DOWN;
    }
  }

  void GLFW_Keyboard::setKeyUp(KeyType key) {
    if (key >= 0 && key < KEY_NUM_OF_KEYS) {
      // NOLINTNEXTLINE(cppcoreguidelines-pro-bounds-constant-array-index) - Bounds checked above
      m_keys[key] = KeyState::KEY_UP;
    }
  }

  bool GLFW_Keyboard::isKeyDown(KeyType key) const {
    if (key >= 0 && key < KEY_NUM_OF_KEYS) {
      // NOLINTNEXTLINE(cppcoreguidelines-pro-bounds-constant-array-index) - Bounds checked above
      return m_keys[key] == KeyState::KEY_DOWN;
    }
    return false;
  }

  bool GLFW_Keyboard::isKeyUp(KeyType key) const {
    if (key >= 0 && key < KEY_NUM_OF_KEYS) {
      // NOLINTNEXTLINE(cppcoreguidelines-pro-bounds-constant-array-index) - Bounds checked above
      return m_keys[key] == KeyState::KEY_UP;
    }
    return true;
  }
}
