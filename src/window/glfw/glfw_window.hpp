#ifndef __IF__GLFW_WINDOW_HPP
#define __IF__GLFW_WINDOW_HPP

#define GLFW_INCLUDE_NONE
// C++ standard library
#include <memory>
#include <string>

// Third-party libraries
#include <GLFW/glfw3.h>

// Local includes
#include "../window.hpp"

namespace IronFrost {
  class GLFW_Mouse;
  class GLFW_Keyboard;

  class GLFW_Window : public IWindow {
    private:
      GLFWwindow* m_window;

      std::unique_ptr<GLFW_Keyboard> m_keyboard;
      std::unique_ptr<GLFW_Mouse> m_mouse;

      GLFW_Window(int width, int height, const std::string& title);

    public:
      ~GLFW_Window() override;

      static std::unique_ptr<GLFW_Window> create(int width, int height, const std::string& title);

      bool isInitialized() const;

      void swapBuffers() const override;
      void pollEvents() const override;

      bool shouldClose() const override;
      void close() const override;

      void* getRawHandle() const override;

      IKeyboard& getKeyboard() const override;
      IMouse& getMouse() const override;
    };
}

#endif
