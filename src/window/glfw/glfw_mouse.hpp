#ifndef __IF__GLFW_MOUSE_HPP
#define __IF__GLFW_MOUSE_HPP

#define GLFW_INCLUDE_NONE
// C++ standard library
#include <unordered_map>

// Third-party libraries
#include <GLFW/glfw3.h>

// Local includes
#include "../window.hpp"

namespace IronFrost {
  class GLFW_Mouse : public IMouse {
    private:
      GLFWwindow* m_window;
      
      double m_xpos, m_ypos;
      double m_relative_xpos, m_relative_ypos;

      MouseButtonState m_buttons[MOUSE_BUTTON_NUM_OF_TYPES] = {MOUSE_BUTTON_UP};

      const static std::unordered_map<int, MouseButtonType> buttonMap;

      void setupMouseMovementCallback(GLFWwindow* window);
      void setupMouseButtonCallback(GLFWwindow* window);

    public:
      explicit GLFW_Mouse(GLFWwindow* window);

      double getXPos() const override;
      double getYPos() const override;

      double getRelativeXPos() const override;
      double getRelativeYPos() const override;
      void resetRelativePosition() override;

      bool isButtonPressed(MouseButtonType button) const override;
      bool isButtonReleased(MouseButtonType button) const override;

      void hideCursor() override;
      void showCursor() override;
      void toggleCursor() override;

      // Public method for testing - this is called by GLFW callbacks
      void updateButtonState(MouseButtonType button, MouseButtonState state);
    };
}

#endif
