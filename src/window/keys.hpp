#ifndef __IF__KEYS_HPP
#define __IF__KEYS_HPP

namespace IronFrost {
  enum KeyType {
    KEY_ESCAPE,
    KEY_ARROW_UP,
    KEY_ARROW_DOWN,
    KEY_ARROW_LEFT,
    <PERSON><PERSON><PERSON>_ARROW_RIGHT,
    <PERSON><PERSON><PERSON>_TILDE,
    <PERSON><PERSON><PERSON>_BACKSPACE,
    <PERSON>EY_ENTER,
    KEY_0,
    KEY_1,
    KEY_2,
    KEY_3,
    KEY_4,
    KEY_5,
    KEY_6,
    KEY_7,
    KEY_8,
    KEY_9,
    KEY_A,
    KEY_B,
    KEY_C,
    KEY_D,
    <PERSON>EY_<PERSON>,
    KEY_F,
    <PERSON>EY_G,
    KEY_H,
    KEY_I,
    <PERSON>EY_<PERSON>,
    <PERSON>EY_K,
    KEY_L,
    KEY_M,
    KEY_N,
    <PERSON>EY_<PERSON>,
    <PERSON><PERSON><PERSON>_<PERSON>,
    <PERSON><PERSON><PERSON>_<PERSON>,
    <PERSON><PERSON><PERSON>_<PERSON>,
    <PERSON><PERSON><PERSON>_<PERSON>,
    <PERSON><PERSON><PERSON>_<PERSON>,
    <PERSON><PERSON><PERSON>_<PERSON>,
    <PERSON><PERSON><PERSON>_<PERSON>,
    <PERSON><PERSON><PERSON>_<PERSON>,
    <PERSON><PERSON><PERSON>_<PERSON>,
    <PERSON><PERSON><PERSON>_Y,
    <PERSON><PERSON><PERSON>_<PERSON>,
    <PERSON><PERSON><PERSON>_SPACE,
    <PERSON><PERSON><PERSON>_<PERSON><PERSON>_OF_KEYS
  };

  enum KeyState {
    KEY_UP,
    <PERSON>EY_DOWN,
    KEY_NUM_OF_STATES
  };
}

#endif
