#include "assets_loader.hpp"

// C++ standard library
#include <functional>
#include <iostream>
#include <memory>
#include <string>
#include <unordered_map>
#include <utility>
#include <vector>

// Local includes
#include "../utils/string_id.hpp"
#include "../vfs/vfs.hpp"
#include "assets_manager.hpp"
#include "asset_types.hpp"
#include "asset_data_types.hpp"
#include "asset_events.hpp"
#include "loaders/image_loader.hpp"
#include "processors/image_atlas_processor.hpp"
#include "writers/image_writer.hpp"
#include "generators/default_texture_generator.hpp"

namespace IronFrost {
  const std::unordered_map<std::string, PrimitiveType> AssetsLoader::s_primitiveTypes = {
    {"triangle", PrimitiveType::TRIANGLE},
    {"quad", PrimitiveType::QUAD},
    {"cube", PrimitiveType::CUBE},
    {"cone", PrimitiveType::CONE},
    {"cylinder", PrimitiveType::CYLINDER},
    {"sphere", PrimitiveType::SPHERE},
    {"plane", PrimitiveType::PLANE}
  };

  BlinnPhongMaterialData AssetsLoader::loadBlinnPhongMaterial(const json& materialConfig) const {
    BlinnPhongMaterialData blinnPhongMaterialData;

    blinnPhongMaterialData.ambient = materialConfig.contains("ambient") 
      ? glm::vec3{ materialConfig["ambient"][0], materialConfig["ambient"][1], materialConfig["ambient"][2] } 
      : glm::vec3(1.0F);

    blinnPhongMaterialData.diffuse = materialConfig.contains("diffuse")
      ? glm::vec3{ materialConfig["diffuse"][0], materialConfig["diffuse"][1], materialConfig["diffuse"][2] }
      : glm::vec3(1.0F);

    blinnPhongMaterialData.specular = materialConfig.contains("specular")
      ? glm::vec3{ materialConfig["specular"][0], materialConfig["specular"][1], materialConfig["specular"][2] }
      : glm::vec3(1.0F);

    blinnPhongMaterialData.shininess = materialConfig.contains("shininess")
      ? materialConfig["shininess"].get<float>()
      : 32.0F;

    blinnPhongMaterialData.textureName = StringID(materialConfig.contains("textureName") ? materialConfig["textureName"] : "");

    return blinnPhongMaterialData;
  }

  PBRMaterialData AssetsLoader::loadPBRMaterial(const json& materialConfig) const {
    if (!materialConfig.contains("baseColor")) {
      throw std::runtime_error("PBR material must have a baseColor texture");
    }

    PBRMaterialData pbrMaterialData;
    pbrMaterialData.textureArray.reserve(6);

    auto baseColorTexture = m_assetsManager.loadImage(materialConfig["baseColor"]);
    int width = baseColorTexture->width;
    int height = baseColorTexture->height;

    struct TextureInfo {
      const char* key;
      std::function<std::unique_ptr<ImageData>(int, int)> defaultGenerator;
    };

    std::vector<TextureInfo> textureTypes = {
      {"baseColor", [](int w, int h) { return DefaultTextureGenerator::createBaseColorTexture(w, h); }},
      {"normal", [](int w, int h) { return DefaultTextureGenerator::createNormalTexture(w, h); }},
      {"metallic", [](int w, int h) { return DefaultTextureGenerator::createMetallicTexture(w, h); }},
      {"roughness", [](int w, int h) { return DefaultTextureGenerator::createRoughnessTexture(w, h); }},
      {"ao", [](int w, int h) { return DefaultTextureGenerator::createAOTexture(w, h); }},
      {"emissive", [](int w, int h) { return DefaultTextureGenerator::createEmissiveTexture(w, h); }}
    };

    pbrMaterialData.textureArray.emplace_back(std::move(baseColorTexture));

    for (size_t i = 1; i < textureTypes.size(); i++) {
      const auto& textureType = textureTypes[i];

      if (materialConfig.contains(textureType.key)) {
        pbrMaterialData.textureArray.emplace_back(m_assetsManager.loadImage(materialConfig[textureType.key]));
      } else {
        pbrMaterialData.textureArray.emplace_back(textureType.defaultGenerator(width, height));
      }
    }

    return pbrMaterialData;
  }

  template<>
  void AssetsLoader::load<AssetType::MESH>(const json& assetConfig, bool async) {
    StringID name = StringID(assetConfig["name"]);
    std::string type = assetConfig["type"];

    if(type == "primitive") {
      std::string primitive = assetConfig["primitive"];
      auto params = PrimitiveParams::fromJSON(assetConfig);

      const MeshData& meshData = m_assetsManager.createPrimitive(name, s_primitiveTypes.at(primitive), params);
      dispatchEvent<LoadMeshEvent, MeshData>(name, meshData, async);
    }
  };

  template<>
  void AssetsLoader::load<AssetType::MODEL>(const json& assetConfig, bool async) {
    StringID name = StringID(assetConfig["name"]);
    std::string path = assetConfig["path"];

    const ModelData& modelData = m_assetsManager.loadModel(name, path);
    dispatchEvent<LoadModelEvent, ModelData>(name, modelData, async);
  };

  template<>
  void AssetsLoader::load<AssetType::SHADER>(const json& assetConfig, bool async) {
    StringID name = StringID(assetConfig["name"]);
    std::string path = assetConfig["path"];

    const ShaderData& shaderData = m_assetsManager.loadShader(name, path);
    dispatchEvent<LoadShaderEvent, ShaderData>(name, shaderData, async);
  };

  template<>
  void AssetsLoader::load<AssetType::IMAGE>(const json& assetConfig, bool async) {
    StringID name = StringID(assetConfig["name"]);
    std::string path = assetConfig["path"];

    const ImageData& imageData = m_assetsManager.loadImage(name, path);
    dispatchEvent<LoadTextureEvent, ImageData>(name, imageData, async);
  };

  template<>
  void AssetsLoader::load<AssetType::AUDIO>(const json& assetConfig, bool async) {
    StringID name = StringID(assetConfig["name"]);
    std::string path = assetConfig["path"];

    const AudioData& audioData = m_assetsManager.loadAudio(name, path);
    dispatchEvent<LoadAudioEvent, AudioData>(name, audioData, async);
  };

  template<>
  void AssetsLoader::load<AssetType::FONT>(const json& assetConfig, bool async) {
    StringID name = StringID(assetConfig["name"]);
    std::string path = assetConfig["path"];
    int size = assetConfig["size"];

    const FontData& fontData = m_assetsManager.loadFont(name, path, size);
    dispatchEvent<LoadFontEvent, FontData>(name, fontData, async);
  };
  
  template<>
  void AssetsLoader::load<AssetType::MATERIAL>(const json& assetConfig, bool async) {
    StringID name = StringID(assetConfig["name"]);
    std::string type = assetConfig["type"];

    if (type == "phong") {
      auto materialData = std::make_unique<MaterialData>(loadBlinnPhongMaterial(assetConfig));
      const MaterialData& storedMaterialData = m_assetsManager.storeMaterial(name, std::move(materialData));
      dispatchEvent<LoadMaterialEvent, MaterialData>(name, storedMaterialData, async);
    } else if (type == "pbr") {
      auto materialData = std::make_unique<MaterialData>(loadPBRMaterial(assetConfig));
      const MaterialData& storedMaterialData = m_assetsManager.storeMaterial(name, std::move(materialData));
      dispatchEvent<LoadMaterialEvent, MaterialData>(name, storedMaterialData, async);
    }
  };

  template<>
  void AssetsLoader::load<AssetType::POSTPROCESS_EFFECT>(const json& assetConfig, bool async) {
    StringID name = StringID(assetConfig["name"]);
    std::vector<std::string> shaderNames = assetConfig["shaders"].get<std::vector<std::string>>();

    const PostprocessData& postprocessData = PostprocessData{shaderNames};
    m_eventDispatcher.dispatch<LoadPostprocessEffectEvent>(name, postprocessData);
    dispatchEvent<LoadPostprocessEffectEvent, PostprocessData>(name, postprocessData, async);
  }

  template<>
  void AssetsLoader::load<AssetType::HEIGHTMAP>(const json& assetConfig, bool async) {
    StringID name = StringID(assetConfig["name"]);
    std::string path = assetConfig["path"];

    const HeightmapData& heightmapData = m_assetsManager.loadHeightmap(name, path);
    dispatchEvent<LoadHeightmapEvent, HeightmapData>(name, heightmapData, async);
  }

  template<>
  void AssetsLoader::load<AssetType::TERRAIN>(const json& assetConfig, bool async) {
    StringID name = StringID(assetConfig["name"]);
    std::string heightmapPath = assetConfig["heightmap"];
    float heightScale = assetConfig.value("heightScale", 1.0f);
    float blockSize = assetConfig.value("blockSize", 4.0f);

    const TerrainData& terrainData = m_assetsManager.loadTerrain(name, TerrainParams{heightmapPath, heightScale, blockSize});
    dispatchEvent<LoadTerrainEvent, TerrainData>(name, terrainData, async);
  }

  const std::unordered_map<AssetType, AssetsLoader::AssetTypeFn, AssetsLoader::AssetTypeHash> AssetsLoader::s_dispatchMap = {
    {AssetType::MESH, &AssetsLoader::load<AssetType::MESH>},
    {AssetType::MODEL, &AssetsLoader::load<AssetType::MODEL>},
    {AssetType::SHADER, &AssetsLoader::load<AssetType::SHADER>},
    {AssetType::IMAGE, &AssetsLoader::load<AssetType::IMAGE>},
    {AssetType::AUDIO, &AssetsLoader::load<AssetType::AUDIO>},
    {AssetType::FONT, &AssetsLoader::load<AssetType::FONT>},
    {AssetType::MATERIAL, &AssetsLoader::load<AssetType::MATERIAL>},
    {AssetType::POSTPROCESS_EFFECT, &AssetsLoader::load<AssetType::POSTPROCESS_EFFECT>},
    {AssetType::HEIGHTMAP, &AssetsLoader::load<AssetType::HEIGHTMAP>},
    {AssetType::TERRAIN, &AssetsLoader::load<AssetType::TERRAIN>}
  };

  void AssetsLoader::load(const AssetInfo& assetInfo) {
    for (const auto& [type, fn] : s_dispatchMap) {
      if (type == assetInfo.type) {
        (this->*fn)(assetInfo.config, false);
        return;
      }
    }
    
    throw std::runtime_error("Unknown asset type: " + std::to_string(static_cast<int>(assetInfo.type)));
  }

  void AssetsLoader::loadAsync(const AssetInfo& assetInfo) {
    for (const auto& [type, fn] : s_dispatchMap) {
      if (type == assetInfo.type) {
        (this->*fn)(assetInfo.config, true);
        return;
      }
    }
    
    throw std::runtime_error("Unknown asset type: " + std::to_string(static_cast<int>(assetInfo.type)));
  }
    
}
