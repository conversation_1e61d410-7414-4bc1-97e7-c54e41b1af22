#ifndef __IF__ASSET_DATA_TYPE_MODEL_HPP
#define __IF__ASSET_DATA_TYPE_MODEL_HPP

// C++ standard library
#include <vector>
#include <memory>

// Third-party libraries
#include <glm/glm.hpp>

// Local includes
#include "../../utils/collision_math.hpp"
#include "mesh_data.hpp"
#include "image_data.hpp"

namespace IronFrost {
  struct ModelDataNode {
    glm::mat4 transform{1.0f};

    std::vector<std::unique_ptr<MeshData>> meshes;
    std::vector<std::unique_ptr<ImageData>> textures;
    std::vector<ModelDataNode> children;
  };

  struct ModelData {
    ModelData() = default;

    // Copy semantics are deleted
    ModelData(const ModelData&) = delete;
    ModelData& operator=(const ModelData&) = delete;

    // Move semantics are enabled
    ModelData(ModelData&&) = default;
    ModelData& operator=(ModelData&&) = default;

    ModelDataNode rootNode;

    CollisionMath::AABB bounds{glm::vec3(0.0f), glm::vec3(0.0f)};
  };
}

#endif
