#ifndef __IF__ASSET_DATA_TYPE_FONT_HPP
#define __IF__ASSET_DATA_TYPE_FONT_HPP

// C++ standard library
#include <vector>

// Third-party libraries
#include <glm/glm.hpp>

namespace IronFrost {
  struct GlyphData {
    glm::vec2 size;
    glm::vec2 bearing;
    unsigned int advance;

    std::vector<unsigned char> bitmap;
  };

  struct FontData {
    FontData() = default;

    // Copy semantics are deleted
    FontData(const FontData&) = delete;
    FontData& operator=(const FontData&) = delete;

    // Move semantics are enabled
    FontData(FontData&&) = default;
    FontData& operator=(FontData&&) = default;

    std::vector<GlyphData> glyphs;
    unsigned int size;
    unsigned int lineHeight;
  };
}

#endif
