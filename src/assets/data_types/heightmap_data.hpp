#ifndef __IF__ASSET_DATA_TYPE_HEIGHTMAP_HPP
#define __IF__ASSET_DATA_TYPE_HEIGHTMAP_HPP

// C++ standard library
#include <vector>

namespace IronFrost {
  struct HeightmapData {
    HeightmapData() = default;

    // Copy semantics are deleted
    HeightmapData(const HeightmapData&) = delete;
    HeightmapData& operator=(const HeightmapData&) = delete;

    // Move semantics are enabled
    HeightmapData(HeightmapData&&) = default;
    HeightmapData& operator=(HeightmapData&&) = default;

    std::vector<float> heightmap;

    int width{0};
    int height{0};
    
    float getHeight(int x, int y) const {
      if (x < 0 || x >= width || y < 0 || y >= height) {
        return 0.0f;
      }

      return heightmap[y * width + x];
    }

    float getInterpolatedHeight(float x, float y) const {
      int x0 = static_cast<int>(std::floor(x));
      int y0 = static_cast<int>(std::floor(y));
      int x1 = x0 + 1;
      int y1 = y0 + 1;

      float dx = x - x0;
      float dy = y - y0;

      float h00 = getHeight(x0, y0);
      float h01 = getHeight(x0, y1);
      float h10 = getHeight(x1, y0);
      float h11 = getHeight(x1, y1);

      float h0 = h00 + (h10 - h00) * dx;
      float h1 = h01 + (h11 - h01) * dx;
      return h0 + (h1 - h0) * dy;
    }
  };
}

#endif
