#ifndef __IF__ASSET_DATA_TYPE_VERTEX_HPP
#define __IF__ASSET_DATA_TYPE_VERTEX_HPP

// C++ standard library
#include <array>

// Third-party libraries
#include <glm/glm.hpp>

namespace IronFrost {
  // pos.x pos.y pos.z uv.x uv.y norm.x norm.y norm.z tan.x tan.y tan.z btan.x btan.y btan.z
  
  struct Vertex {
    std::array<float, 14> data{0.0f};

    glm::vec3 getPosition() const {
      return glm::vec3(data[0], data[1], data[2]);
    }

    glm::vec2 getUV() const {
      return glm::vec2(data[3], data[4]);
    }

    void setNormal(const glm::vec3& normal) {
      data[5] = normal.x;
      data[6] = normal.y;
      data[7] = normal.z;
    }

    glm::vec3 getNormal() const {
      return glm::vec3(data[5], data[6], data[7]);
    }

    void setTangent(const glm::vec3& tangent) {
      data[8] = tangent.x;
      data[9] = tangent.y;
      data[10] = tangent.z;
    }

    glm::vec3 getTangent() const {
      return glm::vec3(data[8], data[9], data[10]);
    }

    void setBitangent(const glm::vec3& bitangent) {
      data[11] = bitangent.x;
      data[12] = bitangent.y;
      data[13] = bitangent.z;
    }

    glm::vec3 getBitangent() const {
      return glm::vec3(data[11], data[12], data[13]);
    }
  };
}

#endif
