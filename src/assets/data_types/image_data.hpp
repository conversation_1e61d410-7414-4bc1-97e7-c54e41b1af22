#ifndef __IF__ASSET_DATA_TYPE_IMAGE_HPP
#define __IF__ASSET_DATA_TYPE_IMAGE_HPP

// C++ standard library
#include <memory>

namespace IronFrost {
  struct ImageData {
    ImageData();

    // Copy semantics are deleted
    ImageData(const ImageData&) = delete;
    ImageData& operator=(const ImageData&) = delete;

    // Move semantics are enabled
    ImageData(ImageData&&) = default;
    ImageData& operator=(ImageData&&) = default;

    int width{0};
    int height{0};
    int channels{0};

    std::shared_ptr<void> data;

    bool is16bit{false};

    unsigned char* get8BitData() const {
      return static_cast<unsigned char*>(data.get());
    }
    unsigned short* get16BitData() const {
      return static_cast<unsigned short*>(data.get());
    }
  };
}

#endif
