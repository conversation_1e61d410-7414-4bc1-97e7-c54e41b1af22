#ifndef __IF__ASSET_DATA_TYPE_MATERIAL_HPP
#define __IF__ASSET_DATA_TYPE_MATERIAL_HPP

// C++ standard library
#include <vector>
#include <memory>
#include <variant>

// Local includes
#include "../../utils/string_id.hpp"

namespace IronFrost {
  struct BlinnPhongMaterialData {
    glm::vec3 ambient;
    glm::vec3 diffuse;
    glm::vec3 specular;
    float shininess;

    StringID textureName;
  };

  struct PBRMaterialData {
    std::vector<std::unique_ptr<ImageData>> textureArray{};
  };

  struct MaterialData {
    MaterialData(std::variant<BlinnPhongMaterialData, PBRMaterialData> data) : data(std::move(data)) {}

    MaterialData() = default;

    // Copy semantics are deleted
    MaterialData(const MaterialData&) = delete;
    MaterialData& operator=(const MaterialData&) = delete;

    // Move semantics are enabled
    MaterialData(MaterialData&&) = default;
    MaterialData& operator=(MaterialData&&) = default;

    std::variant<BlinnPhongMaterialData, PBRMaterialData> data{};
  };
}

#endif
