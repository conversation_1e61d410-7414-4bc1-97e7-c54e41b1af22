#ifndef __IF__ASSET_DATA_TYPE_IMAGE_ATLAS_HPP
#define __IF__ASSET_DATA_TYPE_IMAGE_ATLAS_HPP

// C++ standard library
#include <memory>
#include <vector>

// Third-party libraries
#include <glm/glm.hpp>

namespace IronFrost {
  struct ImageAtlasData {
    struct Entry {
      glm::vec2 position;
      glm::vec2 uvMin;
      glm::vec2 uvMax;

      int width{0};
      int height{0};
    };

    ImageAtlasData() = default;

    // Copy semantics are deleted
    ImageAtlasData(const ImageAtlasData&) = delete;
    ImageAtlasData& operator=(const ImageAtlasData&) = delete;

    // Move semantics are enabled
    ImageAtlasData(ImageAtlasData&&) = default;
    ImageAtlasData& operator=(ImageAtlasData&&) = default;

    std::unique_ptr<ImageData> imageData;
    std::vector<Entry> entries;
  };
}

#endif
