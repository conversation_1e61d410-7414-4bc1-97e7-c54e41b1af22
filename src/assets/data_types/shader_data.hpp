#ifndef __IF__ASSET_DATA_TYPE_SHADER_HPP
#define __IF__ASSET_DATA_TYPE_SHADER_HPP

// C++ standard library
#include <string>

namespace IronFrost {
  struct ShaderData {
    ShaderData(std::string vertexShader, std::string fragmentShader)
      : vertexShader(vertexShader), fragmentShader(fragmentShader) {}

    ShaderData() = default;

    // Copy semantics are deleted
    ShaderData(const ShaderData&) = delete;
    ShaderData& operator=(const ShaderData&) = delete;

    // Move semantics are enabled
    ShaderData(ShaderData&&) = default;
    ShaderData& operator=(ShaderData&&) = default;

    std::string vertexShader;
    std::string fragmentShader;
  };
}

#endif
