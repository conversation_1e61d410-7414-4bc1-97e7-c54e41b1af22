#ifndef __IF__ASSET_DATA_TYPE_AUDIO_HPP
#define __IF__ASSET_DATA_TYPE_AUDIO_HPP

// C++ standard library
#include <vector>

namespace IronFrost {
  struct AudioData {
    AudioData(std::vector<float> samples, int sampleRate, int channels, float duration)
      : samples(samples), sampleRate(sampleRate), channels(channels), duration(duration) {}

    AudioData() = default;

    // Copy semantics are deleted
    AudioData(const AudioData&) = delete;
    AudioData& operator=(const AudioData&) = delete;

    // Move semantics are enabled
    AudioData(AudioData&&) = default;
    AudioData& operator=(AudioData&&) = default;

    std::vector<float> samples;  // Normalized float samples (-1.0 to 1.0)
    int sampleRate;              // Sample rate (e.g., 44100 Hz)
    int channels;                // Number of channels (1 = mono, 2 = stereo)
    float duration;              // Duration in seconds

    size_t getFrameCount() const {
      return samples.size() / channels;
    }

    std::vector<int16_t> getInt16Samples() const {
      std::vector<int16_t> int16Samples;
      int16Samples.reserve(samples.size());

      for (float sample : samples) {
        float clamped = std::max(-1.0f, std::min(1.0f, sample));
        int16Samples.push_back(static_cast<int16_t>(clamped * 32767.0f));
      }

      return int16Samples;
    }
  };
}

#endif
