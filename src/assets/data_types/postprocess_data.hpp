#ifndef __IF__ASSET_DATA_TYPE_POSTPROCESS_HPP
#define __IF__ASSET_DATA_TYPE_POSTPROCESS_HPP

// C++ standard library
#include <vector>
#include <string>

namespace IronFrost {
  struct PostprocessData {
    PostprocessData(std::vector<std::string> shaderNames) : shaderNames(shaderNames) {}

    PostprocessData() = default;

    // Copy semantics are deleted
    PostprocessData(const PostprocessData&) = delete;
    PostprocessData& operator=(const PostprocessData&) = delete;

    // Move semantics are enabled
    PostprocessData(PostprocessData&&) = default;
    PostprocessData& operator=(PostprocessData&&) = default;

    std::vector<std::string> shaderNames;
  };
}

#endif
