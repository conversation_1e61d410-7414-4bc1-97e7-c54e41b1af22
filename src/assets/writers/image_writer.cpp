#include "image_writer.hpp"

// C++ standard library
#include <string>
#include <vector>

// Third-party libraries
#include <stb_image_write.h>

// Local includes
#include "../../vfs/vfs.hpp"
#include "../asset_data_types.hpp"

namespace IronFrost {

  // STB callback function for writing data
  static void stbWriteCallback(void* context, void* data, int size) {
    auto* buffer = static_cast<std::vector<unsigned char>*>(context);

    // Accumulate data in buffer
    const auto* byteData = static_cast<const unsigned char*>(data);
    buffer->insert(buffer->end(), byteData, byteData + size);
  }

  ImageWriter::ImageWriter(IVFS& vfs) : m_vfs{vfs} {}

  bool ImageWriter::saveImage(const ImageData& imageData, const std::string& filePath) {
    int strideInBytes = imageData.width * imageData.channels;

    // Buffer to accumulate PNG data
    std::vector<unsigned char> buffer;

    // Write PNG data using callback
    int result = stbi_write_png_to_func(
        stbWriteCallback,
        &buffer,
        imageData.width,
        imageData.height,
        imageData.channels,
        imageData.data.get(),
        strideInBytes
    );

    if (result == 0 || buffer.empty()) {
        return false;
    }

    // Write binary data through VFS
    return m_vfs.writeFileBinary(filePath, buffer);
  }
}
