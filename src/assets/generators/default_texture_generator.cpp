// Local includes
#include "default_texture_generator.hpp"

namespace IronFrost {
  
  std::unique_ptr<ImageData> DefaultTextureGenerator::createBaseColorTexture(int width, int height) {
    return createSolidColorTexture(255, 255, 255, 255, width, height); // White
  }

  std::unique_ptr<ImageData> DefaultTextureGenerator::createNormalTexture(int width, int height) {
    return createSolidColorTexture(128, 128, 255, 255, width, height); // Flat normal pointing up
  }

  std::unique_ptr<ImageData> DefaultTextureGenerator::createMetallicTexture(int width, int height) {
    return createSolidColorTexture(0, 0, 0, 255, width, height); // Non-metallic (black)
  }

  std::unique_ptr<ImageData> DefaultTextureGenerator::createRoughnessTexture(int width, int height) {
    return createSolidColorTexture(128, 128, 128, 255, width, height); // Medium roughness (gray)
  }

  std::unique_ptr<ImageData> DefaultTextureGenerator::createAOTexture(int width, int height) {
    return createSolidColorTexture(255, 255, 255, 255, width, height); // No ambient occlusion (white)
  }

  std::unique_ptr<ImageData> DefaultTextureGenerator::createEmissiveTexture(int width, int height) {
    return createSolidColorTexture(0, 0, 0, 255, width, height); // No emission
  }
  
  std::unique_ptr<ImageData> DefaultTextureGenerator::createSolidColorTexture(
    unsigned char r, unsigned char g, unsigned char b, unsigned char a, int width, int height) {

    auto imageData = createImageData(width, height, 4);

    int pixelCount = width * height;
    unsigned char* data = new unsigned char[pixelCount * 4];

    for (int i = 0; i < pixelCount; i++) {
      data[i * 4 + 0] = r;
      data[i * 4 + 1] = g;
      data[i * 4 + 2] = b;
      data[i * 4 + 3] = a;
    }

    imageData->data = std::shared_ptr<void>(data, [](void* ptr) {
      delete[] static_cast<unsigned char*>(ptr);
    });

    return imageData;
  }

  std::unique_ptr<ImageData> DefaultTextureGenerator::createGrayscaleTexture(unsigned char value, int width, int height) {
    auto imageData = createImageData(width, height, 1);

    int pixelCount = width * height;
    unsigned char* data = new unsigned char[pixelCount];

    for (int i = 0; i < pixelCount; i++) {
      data[i] = value;
    }

    imageData->data = std::shared_ptr<void>(data, [](void* ptr) {
      delete[] static_cast<unsigned char*>(ptr);
    });

    return imageData;
  }
  
  std::unique_ptr<ImageData> DefaultTextureGenerator::createImageData(int width, int height, int channels) {
    auto imageData = std::make_unique<ImageData>();
    imageData->width = width;
    imageData->height = height;
    imageData->channels = channels;
    imageData->is16bit = false;
    return imageData;
  }
  
} // namespace IronFrost
