#ifndef __IF__DEFAULT_TEXTURE_GENERATOR_HPP
#define __IF__DEFAULT_TEXTURE_GENERATOR_HPP

// C++ standard library
#include <memory>

// Local includes
#include "../asset_data_types.hpp"

namespace IronFrost {
  
  /**
   * Generates default textures for PBR materials when specific texture types are missing
   * Provides sensible defaults that work well with PBR rendering
   */
  class DefaultTextureGenerator {
    public:
      /**
       * Creates a white base color texture (255, 255, 255)
       * Used when albedo/diffuse texture is missing
       */
      static std::unique_ptr<ImageData> createBaseColorTexture(int width = 1, int height = 1);

      /**
       * Creates a flat normal map texture (128, 128, 255)
       * Points straight up in tangent space - no surface detail
       */
      static std::unique_ptr<ImageData> createNormalTexture(int width = 1, int height = 1);

      /**
       * Creates a non-metallic texture (0)
       * Represents dielectric materials (plastic, wood, etc.)
       */
      static std::unique_ptr<ImageData> createMetallicTexture(int width = 1, int height = 1);

      /**
       * Creates a medium roughness texture (128 = 0.5 roughness)
       * Balanced between shiny and matte appearance
       */
      static std::unique_ptr<ImageData> createRoughnessTexture(int width = 1, int height = 1);

      /**
       * Creates a white ambient occlusion texture (255)
       * No ambient occlusion - full ambient lighting
       */
      static std::unique_ptr<ImageData> createAOTexture(int width = 1, int height = 1);

      /**
       * Creates a black emissive texture (0, 0, 0)
       * No self-illumination
       */
      static std::unique_ptr<ImageData> createEmissiveTexture(int width = 1, int height = 1);
      
      /**
       * Creates a texture with the specified color and dimensions
       * Utility method for creating solid color textures
       */
      static std::unique_ptr<ImageData> createSolidColorTexture(
        unsigned char r, unsigned char g, unsigned char b, unsigned char a = 255,
        int width = 1, int height = 1);

      /**
       * Creates a grayscale texture with the specified value and dimensions
       * Utility method for creating single-channel textures
       */
      static std::unique_ptr<ImageData> createGrayscaleTexture(unsigned char value, int width = 1, int height = 1);
      
    private:
      /**
       * Helper method to create a basic ImageData structure
       */
      static std::unique_ptr<ImageData> createImageData(int width, int height, int channels);
  };
  
} // namespace IronFrost

#endif
