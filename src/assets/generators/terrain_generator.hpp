#ifndef __IF__TERRAIN_GENERATOR_HPP
#define __IF__TERRAIN_GENERATOR_HPP

// C++ standard library
#include <memory>
#include <vector>

// Local includes
#include "../asset_data_types.hpp"

namespace IronFrost {
  class TerrainGenerator {
    public:
      static std::unique_ptr<TerrainData> generateTerrain(const HeightmapData& heightmapData, float heightScale = 1.0f, float blockSize = 4.0f); 
  };
}

#endif
