#ifndef __IF__IMAGE_ATLAS_PROCESSOR_HPP
#define __IF__IMAGE_ATLAS_PROCESSOR_HPP

// C standard library
#include <cmath>
#include <cstring>

// C++ standard library
#include <exception>
#include <memory>
#include <utility>
#include <vector>

namespace IronFrost {
  /**
   * @brief Processes multiple images into a single texture atlas
   *
   * The ImageAtlasProcessor takes multiple images of the same dimensions and channels
   * and combines them into a single texture atlas with proper UV coordinates.
   * It automatically calculates the optimal grid layout and adds padding with edge
   * extension to prevent texture bleeding during filtering.
   */
  class ImageAtlasProcessor {
    private:
      static constexpr int DEFAULT_PADDING = 8;        // Padding between images in atlas
      static constexpr int EDGE_EXTENSION_SIZE = 4;    // Pixels to extend for edge bleeding prevention

      int m_padding{DEFAULT_PADDING};
      std::vector<std::unique_ptr<ImageData>> m_images;

      /**
       * @brief Metrics for atlas layout calculation
       */
      struct AtlasMetrics {
        int gridSize;           // Grid dimensions (gridSize x gridSize)
        int atlasWidth;         // Total atlas width in pixels
        int atlasHeight;        // Total atlas height in pixels
        int baseImageWidth;     // Width of individual images
        int baseImageHeight;    // Height of individual images
        int baseImageChannels;  // Number of channels per image
      };

      AtlasMetrics calculateAtlasMetrics() const {
        if (m_images.empty()) {
          throw std::runtime_error("No images available for atlas generation");
        }

        const auto& baseImage = m_images[0];
        const size_t numOfImages = m_images.size();
        const int gridSize = static_cast<int>(std::ceil(std::sqrt(numOfImages)));

        return AtlasMetrics{
          gridSize,
          gridSize * (baseImage->width + m_padding) + m_padding,
          gridSize * (baseImage->height + m_padding) + m_padding,
          baseImage->width,
          baseImage->height,
          baseImage->channels
        };
      }

      void validateImages() const {
        if (m_images.empty()) return;

        const auto& baseImage = m_images[0];

        for (size_t i = 1; i < m_images.size(); ++i) {
          const auto& image = m_images[i];
          if (image->width != baseImage->width || image->height != baseImage->height || image->channels != baseImage->channels) {
            throw std::runtime_error("All images must have the same dimensions and channels");
          }
        }
      }

      glm::vec2 calculateUVMin(const AtlasMetrics& metrics, int x, int y) {
        return glm::vec2{
          static_cast<float>(x) / metrics.atlasWidth,
          static_cast<float>(y) / metrics.atlasHeight
        };
      }

      glm::vec2 calculateUVMax(const AtlasMetrics& metrics, int x, int y) {
        return glm::vec2{
          static_cast<float>(x + metrics.baseImageWidth) / metrics.atlasWidth,
          static_cast<float>(y + metrics.baseImageHeight) / metrics.atlasHeight
        };
      }

      void addEntry(const AtlasMetrics& metrics, ImageAtlasData* imageAtlasData, int currentX, int currentY) {
        imageAtlasData->entries.push_back({
          glm::vec2{currentX, currentY},
          calculateUVMin(metrics, currentX, currentY),
          calculateUVMax(metrics, currentX, currentY),
          metrics.baseImageWidth,
          metrics.baseImageHeight
        });
      }
      
      std::unique_ptr<ImageAtlasData> createAtlas(const AtlasMetrics& metrics) {
        std::unique_ptr<ImageAtlasData> imageAtlasData = std::make_unique<ImageAtlasData>();
        imageAtlasData->imageData = std::make_unique<ImageData>();
    
        ImageData* imageData = imageAtlasData->imageData.get();
        imageData->data = std::make_shared<unsigned char[]>(
          metrics.atlasWidth * metrics.atlasHeight * metrics.baseImageChannels
        );
        imageData->width = metrics.atlasWidth;
        imageData->height = metrics.atlasHeight;
        imageData->channels = metrics.baseImageChannels;
    
        imageAtlasData->entries.reserve(m_images.size());

        return imageAtlasData;
      }

      void copyImageCenterToAtlas(const AtlasMetrics& metrics, unsigned char* atlasData, const unsigned char* srcData, int currentX, int currentY) {
        const int channels = metrics.baseImageChannels;

        for (int row = 0; row < metrics.baseImageHeight; ++row) {
          int atlasRow = currentY + row;
          int atlasOffset = (atlasRow * metrics.atlasWidth + currentX) * channels;
          int imageOffset = row * metrics.baseImageWidth * channels;
          std::memcpy(atlasData + atlasOffset, srcData + imageOffset, metrics.baseImageWidth * channels);
        }
      }

      void copyVerticalEdgesToAtlas(const AtlasMetrics& metrics, unsigned char* atlasData, int currentX, int currentY) {
        const int channels = metrics.baseImageChannels;

        for (int i = 0; i < metrics.baseImageHeight; ++i) {
          for (int b = 1; b <= EDGE_EXTENSION_SIZE; ++b) {
            // Left edge - extend leftmost pixel
            int dstLeftOffset = ((currentY + i) * metrics.atlasWidth + (currentX - b)) * channels;
            int srcLeftOffset = ((currentY + i) * metrics.atlasWidth + currentX) * channels;
            std::memcpy(atlasData + dstLeftOffset, atlasData + srcLeftOffset, channels);

            // Right edge - extend rightmost pixel
            int dstRightOffset = ((currentY + i) * metrics.atlasWidth + (currentX + metrics.baseImageWidth + b - 1)) * channels;
            int srcRightOffset = ((currentY + i) * metrics.atlasWidth + (currentX + metrics.baseImageWidth - 1)) * channels;
            std::memcpy(atlasData + dstRightOffset, atlasData + srcRightOffset, channels);
          }
        }
      }

      void copyHorizontalEdgesToAtlas(const AtlasMetrics& metrics, unsigned char* atlasData, int currentX, int currentY) {
        const int channels = metrics.baseImageChannels;

        for (int i = -EDGE_EXTENSION_SIZE; i < metrics.baseImageWidth + EDGE_EXTENSION_SIZE; ++i) {
          for (int b = 1; b <= EDGE_EXTENSION_SIZE; ++b) {
            // Top edge - extend topmost pixel
            int dstTopOffset = ((currentY - b) * metrics.atlasWidth + (currentX + i)) * channels;
            int srcTopOffset = (currentY * metrics.atlasWidth + (currentX + i)) * channels;
            std::memcpy(atlasData + dstTopOffset, atlasData + srcTopOffset, channels);

            // Bottom edge - extend bottommost pixel
            int dstBottomOffset = ((currentY + metrics.baseImageHeight + b - 1) * metrics.atlasWidth + (currentX + i)) * channels;
            int srcBottomOffset = ((currentY + metrics.baseImageHeight - 1) * metrics.atlasWidth + (currentX + i)) * channels;
            std::memcpy(atlasData + dstBottomOffset, atlasData + srcBottomOffset, channels);
          }
        }
      }

      void copyImageToAtlas(const AtlasMetrics& metrics, ImageAtlasData* imageAtlasData, const ImageData* image, int currentX, int currentY) {
        unsigned char* atlasData = imageAtlasData->imageData->get8BitData();
        const unsigned char* srcData = image->get8BitData();

        // Copy the main image data
        copyImageCenterToAtlas(metrics, atlasData, srcData, currentX, currentY);

        // Copy edge borders for texture filtering (prevents seams)
        copyVerticalEdgesToAtlas(metrics, atlasData, currentX, currentY);
        copyHorizontalEdgesToAtlas(metrics, atlasData, currentX, currentY);
      }

      struct GridPosition {
        int x;
        int y;
        int column;
      };

      GridPosition calculateNextPosition(const AtlasMetrics& metrics, const GridPosition& current) {
        GridPosition next = current;
        next.column++;

        if (next.column >= metrics.gridSize) {
          // Move to next row
          next.column = 0;
          next.x = m_padding;
          next.y += metrics.baseImageHeight + m_padding;
        } else {
          // Move to next column
          next.x += metrics.baseImageWidth + m_padding;
        }

        return next;
      }

      void processImagesIntoAtlas(const AtlasMetrics& metrics, ImageAtlasData* imageAtlasData) {
        GridPosition position{m_padding, m_padding, 0};

        for (const auto& image : m_images) {
          addEntry(metrics, imageAtlasData, position.x, position.y);
          copyImageToAtlas(metrics, imageAtlasData, image.get(), position.x, position.y);
          position = calculateNextPosition(metrics, position);
        }
      }

    public:
      /**
       * @brief Add an image to be included in the atlas
       * @param image Unique pointer to image data (must have same dimensions and channels as other images)
       */
      void addImage(std::unique_ptr<ImageData> image) {
        m_images.push_back(std::move(image));
      }

      /**
       * @brief Generate the texture atlas from all added images
       * @return Unique pointer to ImageAtlasData containing the atlas texture and UV coordinates
       * @throws std::runtime_error if images have different dimensions or channel counts
       */
      std::unique_ptr<ImageAtlasData> get() {
        if (m_images.empty()) return nullptr;

        validateImages();

        const AtlasMetrics metrics = calculateAtlasMetrics();
        std::unique_ptr<ImageAtlasData> imageAtlasData = createAtlas(metrics);

        processImagesIntoAtlas(metrics, imageAtlasData.get());

        return imageAtlasData;
      }
  };
}


#endif
