#ifndef __IF__ASSET_TYPES_HPP
#define __IF__ASSET_TYPES_HPP

// Third-party libraries
#include <nlohmann/json.hpp>

using json = nlohmann::json;

namespace IronFrost {
  enum class AssetType {
    MESH,
    MODEL,
    SHADER,
    IMAGE,
    AUDIO,
    FONT,
    MATERIAL,
    POSTPROCESS_EFFECT,
    HEIGHTMAP,
    TERRAIN, 
    UNKNOWN
  };

  struct AssetInfo {
    AssetType type{AssetType::UNKNOWN};
    json config;
  };
}

#endif
