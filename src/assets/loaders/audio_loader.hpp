#ifndef __IF__AUDIO_LOADER_HPP
#define __IF__AUDIO_LOADER_HPP

// C++ standard library
#include <memory>
#include <string>

// Third-party libraries
#include <sndfile.h>

// Local includes
#include "../asset_data_types.hpp"

namespace IronFrost {
  class IVFS;

  class AudioLoader {
    private:
      IVFS& m_vfs;

      struct VFSData {
        const char* data;
        size_t size;
        size_t offset;
      };

      SF_VIRTUAL_IO createVirtualIO();
      std::unique_ptr<AudioData> processAudioFile(SNDFILE* sndFile, const SF_INFO& sfInfo, const std::string& filePath);

    public:
      explicit AudioLoader(IVFS& vfs);
      ~AudioLoader() = default;

      std::unique_ptr<AudioData> loadAudioFile(const std::string& filePath);
  };
}

#endif
