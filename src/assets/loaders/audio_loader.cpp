#include "audio_loader.hpp"

// C++ standard library
#include <algorithm>
#include <iostream>
#include <stdexcept>

// Third-party libraries
#include <sndfile.h>

// Local includes
#include "../../vfs/vfs.hpp"

namespace IronFrost {
  AudioLoader::AudioLoader(IVFS& vfs) : m_vfs(vfs) {}

  std::unique_ptr<AudioData> AudioLoader::loadAudioFile(const std::string& filePath) {
    if (!m_vfs.exists(filePath)) {
      throw std::runtime_error("Audio file not found: " + filePath);
    }

    std::string fileContent = m_vfs.readFile(filePath);
    VFSData vfsData = {fileContent.data(), fileContent.size(), 0};

    SF_VIRTUAL_IO vio = createVirtualIO();
    SF_INFO sfInfo;

    std::memset(&sfInfo, 0, sizeof(sfInfo));

    SNDFILE* sndFile = sf_open_virtual(&vio, SFM_READ, &sfInfo, &vfsData);
    if (!sndFile) {
      throw std::runtime_error("Failed to open audio file with libsndfile: " +
                               std::string(sf_strerror(nullptr)));
    }

    auto audioData = processAudioFile(sndFile, sfInfo, filePath);

    sf_close(sndFile);

    return audioData;
  }

  SF_VIRTUAL_IO AudioLoader::createVirtualIO() {
    SF_VIRTUAL_IO vio;

    vio.get_filelen = [](void* user_data) -> sf_count_t {
      VFSData* data = static_cast<VFSData*>(user_data);
      return static_cast<sf_count_t>(data->size);
    };

    vio.seek = [](sf_count_t offset, int whence, void* user_data) -> sf_count_t {
      VFSData* data = static_cast<VFSData*>(user_data);

      switch (whence) {
        case SEEK_SET:
          data->offset = static_cast<size_t>(offset);
          break;
        case SEEK_CUR:
          data->offset += static_cast<size_t>(offset);
          break;
        case SEEK_END:
          data->offset = data->size + static_cast<size_t>(offset);
          break;
        default:
          return -1;
      }

      if (data->offset > data->size) {
        data->offset = data->size;
      }

      return static_cast<sf_count_t>(data->offset);
    };

    vio.read = [](void* ptr, sf_count_t count, void* user_data) -> sf_count_t {
      VFSData* data = static_cast<VFSData*>(user_data);

      size_t available = data->size - data->offset;
      size_t to_read = std::min(static_cast<size_t>(count), available);

      if (to_read > 0) {
        std::memcpy(ptr, data->data + data->offset, to_read);
        data->offset += to_read;
      }

      return static_cast<sf_count_t>(to_read);
    };

    vio.write = nullptr;  // We don't need write functionality

    vio.tell = [](void* user_data) -> sf_count_t {
      VFSData* data = static_cast<VFSData*>(user_data);
      return static_cast<sf_count_t>(data->offset);
    };

    return vio;
  }

  std::unique_ptr<AudioData> AudioLoader::processAudioFile(SNDFILE* sndFile, const SF_INFO& sfInfo, const std::string& filePath) {
    auto audioData = std::make_unique<AudioData>();
    audioData->sampleRate = sfInfo.samplerate;
    audioData->channels = sfInfo.channels;
    audioData->duration = static_cast<float>(sfInfo.frames) / sfInfo.samplerate;

    size_t totalSamples = sfInfo.frames * sfInfo.channels;
    audioData->samples.resize(totalSamples);

    sf_count_t samplesRead = sf_read_float(sndFile, audioData->samples.data(), totalSamples);
    if (samplesRead != static_cast<sf_count_t>(totalSamples)) {
      throw std::runtime_error("Failed to read all audio samples from file: " + filePath);
    }

    std::cout << "Loaded audio: " << filePath
              << " (" << audioData->duration << "s, "
              << audioData->channels << " channels, "
              << audioData->sampleRate << " Hz, "
              << audioData->samples.size() << " samples)" << std::endl;

    return audioData;
  }
}
