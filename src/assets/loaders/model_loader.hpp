#ifndef __IF__MODEL_LOADER_HPP
#define __IF__MODEL_LOADER_HPP

// C++ standard library
#include <memory>
#include <string>
#include <utility>

// Third-party libraries
#include <assimp/mesh.h>
#include <assimp/scene.h>

// Local includes
#include "../asset_data_types.hpp"
#include "../asset_primitive_types.hpp"

namespace IronFrost {
  class IVFS;
  struct ModelData;
  struct MeshData;

  class ModelLoader {
    private:
      IVFS& m_vfs;
      
      Vertex toVertex(glm::vec3 pos, glm::vec2 uv, glm::vec3 normal, glm::vec3 tangent, glm::vec3 bitangent);

      std::unique_ptr<ImageData> processMaterialTexture(aiMaterial* mat, aiTextureType type);
      std::pair<std::unique_ptr<MeshData>, std::unique_ptr<ImageData>> processMesh(aiMesh* mesh, const aiScene* scene);
      ModelDataNode processNode(aiNode* node, const aiScene* scene);

      CollisionMath::AABB calculateModelBounds(const ModelDataNode& node, const glm::mat4& parentTransform);

    public:
      explicit ModelLoader(IVFS& vfs);

      std::unique_ptr<ModelData> loadModel(const std::string& path);
  };
}

#endif
