#ifndef __IF__FONT_LOADER_HPP
#define __IF__FONT_LOADER_HPP

// C++ standard library
#include <memory>
#include <string>

// Third-party libraries
#include <ft2build.h>
#include FT_FREETYPE_H

namespace IronFrost {
  class IVFS;
  struct GlyphData;
  struct FontData;

  class FontLoader {
  private:
    IVFS& m_vfs;

    // Helper methods for font loading
    GlyphData loadGlyph(const FT_GlyphSlot& glyph);
    bool initializeFreeType(FT_Library& ft);
    bool createFontFace(FT_Library ft, const std::string& path, FT_Face& fontFace);
    bool loadAllGlyphs(FT_Face fontFace, FontData& fontData, unsigned int fontSize);
    void cleanupFreeType(FT_Library ft, FT_Face fontFace);

  public:
    explicit FontLoader(IVFS& vfs);

    std::unique_ptr<FontData> loadFont(const std::string& path, unsigned int fontSize);
  };
}

#endif
