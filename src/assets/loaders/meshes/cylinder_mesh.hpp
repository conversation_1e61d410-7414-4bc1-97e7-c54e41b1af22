#ifndef __IF__CYLINDER_MESH_HPP
#define __IF__CYLINDER_MESH_HPP

// Third-party libraries
#include <glm/gtc/constants.hpp>

namespace IronFrost {
  class CylinderMesh {
    public:
      static std::unique_ptr<MeshData> createMeshData(int segments) {
        std::vector<Vertex> vertices;
        std::vector<unsigned int> indices;

        generateSide(vertices, indices, segments);
        generateCap(vertices, indices, segments, true);
        generateCap(vertices, indices, segments, false);

        auto data = std::make_unique<MeshData>(MeshData{std::move(vertices), std::move(indices)});

        data->calculateTangentsAndBitangents();
        data->calculateBounds();

        return data;
      }
    
    private:
      static void generateSide(std::vector<Vertex>& vertices, std::vector<unsigned int>& indices, int segments) {
        const float radius = 0.5f;
        const float halfHeight = 0.5f;

        int baseIndex = static_cast<int>(vertices.size());

        for (int i = 0; i <= segments; ++i) {
          float theta = (float)i / segments * glm::two_pi<float>();
          float x = std::cos(theta);
          float z = std::sin(theta);

          glm::vec3 normal = glm::normalize(glm::vec3(x, 0.0f, z));
          float u = (float)i / segments;

          // bottom vertex
          vertices.push_back(Vertex{
            radius * x, -halfHeight, radius * z,
            u, 0.0f,
            normal.x, normal.y, normal.z
          });

          // top vertex
          vertices.push_back(Vertex{
            radius * x, halfHeight, radius * z,
            u, 1.0f,
            normal.x, normal.y, normal.z
          });
        }

        for (int i = 0; i < segments; ++i) {
          int start = baseIndex + i * 2;
          indices.push_back(start);
          indices.push_back(start + 1);
          indices.push_back(start + 3);

          indices.push_back(start);
          indices.push_back(start + 3);
          indices.push_back(start + 2);
        }
      }

      static void generateCap(std::vector<Vertex>& vertices, std::vector<unsigned int>& indices, int segments, bool top) {
        const float radius = 0.5f;
        const float y = top ? 0.5f : -0.5f;
        glm::vec3 normal = top ? glm::vec3(0, 1, 0) : glm::vec3(0, -1, 0);

        int centerIndex = static_cast<int>(vertices.size());
        vertices.push_back(Vertex{
          0.0f, y, 0.0f,
          0.5f, 0.5f,
          normal.x, normal.y, normal.z
        });

        for (int i = 0; i <= segments; ++i) {
          float theta = (float)i / segments * glm::two_pi<float>();
          float x = std::cos(theta);
          float z = std::sin(theta);
          float u = 0.5f + 0.5f * x;
          float v = 0.5f + 0.5f * z;

          vertices.push_back(Vertex{
            radius * x, y, radius * z,
            u, v,
            normal.x, normal.y, normal.z
          });
        }

        int base = centerIndex + 1;
        for (int i = 0; i < segments; ++i) {
          if (top) {
            indices.push_back(centerIndex);
            indices.push_back(base + i + 1);
            indices.push_back(base + i);
          } else {
            indices.push_back(centerIndex);
            indices.push_back(base + i);
            indices.push_back(base + i + 1);
          }
        }
      }
  };
};

#endif
