#ifndef __IF__PLANE_MESH_HPP
#define __IF__PLANE_MESH_HPP

// C++ standard library
#include <iostream>
#include <memory>
#include <utility>
#include <vector>

// Local includes
#include "../../asset_data_types.hpp"

namespace IronFrost {
  class PlaneMesh {
    public:
      struct PlaneOptions {
        float width{100.0f};
        float depth{100.0f};
        unsigned int widthSegments{10};
        unsigned int depthSegments{10};
      };

      static std::unique_ptr<MeshData> createMeshData(const PlaneOptions& planeOptions) {
        std::vector<Vertex> vertices;
        std::vector<unsigned int> indices;

        generateVertices(vertices, planeOptions);
        generateIndices(indices, planeOptions);

        auto data = std::make_unique<MeshData>(MeshData{std::move(vertices), std::move(indices)});
        data->calculateTangentsAndBitangents();
        data->calculateBounds();

        return data;
      }

    private:
      static void generateVertices(std::vector<Vertex>& vertices, const PlaneOptions& planeOptions) {
        const float halfWidth = planeOptions.width * 0.5f;
        const float halfDepth = planeOptions.depth * 0.5f;
        const float dx = planeOptions.width / static_cast<float>(planeOptions.widthSegments);
        const float dz = planeOptions.depth / static_cast<float>(planeOptions.depthSegments);

        // Reserve space for better performance
        const size_t vertexCount = (planeOptions.widthSegments + 1) * (planeOptions.depthSegments + 1);
        vertices.reserve(vertexCount);

        for (unsigned int z = 0; z <= planeOptions.depthSegments; ++z) {
          for (unsigned int x = 0; x <= planeOptions.widthSegments; ++x) {
            const float u = static_cast<float>(x) / planeOptions.widthSegments;
            const float v = static_cast<float>(z) / planeOptions.depthSegments;
            const float xPos = x * dx - halfWidth;
            const float zPos = z * dz - halfDepth;

            vertices.push_back(Vertex{xPos, 0.0f, zPos, u, v, 0.0f, 1.0f, 0.0f});
          }
        }
      }

      static void generateIndices(std::vector<unsigned int>& indices, const PlaneOptions& planeOptions) {
        // Reserve space for better performance
        const size_t indexCount = planeOptions.widthSegments * planeOptions.depthSegments * 6;
        indices.reserve(indexCount);

        for (unsigned int z = 0; z < planeOptions.depthSegments; ++z) {
          for (unsigned int x = 0; x < planeOptions.widthSegments; ++x) {
            const unsigned int topLeft = z * (planeOptions.widthSegments + 1) + x;
            const unsigned int topRight = topLeft + 1;
            const unsigned int bottomLeft = topLeft + planeOptions.widthSegments + 1;
            const unsigned int bottomRight = bottomLeft + 1;

            // First triangle (counter-clockwise)
            indices.push_back(topLeft);
            indices.push_back(bottomLeft);
            indices.push_back(topRight);

            // Second triangle (counter-clockwise)
            indices.push_back(topRight);
            indices.push_back(bottomLeft);
            indices.push_back(bottomRight);
          }
        }
      }
  };
};

#endif
