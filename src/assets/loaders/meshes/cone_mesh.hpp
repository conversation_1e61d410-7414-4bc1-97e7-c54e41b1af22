#ifndef __IF__CONE_MESH_HPP
#define __IF__CONE_MESH_HPP

// Third-party libraries
#include <glm/gtc/constants.hpp>

namespace IronFrost {
  class ConeMesh {
    public:
      static std::unique_ptr<MeshData> createMeshData(int segments) {
        std::vector<Vertex> vertices;
        std::vector<unsigned int> indices;

        generateVertices(vertices, segments);
        generateIndices(indices, segments);

        auto mesh = std::make_unique<MeshData>(MeshData{std::move(vertices), std::move(indices)});

        mesh->calculateTangentsAndBitangents();
        mesh->calculateBounds();
        return mesh;
      }

    private:
      static void generateVertices(std::vector<Vertex>& vertices, int segments) {
        const float radius = 0.5f;
        const float height = 1.0f;
        const float halfHeight = height * 0.5f;

        // Tip vertex
        glm::vec3 tipPos(0.0f, +halfHeight, 0.0f);
        vertices.push_back(Vertex{tipPos.x, tipPos.y, tipPos.z, 0.5f, 1.0f, 0.0f, 1.0f, 0.0f});

        // Base center vertex
        glm::vec3 baseCenterPos(0.0f, -halfHeight, 0.0f);
        vertices.push_back(Vertex{baseCenterPos.x, baseCenterPos.y, baseCenterPos.z, 0.5f, 0.5f, 0.0f, -1.0f, 0.0f});

        // Generate circle vertices
        for (int i = 0; i <= segments; ++i) {
          float theta = (float)i / segments * glm::two_pi<float>();
          float x = glm::cos(theta);
          float z = glm::sin(theta);
          glm::vec3 pos = glm::vec3(radius * x, -halfHeight, radius * z);

          glm::vec2 uv = glm::vec2(
            0.5f + x * 0.5f, // wrap around base circle in UV space
            0.5f + z * 0.5f
          );

          // Approximate normal for sides
          glm::vec3 normal = glm::normalize(glm::vec3(x, radius / height, z));
          vertices.push_back(Vertex{pos.x, pos.y, pos.z, uv.x, uv.y, normal.x, normal.y, normal.z});
        }
      }

      static void generateIndices(std::vector<unsigned int>& indices, int segments) {
        int tipIndex = 0;
        int baseCenterIndex = 1;

        // === SIDE TRIANGLES ===
        for (int i = 0; i < segments; ++i) {
          int current = 2 + i;
          int next = 2 + (i + 1) % (segments + 1);

          indices.push_back(tipIndex);
          indices.push_back(next);
          indices.push_back(current);
        }

        // === BASE TRIANGLES ===
        for (int i = 0; i < segments; ++i) {
          int current = 2 + i;
          int next = 2 + (i + 1) % (segments + 1);

          indices.push_back(baseCenterIndex);
          indices.push_back(current);
          indices.push_back(next);
        }
      }
  };
};

#endif
