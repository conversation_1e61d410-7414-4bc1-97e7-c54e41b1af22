#ifndef __IF__TRIANGLE_MESH_HPP
#define __IF__TRIANGLE_MESH_HPP

// C++ standard library
#include <memory>

// Local includes
#include "../../asset_data_types.hpp"

namespace IronFrost {
  class TriangleMesh {
    public:
      static std::unique_ptr<MeshData> createMeshData() {
        auto data = std::make_unique<MeshData>(MeshData{
          {
            { 0.0f,  0.5f, 0.0f, 0.5f, 1.0f, 0.0f, 0.0f, -1.0f }, // Top vertex
            { 0.5f, -0.5f, 0.0f, 1.0f, 0.0f, 0.0f, 0.0f, -1.0f }, // Bottom right
            {-0.5f, -0.5f, 0.0f, 0.0f, 0.0f, 0.0f, 0.0f, -1.0f }  // Bottom left
          },
          {
            0, 1, 2  // Single triangle
          }
        });

        data->calculateTangentsAndBitangents();
        data->calculateBounds();

        return data;
      }
  };
};

#endif
