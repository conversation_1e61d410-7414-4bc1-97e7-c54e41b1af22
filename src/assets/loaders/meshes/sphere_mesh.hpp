#ifndef __IF__SPHERE_MESH_HPP
#define __IF__SPHERE_MESH_HPP

// C++ standard library
#include <memory>
#include <utility>
#include <vector>

// Third-party libraries
#include <glm/gtc/constants.hpp>

// Local includes
#include "../../asset_data_types.hpp"

namespace IronFrost {
  class SphereMesh {
    public:
      static std::unique_ptr<MeshData> createMeshData(int latitudeBands, int longitudeBands) {
        std::vector<Vertex> vertices;
        std::vector<unsigned int> indices;

        generateVertices(vertices, latitudeBands, longitudeBands);
        generateIndices(indices, latitudeBands, longitudeBands);

        auto data = std::make_unique<MeshData>(MeshData{std::move(vertices), std::move(indices)});

        data->calculateTangentsAndBitangents();
        data->calculateBounds();

        return data;
      }

    private:
      static void generateVertices(std::vector<Vertex>& vertices, int latitudeBands, int longitudeBands) {
        vertices.clear();
        vertices.reserve((latitudeBands + 1) * (longitudeBands + 1));

        for (int lat = 0; lat <= latitudeBands; ++lat) {
          const float theta = lat * glm::pi<float>() / latitudeBands;
          const float sinTheta = glm::sin(theta);
          const float cosTheta = glm::cos(theta);

          for (int lon = 0; lon <= longitudeBands; ++lon) {
            const float phi = lon * 2.0f * glm::pi<float>() / longitudeBands;
            const float sinPhi = glm::sin(phi);
            const float cosPhi = glm::cos(phi);

            // Compute unit position
            const float x = cosPhi * sinTheta;
            const float y = cosTheta;
            const float z = sinPhi * sinTheta;

            // Normalized sphere direction
            glm::vec3 normal = glm::normalize(glm::vec3(x, y, z));
            glm::vec2 uv(1.0f - (float(lon) / longitudeBands), 1.0f - (float(lat) / latitudeBands));

            // For now use zero tangents; they'll be calculated later
            vertices.emplace_back(Vertex{
              0.5f * x, 0.5f * y, 0.5f * z, // Position (radius = 0.5)
              uv.x, uv.y,                  // TexCoords
              normal.x, normal.y, normal.z,
              0.0f, 0.0f, 0.0f,            // Tangent placeholder
              0.0f, 0.0f, 0.0f             // Bitangent placeholder
            });
          }
        }
      }

      static void generateIndices(std::vector<unsigned int>& indices, int latitudeBands, int longitudeBands) {
        indices.clear();
        indices.reserve(latitudeBands * longitudeBands * 6);

        for (int lat = 0; lat < latitudeBands; ++lat) {
          for (int lon = 0; lon < longitudeBands; ++lon) {
            int first = lat * (longitudeBands + 1) + lon;
            int second = first + longitudeBands + 1;

            // First triangle
            indices.push_back(first);
            indices.push_back(first + 1);
            indices.push_back(second);

            // Second triangle
            indices.push_back(first + 1);
            indices.push_back(second + 1);
            indices.push_back(second);
          }
        }
      }
  };
};

#endif
