#ifndef __IF__QUAD_MESH_HPP
#define __IF__QUAD_MESH_HPP

// C++ standard library
#include <memory>

// Local includes
#include "../../asset_data_types.hpp"

namespace IronFrost {
  class QuadMesh {
    public:
      static std::unique_ptr<MeshData> createMeshData() {
        auto data = std::make_unique<MeshData>(MeshData{
          {
            { 0.5f,  0.5f, 0.0f, 1.0f, 1.0f, 0.0f, 0.0f, -1.0f }, 
            { 0.5f, -0.5f, 0.0f, 1.0f, 0.0f, 0.0f, 0.0f, -1.0f }, 
            {-0.5f, -0.5f, 0.0f, 0.0f, 0.0f, 0.0f, 0.0f, -1.0f }, 
            {-0.5f,  0.5f, 0.0f, 0.0f, 1.0f, 0.0f, 0.0f, -1.0f }
          },
          {
            0, 1, 3, 1, 2, 3
          }
        });

        data->calculateTangentsAndBitangents();
        data->calculateBounds();

        return data;
      }
  };
};

#endif
