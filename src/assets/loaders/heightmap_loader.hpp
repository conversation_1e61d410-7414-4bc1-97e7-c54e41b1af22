#ifndef __IF__HEIGHTMAP_LOADER_HPP
#define __IF__HEIGHTMAP_LOADER_HPP

// C++ standard library
#include <memory>
#include <string>
#include <limits>
#include <vector>

namespace IronFrost {
  class IVFS;
  struct HeightmapData;
  struct ImageData;

  class HeightmapLoader {
    private:
      IVFS& m_vfs;

      std::unique_ptr<ImageData> loadImage(const std::string& path);

      template <typename T>
      float normalize(T value) {
          return static_cast<float>(value) / static_cast<float>(std::numeric_limits<T>::max());
      }

      template <typename T>
      void processHeightmapData(const T* srcData, std::vector<float>& dstHeights) {
        size_t size = dstHeights.size();
        for (size_t i = 0; i < size; ++i) {
          dstHeights[i] = normalize(srcData[i]);
        }
      }
    public:
      explicit HeightmapLoader(IVFS& vfs);

      std::unique_ptr<HeightmapData> loadHeightmap(const std::string& path);
  };
}

#endif
