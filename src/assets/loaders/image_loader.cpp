#include "image_loader.hpp"

// C++ standard library
#include <iostream>
#include <memory>
#include <string>
#include <vector>

// Third-party libraries
#include <stb_image.h>

// Local includes
#include "../../vfs/vfs.hpp"
#include "../asset_data_types.hpp"

namespace IronFrost {
  ImageData::ImageData() : data{nullptr, stbi_image_free} {}

  ImageLoader::ImageLoader(IVFS& vfs) : m_vfs{vfs}
  {}

  std::vector<unsigned char> ImageLoader::readImageFile(const std::string& path) const {
    std::vector<unsigned char> data = m_vfs.readFileBinary(path);

    if (data.empty()) {
      std::cerr << "Failed to read image file: " << path << '\n';
      return {};
    }

    int dataSize = static_cast<int>(data.size());
    std::cout << "Loading image: " << path << " (size: " << dataSize << " bytes)" << '\n';

    return data;
  }

  bool ImageLoader::validateImageFormat(const unsigned char* data, int dataSize, const std::string& path) const {
    int x;
    int y;
    int comp;

    if (stbi_info_from_memory(data, dataSize, &x, &y, &comp) == 0) {
      std::cerr << "STB cannot identify image format for: " << path << " - " << stbi_failure_reason() << '\n';
      return false;
    }

    std::cout << "Image info: " << x << "x" << y << " channels=" << comp << '\n';
    return true;
  }

  bool ImageLoader::loadImageData(ImageData* imageData, const unsigned char* data, int dataSize, const std::string& path, int forceChannels) const {
    imageData->is16bit = (stbi_is_16_bit_from_memory(data, dataSize) != 0);

    if (imageData->is16bit) {
      imageData->data.reset(stbi_load_16_from_memory(
          // NOLINTNEXTLINE(cppcoreguidelines-pro-type-reinterpret-cast) - Required for STB image library interface
          reinterpret_cast<const stbi_uc *>(data), dataSize, &imageData->width, &imageData->height, &imageData->channels, forceChannels));
    } else {
      imageData->data.reset(stbi_load_from_memory(
          // NOLINTNEXTLINE(cppcoreguidelines-pro-type-reinterpret-cast) - Required for STB image library interface
          reinterpret_cast<const stbi_uc *>(data), dataSize, &imageData->width, &imageData->height, &imageData->channels, forceChannels));
    }

    if (!imageData->data) {
      std::cerr << "Failed to load image: " << path << " - " << stbi_failure_reason() << '\n';
      return false;
    }

    if (forceChannels > 0) {
      imageData->channels = forceChannels;
    }

    return true;
  }

  std::unique_ptr<ImageData> ImageLoader::loadImage(const std::string& path, int forceChannels) const {
    auto imageData = std::make_unique<ImageData>();
    std::vector<unsigned char> data = readImageFile(path);

    if (data.empty()) {
      return nullptr;
    }

    int dataSize = static_cast<int>(data.size());

    if (!validateImageFormat(data.data(), dataSize, path)) {
      std::cerr << "Invalid image format for: " << path << '\n';
      return nullptr;
    }

    if (!loadImageData(imageData.get(), data.data(), dataSize, path, forceChannels)) {
      std::cerr << "Failed to load image data for: " << path << '\n';
      return nullptr;
    }

    return imageData;
  }
}
