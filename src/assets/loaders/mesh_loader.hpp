#ifndef __IF__MESH_LOADER_HPP
#define __IF__MESH_LOADER_HPP

// C++ standard library
#include <exception>
#include <memory>

// Local includes
#include "../asset_primitive_types.hpp"
#include "meshes/cube_mesh.hpp"
#include "meshes/cone_mesh.hpp"
#include "meshes/cylinder_mesh.hpp"
#include "meshes/plane_mesh.hpp"
#include "meshes/quad_mesh.hpp"
#include "meshes/sphere_mesh.hpp"
#include "meshes/triangle_mesh.hpp"

namespace IronFrost {
  class IVFS;
  struct MeshData;

  class MeshLoader {
    private:
      IVFS& m_vfs;

    public:
      explicit MeshLoader(IVFS& vfs);

      std::unique_ptr<MeshData> createPrimitive(PrimitiveType type, const PrimitiveParams& params = {}) {
        switch (type) {
          case PrimitiveType::TRIANGLE:
            return TriangleMesh::createMeshData();
          case PrimitiveType::QUAD:
            return QuadMesh::createMeshData();
          case PrimitiveType::CUBE:
            return CubeMesh::createMeshData();
          case PrimitiveType::CONE:
            return ConeMesh::createMeshData(
              params.get<int>("segments").value_or(32)
            );
          case PrimitiveType::CYLINDER:
            return CylinderMesh::createMeshData(
              params.get<int>("segments").value_or(32)
            );
          case PrimitiveType::SPHERE:
            return SphereMesh::createMeshData(
              params.get<int>("lat-bands").value_or(32), 
              params.get<int>("long-bands").value_or(32)
            );
          case PrimitiveType::PLANE:
            return PlaneMesh::createMeshData({
              params.get<float>("width").value_or(10.0f), 
              params.get<float>("depth").value_or(10.0f), 
              params.get<unsigned int>("width-segments").value_or(10), 
              params.get<unsigned int>("depth-segments").value_or(10)
            });
          default:
            throw std::runtime_error("Unknown primitive type");
        }
      }
  };
}

#endif
