#include "model_loader.hpp"

// C++ standard library
#include <iostream>
#include <memory>
#include <string>
#include <utility>
#include <vector>

// Third-party libraries
#define GLM_ENABLE_EXPERIMENTAL
#include <assimp/Importer.hpp>
#include <assimp/postprocess.h>
#include <glm/glm.hpp>
#include <glm/gtx/string_cast.hpp>

// Local includes
#include "../../utils/assimp_glm_utils.hpp"
#include "../../vfs/assimp/io_system.hpp"
#include "../../vfs/vfs.hpp"
#include "../asset_data_types.hpp"
#include "image_loader.hpp"

namespace IronFrost {
  Vertex ModelLoader::toVertex(glm::vec3 pos, glm::vec2 uv, glm::vec3 normal, glm::vec3 tangent, glm::vec3 bitangent) {
    return { 
      pos.x, pos.y, pos.z, 
      uv.x, uv.y,
      normal.x, normal.y, normal.z,
      tangent.x, tangent.y, tangent.z,
      bitangent.x, bitangent.y, bitangent.z
    };
  }

  std::unique_ptr<ImageData> ModelLoader::processMaterialTexture(aiMaterial* mat, aiTextureType type) { 
    if (mat->GetTextureCount(type) > 0) {
      aiString path;
      mat->GetTexture(type, 0, &path);

      #ifndef NDEBUG
        std::cout << "  Texture path: " << path.C_Str() << '\n';
      #endif

      ImageLoader imageLoader(m_vfs);
      return imageLoader.loadImage(std::string(path.C_Str()));
    }

    return nullptr;
  }

  std::pair<std::unique_ptr<MeshData>, std::unique_ptr<ImageData>> ModelLoader::processMesh(aiMesh* mesh, const aiScene* scene) {
    std::unique_ptr<MeshData> meshData = std::make_unique<MeshData>();
    std::unique_ptr<ImageData> diffuseTexture = nullptr;

    #ifndef NDEBUG
      std::cout << "Processing mesh: " << mesh->mName.C_Str() << '\n';
      std::cout << "  Vertices: " << mesh->mNumVertices << '\n';
      std::cout << "  Faces: " << mesh->mNumFaces << '\n';

      if (mesh->HasTextureCoords(0)) {
        std::cout << "  Mesh has texture coordinates" << '\n';
      } else {
        std::cout << "  Mesh lacks texture coordinates!" << '\n';
      }
    #endif

    for (unsigned int i = 0; i < mesh->mNumVertices; i++) {
      Vertex vertex;

      glm::vec3 position = convertAssimpVector(mesh->mVertices[i]);
      glm::vec3 normal = convertAssimpVector(mesh->mNormals[i]);
      glm::vec2 texCoords = glm::vec2(0.0F, 0.0F);

      if (mesh->mTextureCoords[0] != nullptr) {
        texCoords = glm::vec2(mesh->mTextureCoords[0][i].x, mesh->mTextureCoords[0][i].y);
      }

      glm::vec3 tangent = convertAssimpVector(mesh->mTangents[i]);
      glm::vec3 bitangent = convertAssimpVector(mesh->mBitangents[i]);

      meshData->vertices.push_back(toVertex(position, texCoords, normal, tangent, bitangent));
    }

    if (mesh->mMaterialIndex >= 0) {
      aiMaterial* material = scene->mMaterials[mesh->mMaterialIndex];
      diffuseTexture = processMaterialTexture(material, aiTextureType_DIFFUSE);
    }

    for (unsigned int i = 0; i < mesh->mNumFaces; i++) {
      aiFace face = mesh->mFaces[i];
      for (unsigned int j = 0; j < face.mNumIndices; j++) {
        meshData->indices.push_back(face.mIndices[j]);
      }
    }

    meshData->calculateBounds();

    return {std::move(meshData), std::move(diffuseTexture)};
  }

  ModelDataNode ModelLoader::processNode(aiNode* node, const aiScene* modelScene) {
    ModelDataNode modelNode;
    modelNode.transform = convertAssimpMatrix(node->mTransformation);

    if (node->mNumMeshes > 0) {
      for (unsigned int i = 0; i < node->mNumMeshes; i++) {
        auto [mesh, texture] = processMesh(modelScene->mMeshes[node->mMeshes[i]], modelScene);

        modelNode.meshes.emplace_back(std::move(mesh));
        modelNode.textures.emplace_back(std::move(texture));
      }
    }

    for (unsigned int i = 0; i < node->mNumChildren; i++) {
      modelNode.children.emplace_back(processNode(node->mChildren[i], modelScene));
    }

    return modelNode;
  }

  CollisionMath::AABB ModelLoader::calculateModelBounds(const ModelDataNode& node, const glm::mat4& parentTransform) {
    glm::mat4 globalTransform = parentTransform * node.transform;

    glm::vec3 min(FLT_MAX);
    glm::vec3 max(-FLT_MAX);

    for (const auto& mesh : node.meshes) {
      CollisionMath::AABB worldBounds = transformAABB(mesh->bounds, globalTransform);
      min = glm::min(min, worldBounds.min);
      max = glm::max(max, worldBounds.max);
    }

    for (const auto& child : node.children) {
      CollisionMath::AABB childBounds = calculateModelBounds(child, globalTransform);
      min = glm::min(min, childBounds.min);
      max = glm::max(max, childBounds.max);
    }

    return CollisionMath::AABB{min, max};
  }

  ModelLoader::ModelLoader(IVFS& vfs) : m_vfs{vfs} {}

  std::unique_ptr<ModelData> ModelLoader::loadModel(const std::string& path) {
    Assimp::Importer importer;

    // NOLINTNEXTLINE(cppcoreguidelines-owning-memory) - Assimp takes ownership of IOHandler
    importer.SetIOHandler(new VFS_IOSystem(m_vfs));

    // const std::vector<unsigned char>& data = m_vfs.readFileBinary(_path);
    // const aiScene* modelScene = importer.ReadFileFromMemory(data.data(), data.size(), 
    //   aiProcess_Triangulate | aiProcess_GenNormals | aiProcess_JoinIdenticalVertices);

    const aiScene* modelScene = importer.ReadFile(path, 
      aiProcess_Triangulate | 
      aiProcess_GenNormals | 
      aiProcess_JoinIdenticalVertices | 
      aiProcess_FlipUVs | 
      aiProcess_FixInfacingNormals |
      aiProcess_CalcTangentSpace
    );

    if(modelScene == nullptr || (modelScene->mFlags & AI_SCENE_FLAGS_INCOMPLETE) != 0 || modelScene->mRootNode == nullptr) {
      return nullptr;
    }

    std::unique_ptr<ModelData> modelData = std::make_unique<ModelData>();

    modelData->rootNode = processNode(modelScene->mRootNode, modelScene);
    modelData->bounds = calculateModelBounds(modelData->rootNode, glm::mat4(1.0f));

    return modelData;
  }
}
