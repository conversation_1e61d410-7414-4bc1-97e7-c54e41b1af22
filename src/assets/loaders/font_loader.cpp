#include "font_loader.hpp"

// C standard library
#include <cstring>

// C++ standard library
#include <exception>
#include <iostream>
#include <memory>
#include <string>
#include <vector>

// Local includes
#include "../../vfs/vfs.hpp"
#include "../asset_data_types.hpp"

namespace IronFrost  {
  GlyphData FontLoader::loadGlyph(const FT_GlyphSlot& glyph) {
    GlyphData glyphData;

    glyphData.size = { glyph->bitmap.width, glyph->bitmap.rows };
    glyphData.bearing = { glyph->bitmap_left, glyph->bitmap_top };
    glyphData.advance = glyph->advance.x >> 6;

    glyphData.bitmap.resize(static_cast<std::size_t>(glyph->bitmap.width) * glyph->bitmap.rows);
    std::memcpy(glyphData.bitmap.data(), glyph->bitmap.buffer, glyphData.bitmap.size());

    return glyphData;
  }

  bool FontLoader::initializeFreeType(FT_Library& ft) {
    if (FT_Init_FreeType(&ft) != 0) {
      std::cerr << "Failed to initialize FreeType library" << '\n';
      return false;
    }
    return true;
  }

  bool FontLoader::createFontFace(FT_Library ft, const std::string& path, FT_Face& fontFace) {
    std::vector<unsigned char> font = m_vfs.readFileBinary(path);

    if (font.empty()) {
      std::cerr << "Failed to read font file: " << path << '\n';
      return false;
    }

    if (FT_New_Memory_Face(ft, font.data(), static_cast<FT_Long>(font.size()), 0, &fontFace) != 0) {
      std::cerr << "Failed to load font: " << path << '\n';
      return false;
    }

    return true;
  }

  bool FontLoader::loadAllGlyphs(FT_Face fontFace, FontData& fontData, unsigned int fontSize) {
    fontData.glyphs.resize(128);

    if (FT_Set_Pixel_Sizes(fontFace, 0, fontSize) != 0) {
      std::cerr << "Failed to set font size: " << fontSize << '\n';
      return false;
    }

    for (unsigned char c = 32; c < 128; ++c) {
      if (FT_Load_Char(fontFace, c, FT_LOAD_RENDER | FT_LOAD_FORCE_AUTOHINT) != 0) {
        std::cerr << "Failed to load glyph for character: " << c << '\n';
        continue;
      }

      fontData.glyphs[c] = loadGlyph(fontFace->glyph);
    }

    fontData.lineHeight = (fontFace->size->metrics.height >> 6);
    return true;
  }

  void FontLoader::cleanupFreeType(FT_Library ft, FT_Face fontFace) {
    FT_Done_Face(fontFace);
    FT_Done_FreeType(ft);
  }

  FontLoader::FontLoader(IVFS& vfs) : m_vfs(vfs)
  {}

  std::unique_ptr<FontData> FontLoader::loadFont(const std::string& path, unsigned int fontSize) {
    if (fontSize == 0) {
      std::cerr << "Invalid font size: " << fontSize << '\n';
      return nullptr;
    }

    auto fontData = std::make_unique<FontData>();
    fontData->size = fontSize;

    FT_Library ft;
    if (!initializeFreeType(ft)) {
      return nullptr;
    }

    FT_Face fontFace;
    if (!createFontFace(ft, path, fontFace)) {
      FT_Done_FreeType(ft);
      return nullptr;
    }

    if (!loadAllGlyphs(fontFace, *fontData, fontSize)) {
      cleanupFreeType(ft, fontFace);
      return nullptr;
    }

    cleanupFreeType(ft, fontFace);
    return fontData;
  }
}
