#ifndef __IF__ASSET_DATA_TYPES_HPP
#define __IF__ASSET_DATA_TYPES_HPP

#include "data_types/vertex.hpp"
#include "data_types/shader_data.hpp"
#include "data_types/mesh_data.hpp"
#include "data_types/image_data.hpp"
#include "data_types/image_atlas_data.hpp"
#include "data_types/model_data.hpp"
#include "data_types/font_data.hpp"
#include "data_types/audio_data.hpp"
#include "data_types/material_data.hpp"
#include "data_types/heightmap_data.hpp"
#include "data_types/terrain_data.hpp"
#include "data_types/postprocess_data.hpp"

#endif
