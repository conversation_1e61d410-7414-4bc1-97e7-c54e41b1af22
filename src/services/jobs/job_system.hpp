
#ifndef __IF__JOB_SYSTEM_HPP
#define __IF__JOB_SYSTEM_HPP

// C++ standard library
#include <vector>
#include <thread>
#include <mutex>
#include <condition_variable>
#include <queue>
#include <atomic>
#include <functional>

// Local includes
#include "../service.hpp"

namespace IronFrost {
  class JobSystem : public IService {
    private:
      std::vector<std::thread> m_workers;

      std::mutex m_mutex;
      std::condition_variable m_condition;

      std::queue<std::function<void()>> m_queue;
      
      std::atomic<bool> m_running{true};
      std::atomic<size_t> m_pendingJobs{0};

      size_t defaultWorkerCount() {
        auto count = std::thread::hardware_concurrency();
        return count > 1 ? (count - 1) : 1;
      }

      void workerThread() {
        while(true) {
          std::function<void()> job;
          {
            std::unique_lock<std::mutex> lock(m_mutex);
            m_condition.wait(lock, [this]() { return !m_running.load(std::memory_order_relaxed) || !m_queue.empty(); });

            if (!m_running.load(std::memory_order_relaxed)) {
              break;
            }

            job = std::move(m_queue.front());
            m_queue.pop();

            m_pendingJobs.fetch_add(1, std::memory_order_acq_rel);
          }

          try {
            job();
          } catch (const std::exception& e) {
            std::cerr << "Exception in job system worker: " << e.what() << std::endl;
          }

          m_pendingJobs.fetch_sub(1, std::memory_order_acq_rel);
          m_condition.notify_all();
        }
      }

    public:
      JobSystem() {
        size_t workerCount = defaultWorkerCount();

        m_workers.reserve(workerCount);
        for (size_t i = 0; i < workerCount; ++i) {
          m_workers.emplace_back([this]() { this->workerThread(); });
        }
      };

      ~JobSystem() {
        m_running.store(false, std::memory_order_relaxed);
        m_condition.notify_all();

        for (auto& worker : m_workers) {
          if (worker.joinable()) worker.join();
        }
      };

      // Non-copyable
      JobSystem(const JobSystem&) = delete;
      JobSystem& operator=(const JobSystem&) = delete;

      // Non-movable
      JobSystem(JobSystem&&) = delete;
      JobSystem& operator=(JobSystem&&) = delete;

      size_t pendingJobs() const {
        return m_pendingJobs.load(std::memory_order_relaxed);
      }

      size_t workerCount() const {
        return m_workers.size();
      }

      void scheduleJob(std::function<void()> job) {
        if (!m_running.load(std::memory_order_relaxed)) {
          return;
        }

        {
          std::scoped_lock<std::mutex> lock(m_mutex);
          m_queue.push(std::move(job));
        }

        m_condition.notify_one();
      }

      void waitForCompletion() {
        std::unique_lock<std::mutex> lock(m_mutex);
        m_condition.wait(lock, [this]() { return m_queue.empty() && m_pendingJobs.load(std::memory_order_acquire) == 0; });
      }
  };
}

#endif
