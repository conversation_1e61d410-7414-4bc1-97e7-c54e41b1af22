#ifndef __IF__SERVICE_LOCATOR_HPP
#define __IF__SERVICE_LOCATOR_HPP

// C++ standard library
#include <memory>
#include <typeindex>
#include <unordered_map>
#include <utility>

// Local includes
#include "service.hpp"

namespace IronFrost {
  class ServiceLocator {
    private:
      static std::unordered_map<std::type_index, std::unique_ptr<IService>>& services() {
        static std::unordered_map<std::type_index, std::unique_ptr<IService>> services;
        return services;
      }

    public:
      template<typename T>
      static void registerService(std::unique_ptr<T> service) {
        services()[std::type_index(typeid(T))] = std::move(service);
      }

      template<typename T>
      static T& getService() {
        return dynamic_cast<T&>(*services()[std::type_index(typeid(T))]);
      }
  };
}

#endif
