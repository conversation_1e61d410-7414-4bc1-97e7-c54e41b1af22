#ifndef __IF__CAMERA_HPP
#define __IF__CAMERA_HPP

// Third-party libraries
#include <glm/glm.hpp>

// Local includes
#include "frustum.hpp"

namespace IronFrost {
  class Camera {
    private:
      glm::vec3 m_position{0.0f, 0.0f, 1.0f};
      glm::vec3 m_direction{0.0f, 0.0f, -1.0f};
      glm::vec3 m_up{0.0f, 1.0f, 0.0f};
      glm::vec3 m_right{1.0f, 0.0f, 0.0f};

      glm::mat4 m_viewMatrix;
      glm::mat4 m_projectionMatrix;

      float m_fov{45.0f};
      float m_aspectRatio{4.0f / 3.0f};
      float m_nearPlane{0.1f};
      float m_farPlane{100.0f};

      float m_collisionRadius{0.5f}; // Collision sphere radius

      void updateViewMatrix();
      void updateProjectionMatrix();

      void updateVectors();

    public:
      Camera()
      {
        updateVectors();
        updateViewMatrix();
        updateProjectionMatrix();
      };

      Camera(const glm::vec3 &position, const glm::vec3 &direction, const glm::vec3 &up, float fov, float aspectRatio, float near, float far)
          : m_position(position), m_direction(direction), m_up(up), m_fov(fov), m_aspectRatio(aspectRatio), m_nearPlane(near), m_farPlane(far)
      {
        updateVectors();
        updateViewMatrix();
        updateProjectionMatrix();
      }

      void setPosition(const glm::vec3& position);
      const glm::vec3& getPosition() const;
      void setDirection(const glm::vec3& direction);
      void setUp(const glm::vec3& up);

      void setFov(float fov);
      void setAspectRatio(float aspectRatio);
      void setNearPlane(float near);
      void setFarPlane(float far);

      float getCollisionRadius() const;
      void setCollisionRadius(float radius);

      void moveForward(float speed, float deltaTime);
      void moveBackward(float speed, float deltaTime);
      void moveLeft(float speed, float deltaTime);
      void moveRight(float speed, float deltaTime);

      glm::vec3 calculateForwardMovement(float speed, float deltaTime) const;
      glm::vec3 calculateBackwardMovement(float speed, float deltaTime) const;
      glm::vec3 calculateLeftMovement(float speed, float deltaTime) const;
      glm::vec3 calculateRightMovement(float speed, float deltaTime) const;

      void setPositionIfValid(const glm::vec3& newPosition);

      void turnLeft(float speed, float deltaTime);
      void turnRight(float speed, float deltaTime);
      void turnUp(float speed, float deltaTime);
      void turnDown(float speed, float deltaTime);

      void mouseLookYaw(float angle);    // Horizontal rotation around world up
      void mouseLookPitch(float angle);  // Vertical rotation around camera right

      const glm::mat4& getViewMatrix() const;
      const glm::mat4& getProjectionMatrix() const;

      Frustum getFrustum() const {
        return Frustum(m_projectionMatrix * m_viewMatrix);
      }
  };
}

#endif
