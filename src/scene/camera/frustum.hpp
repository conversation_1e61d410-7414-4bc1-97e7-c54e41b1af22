#ifndef __IF__FRUSTUM_HPP
#define __IF__FRUSTUM_HPP

// Third-party libraries
#include <glm/glm.hpp>

// Local includes
#include "../../utils/collision_math.hpp"

namespace IronFrost {
  class Frustum {
    private:
      CollisionMath::Plane m_planes[6];

      enum PlaneIndex {
        LEFT = 0,
        RIGHT,
        TOP,
        BOTTOM,
        NEAR,
        FAR
      };

      CollisionMath::Plane extractPlane(const glm::vec4& p) const {
        glm::vec3 normal = glm::vec3(p.x, p.y, p.z);
        float length = glm::length(normal);
        if (length > 0.0f) {
          return CollisionMath::Plane(glm::normalize(normal), -p.w / length);
        }
        return CollisionMath::Plane(glm::vec3(0.0f), 0.0f);
      }

    public:
      explicit Frustum(const glm::mat4& viewProj) {
        glm::vec4 row0 = glm::vec4(viewProj[0][0], viewProj[1][0], viewProj[2][0], viewProj[3][0]);
        glm::vec4 row1 = glm::vec4(viewProj[0][1], viewProj[1][1], viewProj[2][1], viewProj[3][1]);
        glm::vec4 row2 = glm::vec4(viewProj[0][2], viewProj[1][2], viewProj[2][2], viewProj[3][2]);
        glm::vec4 row3 = glm::vec4(viewProj[0][3], viewProj[1][3], viewProj[2][3], viewProj[3][3]);

        m_planes[LEFT]   = extractPlane(row3 + row0);
        m_planes[RIGHT]  = extractPlane(row3 - row0);
        m_planes[BOTTOM] = extractPlane(row3 + row1);
        m_planes[TOP]    = extractPlane(row3 - row1);
        m_planes[NEAR]   = extractPlane(row3 + row2);
        m_planes[FAR]    = extractPlane(row3 - row2);
      }

      ~Frustum() = default;

      bool intersects(const CollisionMath::AABB& aabb) const {
        for (int i = 0; i < 6; i++) {
          auto& plane = m_planes[i];

          glm::vec3 p = {
            plane.normal.x >= 0.0f ? aabb.max.x : aabb.min.x,
            plane.normal.y >= 0.0f ? aabb.max.y : aabb.min.y,
            plane.normal.z >= 0.0f ? aabb.max.z : aabb.min.z
          };

          if (CollisionMath::distanceToPlane(p, plane) < 0.0f) {
            return false;
          }
        }

        return true;
      }
  };
}

#endif
