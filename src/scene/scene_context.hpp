#ifndef __IF__SCENE_CONTEXT_HPP
#define __IF__SCENE_CONTEXT_HPP

namespace IronFrost {
  class AssetsManager;
  class EventDispatcher;
  class IRenderer;
  class IScriptEngine;
  class IVFS;
  class SceneDataStorage;

  struct SceneContext {
    AssetsManager& assetsManager;
    SceneDataStorage& sceneDataStorage;

    IVFS& vfs;
    IRenderer& renderer;
    IScriptEngine* scriptEngine;  // Optional - can be nullptr if scripts disabled

    SceneContext(
      AssetsManager& assetsManager, SceneDataStorage& sceneDataStorage, IVFS& vfs, IRenderer& renderer, IScriptEngine* scriptEngine = nullptr)
      : assetsManager(assetsManager), sceneDataStorage(sceneDataStorage), vfs(vfs), renderer(renderer), scriptEngine(scriptEngine) {}
  };
}

#endif
