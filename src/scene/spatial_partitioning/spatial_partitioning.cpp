#include "spatial_partitioning.hpp"

// Local includes
#include "octree/octree.hpp"

namespace IronFrost {
  std::unique_ptr<ISpatialPartitioning> ISpatialPartitioning::create(SPATIAL_PARTITIONING_TYPE spatialPartitioningType) {
    switch (spatialPartitioningType) {
      case SPATIAL_PARTITIONING_TYPE::OCTREE:
        return std::make_unique<Octree>(CollisionMath::AABB::fromCenterAndSize(glm::vec3(0.0f), glm::vec3(512.0f)));
      default:
        return nullptr;
    }
  }
}
