#ifndef __IF__SCENE_LIGHTING_HPP
#define __IF__SCENE_LIGHTING_HPP

// C++ standard library
#include <vector>

namespace IronFrost {
  struct DirectionalLight {
    glm::vec3 direction;
    glm::vec3 color;
    float intensity;
  };

  constexpr int MAX_DIRECTIONAL_LIGHTS = 4;

  class SceneLighting {
    private:
      glm::vec3 m_ambientLightColor{0.6f, 0.5f, 0.4f};
      float m_ambientLightIntensity{0.6f};

      std::vector<DirectionalLight> m_directionalLights;

    public:
      SceneLighting() = default;

      void setAmbientLightColor(const glm::vec3& color) {
        m_ambientLightColor = color;
      }

      void setAmbientLightIntensity(float intensity) {
        m_ambientLightIntensity = intensity;
      }

      const glm::vec3& getAmbientLightColor() const {
        return m_ambientLightColor;
      }

      float getAmbientLightIntensity() const {
        return m_ambientLightIntensity;
      }

      void addDirectionalLight(const DirectionalLight& light) {
        m_directionalLights.push_back(light);
      }

      void addDirectionalLight(const glm::vec3& direction, const glm::vec3& color, float intensity) {
        m_directionalLights.push_back({ glm::normalize(direction), color, intensity });
      }

      const std::vector<DirectionalLight>& getDirectionalLights() const {
        return m_directionalLights;
      }

      void clearDirectionalLights() {
        m_directionalLights.clear();
      }
  };
}

#endif
