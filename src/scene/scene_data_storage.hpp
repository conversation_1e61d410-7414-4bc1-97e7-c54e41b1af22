#ifndef __IF__SCENE_DATA_STORAGE_HPP
#define __IF__SCENE_DATA_STORAGE_HPP

// C++ standard library
#include <memory>
#include <iostream>

// Local includes
#include "../utils/containers_set.hpp"
#include "../utils/collision_math.hpp"
#include "../services/service_locator.hpp"
#include "../events/event_dispatcher.hpp"
#include "../assets/asset_events.hpp"

namespace IronFrost {
  class SceneDataStorage {
    private:
      ValueContainersSet<
        CollisionMath::AABB,
        std::shared_ptr<const HeightmapData>
      > m_sceneDataStorage;

    public:
      SceneDataStorage() {
        EventDispatcher& eventDispatcher = ServiceLocator::getService<EventDispatcher>();

        eventDispatcher.registerListener<LoadMeshEvent>([&](const LoadMeshEvent& event) {
          std::cout << "Loading scene mesh AABB: " << StringID::getString(event.name()) << std::endl;
          m_sceneDataStorage.getContainer<CollisionMath::AABB>().insert(event.name(), event.meshData().bounds);
        });

        eventDispatcher.registerListener<LoadModelEvent>([&](const LoadModelEvent& event) {
          std::cout << "Loading scene model AABB: " << StringID::getString(event.name()) << std::endl;
          m_sceneDataStorage.getContainer<CollisionMath::AABB>().insert(event.name(), event.modelData().bounds);
        });

        eventDispatcher.registerListener<LoadTerrainEvent>([&](const LoadTerrainEvent& event) {
          std::cout << "Loading scene terrain AABB and heightmap collision data: " << StringID::getString(event.name()) << std::endl;

          auto& terrainData = event.terrainData();
          auto& meshData = terrainData.meshData;
          auto& heightmapData = terrainData.heightmapData;

          m_sceneDataStorage.getContainer<CollisionMath::AABB>().insert(event.name(), meshData.bounds);
          m_sceneDataStorage.getContainer<std::shared_ptr<const HeightmapData>>().insert(event.name(), heightmapData);
        });

        eventDispatcher.registerListener<UnloadAssetEvent>([&](const UnloadAssetEvent& event) {
          std::cout << "Unloading scene collision data: " << StringID::getString(event.name()) << std::endl;

          // Remove from all containers
          m_sceneDataStorage.getContainer<CollisionMath::AABB>().remove(event.name());
          m_sceneDataStorage.getContainer<std::shared_ptr<const HeightmapData>>().remove(event.name());
        });
      }
      
      template<typename T>
      T& get(const StringID& name) {
        return m_sceneDataStorage.getContainer<T>().get(name);
      }

      template<typename T>
      const T& get(const StringID& name) const {
        return m_sceneDataStorage.getContainer<T>().get(name);
      }
      
      ~SceneDataStorage() = default;
  };
}

#endif
