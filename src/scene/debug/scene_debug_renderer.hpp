#ifndef __IF__SCENE_DEBUG_RENDERER_HPP
#define __IF__SCENE_DEBUG_RENDERER_HPP

// C++ standard library
#include <memory>

// Third-party libraries
#include <glm/glm.hpp>

// Local includes
#include "../../app/config/debug_config.hpp"
#include "../../renderer/renderer.hpp"
#include "../camera/camera.hpp"

namespace IronFrost {
  class SceneDebugRenderer {
    private:
      IRenderer& m_renderer;
      DebugConfig& m_debugConfig;
      
      MeshHandle m_debugCubeMesh;
      ShaderHandle m_debugShader;

      void processNode(const SceneNode& node, const glm::mat4& transform) {
        CollisionMath::AABB localAABB{glm::vec3(0.0f), glm::vec3(0.0f)};

        if (node.sceneObject) {
          const SceneObject& object = node.sceneObject.value();
          localAABB = object.bounds;
        } else if (node.sceneModel) {
          const SceneModel& model = node.sceneModel.value();
          localAABB = model.bounds;
        } else {
          return;
        }

        if (m_debugConfig.isEnabled(DebugFeature::BoundingBoxes)) {
          CollisionMath::AABB worldAABB = CollisionMath::transformAABB(localAABB, transform);
          renderWireframeAABB(worldAABB);
        }
      }
      
      void renderWireframeAABB(const CollisionMath::AABB& aabb, const glm::vec3& color = glm::vec3(1.0f)) {
        glm::vec3 center = aabb.getCenter();
        glm::vec3 size = aabb.getSize();
        glm::mat4 modelTransform = glm::translate(glm::mat4(1.0f), center) * glm::scale(glm::mat4(1.0f), size);

        m_renderer.submitRenderableObject(
          RenderableObject{m_debugShader, m_debugCubeMesh, {}, {}}, 
          InstanceData{.transform = modelTransform, .color = color}
        );
      }

    public:
      explicit SceneDebugRenderer(IRenderer& renderer) : m_renderer(renderer), m_debugConfig(ServiceLocator::getService<DebugConfig>()) {
        const IResourceManager& resourceManager = m_renderer.getResourceManager();

        m_debugCubeMesh = resourceManager.get<MeshHandle>(FallbackResources::DEBUG_CUBE_NAME);
        m_debugShader = resourceManager.get<ShaderHandle>(DefaultShaders::DEFAULT_DEBUG_SHADER_NAME);
      }

      ~SceneDebugRenderer() = default;
      
      SceneDebugRenderer(const SceneDebugRenderer&) = delete;
      SceneDebugRenderer& operator=(const SceneDebugRenderer&) = delete;

      void render(ISceneGraph& sceneGraph, Camera& camera) {
        m_renderer.clearRenderQueue();
        m_renderer.setViewProjection(camera.getViewMatrix(), camera.getProjectionMatrix());

        sceneGraph.traverse([&](const SceneNode& node, const glm::mat4& transform) {
          processNode(node, transform);
        });

        m_renderer.withRenderState({.depthTest = true, .cullFace = false, .blend = false, .wireframe = true}, [&]() {
          m_renderer.render();
        });
      }
  };
}

#endif
