#ifndef __IF__SCENE_HPP
#define __IF__SCENE_HPP

// C++ standard library
#include <memory>
#include <string>
#include <unordered_map>
#include <unordered_set>

// Local includes
#include "../gui/gui.hpp"
#include "../utils/string_id.hpp"
#include "camera/camera.hpp"
#include "scene_assets.hpp"
#include "scene_context.hpp"
#include "scene_graph/scene_graph.hpp"
#include "spatial_partitioning/spatial_partitioning.hpp"
#include "collisions/collision_cache.hpp"
#include "collisions/collision_handler.hpp"
#include "scene_lighting.hpp"
#include "scene_renderer.hpp"

namespace IronFrost {
  class AssetsManager;
  class EventDispatcher;
  struct GameContext;
  class IRenderer;
  class ISceneScriptContext;
  class IVFS;
  
  class IScene {
  protected:
    std::string m_scenePath;
    StringID m_sceneName;

    SceneContext& m_sceneContext;

    Camera m_camera;
    GUI m_gui;

    SceneLighting m_sceneLighting;
    SceneRenderer m_sceneRenderer;

    void setPostprocessUniforms();
    void registerEventListeners();

  public:
    IScene(const StringID& sceneName, std::string scenePath, SceneContext& sceneContext);
    IScene(const IScene &) = delete;
    IScene &operator=(const IScene &) = delete;

    virtual ~IScene() = default;

    virtual void initialize() = 0;

    virtual void load() = 0;
    virtual void unload() = 0;

    virtual void update(float deltaTime, GameContext& gameContext) = 0;
    virtual void render() = 0;
    
    virtual bool isLoaded() const { 
      return true; 
    };

    const StringID& getSceneName() const;
    
    Camera &getCamera();
    GUI &getGUI();
    SceneRenderer &getSceneRenderer();
  };

  class GameScene : public IScene {
  private:
    SceneAssets m_sceneAssets;
    std::unique_ptr<ISceneScriptContext> m_scriptContext;

    std::unique_ptr<ISceneGraph> m_sceneGraph{nullptr};
    std::unique_ptr<ISpatialPartitioning> m_spatialPartitioning{nullptr};
    
    CollisionCache m_collisionCache;

    std::unique_ptr<CollisionHandler> m_collisionHandler{nullptr};

    bool m_sceneReady{false};
    float m_sceneLoadingProgress{0.0f};

    void handleInput(float deltaTime, const GameContext& gameContext);

    void loadScene(const std::string& path);
    void unloadScene();

    void loadSceneScripts(const std::string& path);
    void unloadSceneScripts();

    void setUniforms();

    void updateSpatialPartitioning();
    void updateCollisions();

  public:
    GameScene(const StringID& sceneName, const std::string& scenePath, SceneContext& sceneContext);

    void initialize() override;
    
    void load() override;
    void unload() override;

    void update(float deltaTime, GameContext& gameContext) override;
    void render() override;

    bool isLoaded() const override {
      return m_sceneAssets.allAssetsLoaded() && m_sceneReady;
    }

    void executePerFrameScripts(float deltaTime);

    ISceneGraph& getSceneGraph();
    CollisionHandler& getCollisionHandler();
  };

  class LoadingScene : public IScene {
  public:
    LoadingScene(const std::string& scenePath, SceneContext& sceneContext);

    void initialize() override;

    void load() override;
    void unload() override;

    void update(float deltaTime, GameContext& gameContext) override;
    void render() override;
  };
}

#endif
