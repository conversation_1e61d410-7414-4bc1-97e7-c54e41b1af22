#ifndef __IF__COLLISION_AABB_HPP
#define __IF__COLLISION_AABB_HPP

// Local includes
#include "collision_shape.hpp"

namespace IronFrost {
  class CollisionSphere;

  class CollisionAABB : public CollisionShape {
    private:
      CollisionMath::AABB m_localAABB;
      CollisionMath::AABB m_worldAABB;

    public:
      CollisionAABB(const CollisionMath::AABB& aabb) : 
        m_localAABB(aabb), m_worldAABB(aabb) {}

      CollisionAABB(const CollisionMath::AABB& aabb, const glm::mat4& transform) :
        m_localAABB(aabb), m_worldAABB(CollisionMath::transformAABB(aabb, transform)) {}

      ~CollisionAABB() override = default;

      void update(const glm::mat4& transform) override;

      CollisionMath::AABB getWorldAABB() const override;

      bool intersects(const CollisionShape& other) const override;
      bool intersectsSphere(const CollisionSphere& sphere) const override;
      bool intersectsAABB(const CollisionAABB& aabb) const override;
      bool intersectsPlane(const CollisionPlane& plane) const override;
      bool intersectsTerrain(const CollisionTerrain& terrain) const override;

      std::optional<CollisionMath::SweepHit> sweepSphere(const CollisionMath::Sphere& sphere, const glm::vec3& delta) const override;

      glm::vec3 resolveSphereCollision(const CollisionMath::Sphere& sphere) override;      
      glm::vec3 resolveAABBCollision(const CollisionMath::AABB& aabb) override;
  };
}
  
#endif
