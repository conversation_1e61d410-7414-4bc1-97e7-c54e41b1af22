#ifndef __IF__COLLISION_TERRAIN_HPP
#define __IF__COLLISION_TERRAIN_HPP

// C++ standard library
#include <memory>

// Local includes
#include "collision_shape.hpp"
#include "../../../assets/data_types/heightmap_data.hpp"

namespace IronFrost {
  class CollisionTerrain : public CollisionShape {
    private:
      std::shared_ptr<const HeightmapData> m_heightmapData;

      float m_heightScale{1.0f};
      float m_blockSize{4.0f};

      glm::mat4 m_transform{1.0f};
      glm::mat4 m_transformInverse{1.0f};
      glm::mat3 m_transformNormal{1.0f};

      CollisionMath::AABB m_localAABB;
      CollisionMath::AABB m_worldAABB;

      std::optional<float> getTerrainHeight(float worldX, float worldZ) const;

    public:
      CollisionTerrain(const std::shared_ptr<const HeightmapData>& heightmapData, float heightScale, float blockSize, const CollisionMath::AABB& aabb) :
        m_heightmapData(heightmapData),
        m_heightScale(heightScale),
        m_blockSize(blockSize),
        m_localAABB(aabb)
      {}

      ~CollisionTerrain() override = default;

      void update(const glm::mat4& transform) override;

      CollisionMath::AABB getWorldAABB() const override;

      bool intersects(const CollisionShape& other) const override;
      bool intersectsSphere(const CollisionSphere& sphere) const override;
      bool intersectsAABB(const CollisionAABB& aabb) const override;
      bool intersectsPlane(const CollisionPlane& plane) const override;
      bool intersectsTerrain(const CollisionTerrain& terrain) const override;

      glm::vec3 resolveSphereCollision(const CollisionMath::Sphere& sphere) override;      
      glm::vec3 resolveAABBCollision(const CollisionMath::AABB& aabb) override;
  };
}

#endif
