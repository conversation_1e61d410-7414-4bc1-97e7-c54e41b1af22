#include "collision_plane.hpp"
#include "collision_sphere.hpp"
#include "collision_aabb.hpp"
#include "collision_terrain.hpp"

namespace IronFrost {
  void CollisionPlane::update(const glm::mat4& transform) {
    m_worldPlane = CollisionMath::transformPlane(m_localPlane, transform);
  }

  CollisionMath::Plane CollisionPlane::getWorldPlane() const {
    return m_worldPlane;
  }

  CollisionMath::AABB CollisionPlane::getWorldAABB() const {
    return CollisionMath::AABB::fromCenterAndSize(m_worldPlane.normal * m_worldPlane.distance, glm::vec3(1.0f));
  }

  bool CollisionPlane::intersects(const CollisionShape& other) const {
    return other.intersectsPlane(*this);
  }

  bool CollisionPlane::intersectsSphere(const CollisionSphere& sphere) const {
    return CollisionMath::spherePlaneIntersection(sphere.getWorldSphere(), m_worldPlane);
  } 

  bool CollisionPlane::intersectsAABB(const CollisionAABB& aabb) const {
    return CollisionMath::aabbPlaneIntersection(aabb.getWorldAABB(), m_worldPlane);
  }

  bool CollisionPlane::intersectsPlane(const CollisionPlane& plane) const {
    return CollisionMath::planePlaneIntersection(m_worldPlane, plane.getWorldPlane());
  }

  bool CollisionPlane::intersectsTerrain(const CollisionTerrain& terrain) const {
    return CollisionMath::aabbPlaneIntersection(terrain.getWorldAABB(), m_worldPlane);
  }

  std::optional<CollisionMath::SweepHit> CollisionPlane::sweepSphere(const CollisionMath::Sphere& sphere, const glm::vec3& delta) const {
    return CollisionMath::sweepSpherePlaneCollision(sphere, m_worldPlane, delta);
  }

  glm::vec3 CollisionPlane::resolveSphereCollision(const CollisionMath::Sphere& sphere) {
    return CollisionMath::resolveSpherePlaneCollision(sphere, m_worldPlane);
  }

  glm::vec3 CollisionPlane::resolveAABBCollision(const CollisionMath::AABB& aabb) {
    return CollisionMath::resolveAABBPlaneCollision(aabb, m_worldPlane);
  }
}
