#include "collision_terrain.hpp"
#include "collision_sphere.hpp"
#include "collision_aabb.hpp"
#include "collision_plane.hpp"

#include <iostream>

namespace IronFrost {
  std::optional<float> CollisionTerrain::getTerrainHeight(float worldX, float worldZ) const {
    // World to local
    glm::vec4 localPoint = m_transformInverse * glm::vec4(worldX, 0.0f, worldZ, 1.0f);

    // Local to heightmap
    const int W = m_heightmapData->width;
    const int H = m_heightmapData->height;

    float x = (localPoint.x / m_blockSize + 0.5f) * float(W - 1);
    float z = (localPoint.z / m_blockSize + 0.5f) * float(H - 1);

    if (x < 0 || x >= m_heightmapData->width || z < 0 || z >= m_heightmapData->height) {
      return std::nullopt;
    }

    float localY = m_heightmapData->getInterpolatedHeight(x, z) * m_heightScale;

    glm::vec4 worldPoint = m_transform * glm::vec4(localPoint.x, localY, localPoint.z, 1.0f);

    return worldPoint.y;
  }

  void CollisionTerrain::update(const glm::mat4& transform) {
    m_transform = transform;

    m_transformInverse = glm::inverse(transform);
    m_transformNormal = glm::inverseTranspose(glm::mat3(transform));

    m_worldAABB = CollisionMath::transformAABB(m_localAABB, transform);
  }

  CollisionMath::AABB CollisionTerrain::getWorldAABB() const {
    return m_worldAABB;
  }

  bool CollisionTerrain::intersects(const CollisionShape& other) const {
    return other.intersectsTerrain(*this);
  }

  bool CollisionTerrain::intersectsSphere(const CollisionSphere& sphere) const {
    return CollisionMath::sphereAABBIntersection(sphere.getWorldSphere(), m_worldAABB);
  }

  bool CollisionTerrain::intersectsAABB(const CollisionAABB& aabb) const {
    return CollisionMath::aabbIntersection(aabb.getWorldAABB(), m_worldAABB);
  }

  bool CollisionTerrain::intersectsPlane(const CollisionPlane& plane) const {
    return CollisionMath::aabbPlaneIntersection(m_worldAABB, plane.getWorldPlane());
  }

  bool CollisionTerrain::intersectsTerrain(const CollisionTerrain& terrain) const {
    return CollisionMath::aabbIntersection(m_worldAABB, terrain.getWorldAABB());
  }

  glm::vec3 CollisionTerrain::resolveSphereCollision(const CollisionMath::Sphere& sphere) {
    if (auto h = getTerrainHeight(sphere.center.x, sphere.center.z)) {
      if (sphere.center.y - sphere.radius < *h) {
        return glm::vec3(sphere.center.x, *h + sphere.radius, sphere.center.z);
      }
    }
    return sphere.center;
  }

  glm::vec3 CollisionTerrain::resolveAABBCollision(const CollisionMath::AABB& aabb) {
    glm::vec3 center = aabb.getCenter();
    const float halfY = (aabb.max.y - aabb.min.y) * 0.5f;

    float maxHeight = -std::numeric_limits<float>::infinity();

    const float corners[4][2] = {
      {aabb.min.x, aabb.min.z},
      {aabb.max.x, aabb.min.z},
      {aabb.min.x, aabb.max.z},
      {aabb.max.x, aabb.max.z}
    };

    for (const auto& corner : corners) {
      if (auto h = getTerrainHeight(corner[0], corner[1])) {
        maxHeight = std::max(maxHeight, *h);
      }
    }

    if (maxHeight == -std::numeric_limits<float>::infinity()) {
      return center;
    }

    if (center.y - halfY < maxHeight) {
      return glm::vec3(center.x, maxHeight + halfY, center.z);
    }

    return center;
  }
}
