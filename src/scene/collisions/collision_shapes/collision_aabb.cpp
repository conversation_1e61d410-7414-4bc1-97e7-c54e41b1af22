#include "collision_aabb.hpp"
#include "collision_sphere.hpp"
#include "collision_plane.hpp"
#include "collision_terrain.hpp"

namespace IronFrost {
  void CollisionAABB::update(const glm::mat4& transform) {
    m_worldAABB = CollisionMath::transformAABB(m_localAABB, transform);
  }

  CollisionMath::AABB CollisionAABB::getWorldAABB() const {
    return m_worldAABB;
  }

  bool CollisionAABB::intersects(const CollisionShape& other) const {
    return other.intersectsAABB(*this);
  }

  bool CollisionAABB::intersectsSphere(const CollisionSphere& sphere) const {
    return CollisionMath::sphereAABBIntersection( sphere.getWorldSphere(), m_worldAABB);
  }

  bool CollisionAABB::intersectsAABB(const CollisionAABB& aabb) const {
    return CollisionMath::aabbIntersection(m_worldAABB, aabb.getWorldAABB());
  }

  bool CollisionAABB::intersectsPlane(const CollisionPlane& plane) const {
    return CollisionMath::aabbPlaneIntersection(m_worldAABB, plane.getWorldPlane());
  }

  bool CollisionAABB::intersectsTerrain(const CollisionTerrain& terrain) const {
    return CollisionMath::aabbIntersection(m_worldAABB, terrain.getWorldAABB());
  }

  std::optional<CollisionMath::SweepHit> CollisionAABB::sweepSphere(const CollisionMath::Sphere& sphere, const glm::vec3& delta) const {
    return CollisionMath::sweepSphereAABBCollision(sphere, m_worldAABB, delta);
  }

  glm::vec3 CollisionAABB::resolveSphereCollision(const CollisionMath::Sphere& sphere) {
    return CollisionMath::resolveSphereAABBCollision(sphere, m_worldAABB);
  }

  glm::vec3 CollisionAABB::resolveAABBCollision(const CollisionMath::AABB& aabb) {
    return CollisionMath::resolveAABBAABBCollision(aabb, m_worldAABB);
  }
}
