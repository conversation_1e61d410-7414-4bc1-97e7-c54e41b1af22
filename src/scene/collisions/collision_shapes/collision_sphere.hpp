#ifndef __IF__COLLISION_SPHERE_HPP
#define __IF__COLLISION_SPHERE_HPP

// Local includes
#include "collision_shape.hpp"

namespace IronFrost {
  class CollisionSphere : public CollisionShape {
    private:
      CollisionMath::Sphere m_localSphere;
      CollisionMath::Sphere m_worldSphere;

    public:
      CollisionSphere(const CollisionMath::Sphere& sphere) : 
        m_localSphere(sphere), m_worldSphere(sphere) {}

      CollisionSphere(const CollisionMath::Sphere& sphere, const glm::mat4& transform) :
        m_localSphere(sphere), m_worldSphere(CollisionMath::transformSphere(sphere, transform)) {}

      ~CollisionSphere() override = default;

      void update(const glm::mat4& transform) override;

      CollisionMath::Sphere getWorldSphere() const;
      CollisionMath::AABB getWorldAABB() const override;

      bool intersects(const CollisionShape& other) const override;
      bool intersectsSphere(const CollisionSphere& sphere) const override;
      bool intersectsAABB(const CollisionAABB& aabb) const override;
      bool intersectsPlane(const CollisionPlane& plane) const override;
      bool intersectsTerrain(const CollisionTerrain& terrain) const override;

      glm::vec3 resolveSphereCollision(const CollisionMath::Sphere& sphere) override;      
      glm::vec3 resolveAABBCollision(const CollisionMath::AABB& aabb) override;
  };
}

#endif
