#ifndef __IF__COLLISION_HANDLER_HPP
#define __IF__COLLISION_HANDLER_HPP

// Third-party libraries
#include <glm/glm.hpp>
#define GLM_ENABLE_EXPERIMENTAL
#include <glm/gtx/norm.hpp>

// Local includes
#include "collision_cache.hpp"
#include "collision_shapes/collision_shape.hpp"
#include "collision_shapes/collision_aabb.hpp"
#include "collision_shapes/collision_sphere.hpp"
#include "../../utils/collision_math.hpp"
#include "../scene_graph/scene_node.hpp"
#include "../camera/camera.hpp"

namespace IronFrost {
  class CollisionHandler {
    private:
      CollisionCache& m_collisionCache;

    public:
      Col<PERSON><PERSON><PERSON>andler(CollisionCache& collisionCache) : m_collisionCache(collisionCache) {}
      
      glm::vec3 resolveCameraMovement(const Camera& camera, const glm::vec3& desiredPosition) const {
        glm::vec3 finalPosition = desiredPosition;
        const float radius = camera.getCollisionRadius();
        constexpr float epsSquared = 1e-10f;
        constexpr int maxIterations = 3;

        if (radius <= 0.0f) {
          return desiredPosition;
        }

        for (int i = 0; i < maxIterations; ++i) {
          CollisionMath::Sphere cameraSphere{finalPosition, radius};
          bool collision = false;

          for (const auto& candidate : m_collisionCache.getCandidates()) {
            for (const auto& shape : candidate->sceneCollisionShapes) {
              if (shape->intersectsSphere(cameraSphere)) {
                glm::vec3 newPosition = shape->resolveSphereCollision(cameraSphere);

                if (glm::length2(newPosition - finalPosition) > epsSquared) {
                  finalPosition = newPosition;
                  collision = true;
                  goto next_iteration;
                }
              }
            }
          }

          next_iteration:
          if (!collision) break;
        }

        return finalPosition;
      }
  };
}

#endif
