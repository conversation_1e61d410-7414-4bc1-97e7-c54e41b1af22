#ifndef __IF__COLLISION_CACHE_HPP
#define __IF__COLLISION_CACHE_HPP

// C++ standard library
#include <vector>

// Local includes
#include "../scene_graph/scene_node.hpp"
#include "collision_shapes/collision_shape.hpp"

namespace IronFrost {
   class CollisionCache {
    private:
      std::vector<SceneNode*> m_candidates;

    public:
      void addCandidate(SceneNode* node) {
        m_candidates.push_back(node);
      }

      void clearCandidates() {
        m_candidates.clear();
      }

      const std::vector<SceneNode*>& getCandidates() const {
        return m_candidates;
      }
  };
}

#endif
