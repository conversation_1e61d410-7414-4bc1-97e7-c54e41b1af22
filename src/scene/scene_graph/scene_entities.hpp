#ifndef __IF__SCENE_ENTITIES_HPP
#define __IF__SCENE_ENTITIES_HPP

// C++ standard library
#include <memory>
#include <variant>

// Third-party libraries
#include <glm/glm.hpp>

// Local includes
#include "../collisions/collision_shapes/collision_shape.hpp"
#include "../../renderer/renderables/renderables.hpp"

namespace IronFrost {
  struct SceneObject {
    RenderableObjectID renderableObjectID;

    glm::vec2 uvTiling{1.0f, 1.0f};
    CollisionMath::AABB bounds{glm::vec3(0.0f), glm::vec3(0.0f)};
  };

  struct SceneModel {
    RenderableModelID renderableModelID;

    CollisionMath::AABB bounds{glm::vec3(0.0f), glm::vec3(0.0f)};
  };

  struct SceneLight {
    glm::vec3 color{1.0f, 1.0f, 1.0f};
    float intensity{1.0f};

    float constant{1.0f};
    float linear{0.09f};
    float quadratic{0.032f};
  };

  using SceneEntity = std::variant<SceneObject, SceneModel, SceneLight>;
  using SceneCollisionShape = CollisionShape;
}

#endif
