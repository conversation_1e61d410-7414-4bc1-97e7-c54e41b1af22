#include "scene_list.hpp"

// C++ standard library
#include <functional>

namespace IronFrost {
  void SceneList::traverse(std::function<void(SceneNode&, const glm::mat4&)> callback) {
    for (auto& [name, sceneNode] : m_nodes) {
      sceneNode.traverse(glm::mat4(1.0F), callback);
    }
  }

  void SceneList::traverseDirty(std::function<void(SceneNode&, const glm::mat4&)> callback) {
    for (auto& [name, sceneNode] : m_nodes) {
      sceneNode.traverseDirty(glm::mat4(1.0F), callback);
    }
  }

  void SceneList::insert(StringID name, SceneNode&& object) {
    m_nodes[name] = std::move(object);
  }

  SceneNode& SceneList::get(StringID name) {
    auto it = m_nodes.find(name);
    if (it == m_nodes.end()) {
      throw std::runtime_error("SceneNode with name " + StringID::getString(name) + " not found");
    }
    return it->second;
  }
}
