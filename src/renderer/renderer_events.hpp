#ifndef __IF__RENDERER_EVENTS_HPP
#define __IF__RENDERER_EVENTS_HPP

// Local includes
#include "../assets/asset_types.hpp"
#include "../events/events.hpp"

namespace IronFrost {
  class ResourceCreatedEvent : public Event {
    private:
      const StringID m_name;
      const AssetType m_type;

    public:
      ResourceCreatedEvent(const StringID& name, AssetType type) :
        m_name(name),
        m_type(type)
      {}

      std::string toString() const override {
        return "ResourceCreatedEvent";
      }

      const StringID& name() const { return m_name; }
      AssetType type() const { return m_type; }
  };

  class DestroyResourceEvent : public Event {
    private:
      const StringID m_name;
      const AssetType m_type;

    public:
      DestroyResourceEvent(const StringID& name, AssetType type) :
        m_name(name),
        m_type(type)
      {}

      std::string toString() const override {
        return "DestroyResourceEvent";
      }

      const StringID& name() const { return m_name; }
      AssetType type() const { return m_type; }
  };
}

#endif
