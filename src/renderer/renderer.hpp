#ifndef __IF__RENDERER_HPP
#define __IF__RENDERER_HPP

// C++ standard library
#include <functional>
#include <memory>
#include <string>
#include <unordered_map>
#include <vector>

// Local includes
#include "../utils/string_id.hpp"
#include "gpu_handles.hpp"
#include "instance_data.hpp"
#include "multipass_framebuffer.hpp"
#include "renderables/renderables.hpp"
#include "renderer_queue.hpp"
#include "pixel_rect.hpp"

namespace IronFrost
{
  enum RENDERER_TYPE {
    OPENGL
  };

  struct RenderState {
    bool depthTest{true};
    bool cullFace{true};
    bool blend{true};
    bool wireframe{false};
  };

  namespace DefaultShaders {
    inline const StringID DEFAULT_SHADER_NAME = StringID("shader::system::default");
    inline const StringID DEFAULT_POSTPROCESS_SHADER_NAME = StringID("shader::system::postprocess::default");
    inline const StringID DEFAULT_GUI_SHADER_NAME = StringID("shader::system::gui::default");
    inline const StringID DEFAULT_TEXT_SHADER_NAME = StringID("shader::system::text::default");
    inline const StringID DEFAULT_DEBUG_SHADER_NAME = StringID("shader::system::debug::default");
  }

  class AssetsManager;
  class GUIRenderer;
  struct PostprocessEffect;
  class RenderablesManager;
  class IPostprocessManager;
  class IResourceManager;
  class IWindow;

  class IRenderer {
    protected:
      std::unique_ptr<IMultipassFramebuffer> m_multipassFramebuffer;
      std::unique_ptr<IResourceManager> m_resourceManager;
      std::unique_ptr<IPostprocessManager> m_postprocessManager;
      std::unique_ptr<RenderablesManager> m_renderablesManager;
      std::unique_ptr<GUIRenderer> m_guiRenderer;

      RendererQueue m_rendererQueue;
      std::vector<RenderableLight> m_renderableLights;
      
      glm::mat4 m_projectionMatrix{glm::mat4(1.0)};
      glm::mat4 m_viewMatrix{glm::mat4(1.0)};

      IWindow& m_window;
      ShaderUniforms m_globalUniforms;

      IRenderer(IWindow& _window);
    public:
      IRenderer(const IRenderer&) = delete;
      IRenderer(IRenderer&&) = delete;
      IRenderer &operator=(const IRenderer&) = delete;
      
      virtual ~IRenderer();

      const IWindow& getWindow();
      GUIRenderer& getGUIRenderer();

      template<typename T>
      void setGlobalUniform(const std::string& name, const T& value) {
        m_globalUniforms.set(name, value);
      }

      IMultipassFramebuffer& getMultipassFramebuffer() const {
        return *m_multipassFramebuffer;
      }

      IResourceManager& getResourceManager() const {
        return *m_resourceManager;
      }

      RenderablesManager& getRenderablesManager() const {
        return *m_renderablesManager;
      }

      virtual void useShader(unsigned int _id) = 0;
      virtual void unbindShader() = 0;

      PostprocessEffect& createPostprocessEffect(const StringID& name, const std::vector<std::string>& shaderNames);
      PostprocessEffect& getPostprocessEffect(const StringID& name);

      void setViewProjection(const glm::mat4& view, const glm::mat4& projection);
      void setView(const glm::mat4& view);
      void setProjection(const glm::mat4& projection);

      void submitRenderableObject(const RenderableObject& renderableObject, const InstanceData& instanceData);
      void submitRenderableObject(RenderableObjectID renderableObjectID, const InstanceData& instanceData);
      void submitRenderableLight(const RenderableLight& renderableLight);
      void clearRenderQueue();

      virtual void beginFrame() = 0;
      virtual void endFrame() = 0;
      virtual void render() = 0;
      virtual void renderToFramebuffer(std::function<void()> callback) = 0;
      virtual void renderPostprocessEffect(const PostprocessEffect& postprocessEffect) = 0;

      virtual void withRenderState(const RenderState& state, std::function<void()> func) = 0;
      virtual void withScissorRect(const PixelRect& rect, std::function<void()> func) = 0;

      static std::unique_ptr<IRenderer> create(RENDERER_TYPE rendererType, IWindow& window);
    };
}

#endif
