#ifndef __IF__SHADER_UNIFORM_HPP
#define __IF__SHADER_UNIFORM_HPP

// C++ standard library
#include <string>
#include <unordered_map>
#include <utility>
#include <variant>

// Third-party libraries
#include <glm/glm.hpp>

namespace IronFrost {
  using UniformValue = std::variant<int, float, glm::vec2, glm::vec3, glm::vec4, glm::mat3, glm::mat4>;

  class ShaderUniforms {
    private:
      std::unordered_map<std::string, UniformValue> uniforms;

    public:
      ShaderUniforms() = default; 
      
      ShaderUniforms(std::initializer_list<std::pair<const std::string, UniformValue>> init) {
        for (const auto& [key, value] : init) {
            uniforms[key] = value;
        }
      }

      template<typename T>
      void set(const std::string& name, const T& value) {
        uniforms[name] = value;
      }

      template<typename T>
      T get(const std::string& name) const {
        return std::get<T>(uniforms.at(name));
      }

      auto begin() { return uniforms.begin(); }
      auto end() { return uniforms.end(); }
      auto begin() const { return uniforms.begin(); }
      auto end() const { return uniforms.end(); }

      bool empty() const { return uniforms.empty(); }
  };
}

#endif
