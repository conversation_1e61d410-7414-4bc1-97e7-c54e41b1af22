#ifndef __IF__FALLBACK_RESOURCES_HPP
#define __IF__FALLBACK_RESOURCES_HPP

// Local includes
#include "../utils/string_id.hpp"

namespace IronFrost {
  namespace FallbackResources {
    inline const StringID DEFAULT_QUAD_NAME = StringID("mesh::system::quad");

    inline const StringID FALLBACK_MESH_NAME = StringID("mesh::system::fallback");
    inline const StringID FALLBACK_TEXTURE_NAME = StringID("texture::system::fallback");
    inline const StringID FALLBACK_MODEL_NAME = StringID("model::system::fallback");
    inline const StringID FALLBACK_MATERIAL_NAME = StringID("material::system::fallback");

    inline const StringID DEBUG_CUBE_NAME = StringID("mesh::system::debug::cube");
  }
}

#endif
