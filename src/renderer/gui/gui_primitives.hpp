#ifndef __IF__GUI_PRIMITIVES_HPP
#define __IF__GUI_PRIMITIVES_HPP

// C++ standard library
#include <string>
#include <variant>

// Third-party libraries
#include <glm/glm.hpp>

// Local includes
#include "../../utils/color.hpp"
#include "../gpu_handles.hpp"
#include "../pixel_rect.hpp"

namespace IronFrost {
  namespace GUIPrimitives {
    struct Quad {
      glm::mat4 transform;
      glm::vec4 uv;

      TextureHandle texture;
      Color color;

      int layer{0};
    };

    struct Text {
      glm::vec2 position;
      glm::vec2 size;
      std::string text;

      FontHandle font;
      Color color;

      int layer{0};
    };

    struct ClipPush {
      glm::vec4 rect;
    };

    struct ClipPop {
    };
  }

  using GUIPrimitive = std::variant<
    GUIPrimitives::Quad, 
    GUIPrimitives::Text, 
    GUIPrimitives::ClipPush, 
    GUIPrimitives::ClipPop
  >;
  using GUIPrimitiveList = std::vector<GUIPrimitive>;
}

#endif
