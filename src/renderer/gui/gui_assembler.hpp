#ifndef __IF__GUI_ASSEMBLER_HPP
#define __IF__GUI_ASSEMBLER_HPP

// C++ standard library
#include <variant>

// Third-party libraries
#include <glm/glm.hpp>

// Local includes
#include "gui_primitives.hpp"
#include "../gpu_handles.hpp"
#include "../resource_manager.hpp"
#include "gui_render_context.hpp"
#include "handlers/quad_handler.hpp"
#include "handlers/text_handler.hpp"

namespace IronFrost {
  class GUIAssembler {
    private:
      GUIRenderContext& m_guiRenderContext;

      std::vector<glm::vec4> m_clipStack;

      void handle(const GUIPrimitives::Quad& quad) {
        QuadHandler quadHandler(m_guiRenderContext);
        quadHandler(quad, getScissorRect());
      }

      void handle(const GUIPrimitives::Text& text) {
        TextHandler textHandler(m_guiRenderContext);
        textHandler(text, getScissorRect());
      }

      void handle(const GUIPrimitives::ClipPush& clipPush) {
        if (m_clipStack.empty()) {
          m_clipStack.push_back(clipPush.rect);
        } else {
          m_clipStack.push_back(intersect(m_clipStack.back(), clipPush.rect));
        }
      }

      void handle(const GUIPrimitives::ClipPop& /* clipPop */) {
        m_clipStack.pop_back();
      }

      std::optional<glm::vec4> getScissorRect() {
        if (m_clipStack.empty()) {
          return std::nullopt;
        }

        return m_clipStack.back();
      }

      static glm::vec4 intersect(const glm::vec4& a, const glm::vec4& b) {
        float x1 = std::max(a.x, b.x);
        float y1 = std::max(a.y, b.y);
        float x2 = std::min(a.x + a.z, b.x + b.z);
        float y2 = std::min(a.y + a.w, b.y + b.w);

        return glm::vec4(x1, y1, std::max(0.0f, x2 - x1), std::max(0.0f, y2 - y1));
      }

    public:
      GUIAssembler(GUIRenderContext& guiRenderContext) :
        m_guiRenderContext(guiRenderContext)
      {}

      void begin() {
        IRenderer& renderer = m_guiRenderContext.renderer;

        renderer.clearRenderQueue();
        renderer.setViewProjection(glm::mat4(1.0F), m_guiRenderContext.guiProjection.getProjectionMatrix());
      }

      void submit(const GUIPrimitiveList& primitiveList) {
        for (const GUIPrimitive& primitive : primitiveList) {
          std::visit([&](const auto& p) { handle(p); }, primitive);
        }
      }

      void end() {
        IRenderer& renderer = m_guiRenderContext.renderer;

        renderer.withRenderState({.depthTest = false, .cullFace = false, .blend = true}, [&]() {
          renderer.render();
        });
      }
  };
}

#endif
