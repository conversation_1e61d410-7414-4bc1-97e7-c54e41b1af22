#ifndef __IF__GUI_PROJECTION_HPP
#define __IF__GUI_PROJECTION_HPP

// Third-party libraries
#include <glm/glm.hpp>

// Local includes
#include "../pixel_rect.hpp"

namespace IronFrost {
  class GUIProjection {
    private:
      glm::mat4 m_projectionMatrix{1.0f};

      float m_baseScreenWidth{3840.0};
      float m_baseScreenHeight{2160.0};

      int m_screenWidth{0};
      int m_screenHeight{0};

      float m_scaleFactor{1.0F};

      float m_offsetX{0.0F};
      float m_offsetY{0.0F};

    public:
      GUIProjection() = default;
      GUIProjection(const glm::mat4& projectionMatrix) : m_projectionMatrix(projectionMatrix) {}

      const glm::mat4& getProjectionMatrix() const {
        return m_projectionMatrix;
      }

      void updateProjectionMatrix(int screenWidth, int screenHeight) {
        m_screenWidth = screenWidth;
        m_screenHeight = screenHeight;

        m_scaleFactor = std::min(static_cast<float>(screenWidth) / m_baseScreenWidth, 
                                static_cast<float>(screenHeight) / m_baseScreenHeight);

        // Calculate effective dimensions and offsets in one step
        float effectiveWidth = screenWidth / m_scaleFactor;
        float effectiveHeight = screenHeight / m_scaleFactor;
        
        m_offsetX = (m_baseScreenWidth - effectiveWidth) * 0.5f;
        m_offsetY = (m_baseScreenHeight - effectiveHeight) * 0.5f;

        m_projectionMatrix = glm::ortho(m_offsetX, m_offsetX + effectiveWidth, 
                                       m_offsetY + effectiveHeight, m_offsetY, -1.0f, 1.0f);
      }

      PixelRect guiToScreenRect(const glm::vec4& rect) const {
        // Transform GUI coordinates to screen pixels
        glm::vec2 screenPos = (glm::vec2(rect.x, rect.y) - glm::vec2(m_offsetX, m_offsetY)) * m_scaleFactor;
        glm::vec2 screenSize = glm::vec2(rect.z, rect.w) * m_scaleFactor;
        
        // Convert to pixel coordinates (flip Y axis)
        int x1 = static_cast<int>(std::floor(screenPos.x));
        int y1 = static_cast<int>(std::floor(m_screenHeight - (screenPos.y + screenSize.y)));
        int x2 = static_cast<int>(std::ceil(screenPos.x + screenSize.x));
        int y2 = static_cast<int>(std::ceil(m_screenHeight - screenPos.y));
        
        // Clamp to screen bounds
        x1 = std::clamp(x1, 0, m_screenWidth);
        y1 = std::clamp(y1, 0, m_screenHeight);
        x2 = std::clamp(x2, 0, m_screenWidth);
        y2 = std::clamp(y2, 0, m_screenHeight);

        return PixelRect{x1, y1, x2 - x1, y2 - y1};
      }

      float getBaseScreenWidth() const {
        return m_baseScreenWidth;
      }

      float getBaseScreenHeight() const {
        return m_baseScreenHeight;
      }
  };
}

#endif
