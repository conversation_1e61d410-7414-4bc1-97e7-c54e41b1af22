#ifndef __IF__GUI_RENDERER_HPP
#define __IF__GUI_RENDERER_HPP

// C++ standard library
#include <unordered_map>
#include <vector>

// Local includes
#include "../gpu_handles.hpp"
#include "../renderables/renderable_object.hpp"
#include "gui_projection.hpp"
#include "gui_primitives_cache.hpp"
#include "gui_assembler.hpp"
#include "gui_render_context.hpp"

namespace IronFrost {
  class EventDispatcher;
  class GUI;
  class IRenderer;
  class ImageWidget;
  class LabelWidget;
  class PanelWidget;
  class Widget;

  class GUIRenderer {
    private:
      IRenderer& m_renderer;

      GUIProjection m_guiProjection;

      GUIPrimitivesCache m_guiPrimitivesCache;

      GUIRenderContext m_guiRenderContext;
      GUIAssembler m_guiAssembler;

      void submitWidget(Widget& widget);
      void createAndCacheRenderables(Widget& widget);

    public:
      G<PERSON><PERSON><PERSON><PERSON>(IRenderer& renderer);

      GUIRenderer& operator=(const GUIRenderer&) = delete;
      GUIRenderer(const GUIRenderer&) = delete;

      void render(GUI& gui);
  };
}

#endif
