#ifndef __IF__GUI_PRIMITIVE_BUILDER_HPP
#define __IF__GUI_PRIMITIVE_BUILDER_HPP

// C++ standard library
#include <vector>

// Local includes
#include "gui_primitives.hpp"
#include "gui_layers.hpp"

namespace IronFrost {
  class GUIPrimitiveBuilder {
    private:
      GUIPrimitiveList& m_primitivesList;

      std::vector<int> m_layerStack;

    public:
      GUIPrimitiveBuilder(GUIPrimitiveList& primitivesList) : 
        m_primitivesList(primitivesList) 
      {}

      void add(GUIPrimitives::Quad& quad) {
        quad.layer += getCurrentLayer();
        m_primitivesList.push_back(quad);
      }

      void add(GUIPrimitives::Quad&& quad) {
        quad.layer += getCurrentLayer();
        m_primitivesList.push_back(std::move(quad));
      }

      void add(GUIPrimitives::Text& text) {
        text.layer += getCurrentLayer();
        m_primitivesList.push_back(text);
      }

      void add(GUIPrimitives::Text&& text) {
        text.layer += getCurrentLayer();
        m_primitivesList.push_back(std::move(text));
      }

      void add(GUIPrimitives::ClipPush& clipPush) {
        m_primitivesList.push_back(clipPush);
      }

      void add(GUIPrimitives::ClipPush&& clipPush) {
        m_primitivesList.push_back(std::move(clipPush));
      }

      void add(GUIPrimitives::ClipPop& clipPop) {
        m_primitivesList.push_back(clipPop);
      }

      void add(GUIPrimitives::ClipPop&& clipPop) {
        m_primitivesList.push_back(std::move(clipPop));
      }

      void pushLayer(int step = LAYER_STEP) {
        m_layerStack.push_back(step);
      }

      void popLayer() {
        if (!m_layerStack.empty()) {
          m_layerStack.pop_back();
        }
      }

      int getCurrentLayer() const {
        return m_layerStack.empty() ? 0 : m_layerStack.back();
      }

      GUIPrimitiveList& getPrimitives() {
        return m_primitivesList;
      }

      void clear() {
        m_primitivesList.clear();
      }
  };
}

#endif
