#ifndef __IF__QUAD_HANDLER_HPP
#define __IF__QUAD_HANDLER_HPP

namespace IronFrost {
  class QuadHandler {
    private:
      GUIRenderContext& m_guiRenderContext;

    public:
      QuadHandler(GUIRenderContext& guiRenderContext) :
        m_guiRenderContext(guiRenderContext)
      {}

      void operator()(const GUIPrimitives::Quad& quad, std::optional<glm::vec4> scissorRect) {
        const GUIMaterial material{quad.texture};

        RenderableObject renderableObject{
          .shaderHandle = m_guiRenderContext.guiShader,
          .meshHandle = m_guiRenderContext.quadMesh,
          .material = material,
          .uniforms = {
            {"color", quad.color.getRGB()},
            {"alpha", quad.color.getA()}
          }
        };

        InstanceData instanceData{.transform = quad.transform, .layer = quad.layer};

        if(scissorRect.has_value()) {
          instanceData.scissorRect = m_guiRenderContext.guiProjection.guiToScreenRect(scissorRect.value());
        }

        m_guiRenderContext.renderer.submitRenderableObject(renderableObject, instanceData);
      }

  };
}

#endif
