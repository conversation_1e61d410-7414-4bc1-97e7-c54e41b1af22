#ifndef __IF__GUI_PRIMITIVES_CACHE_HPP
#define __IF__GUI_PRIMITIVES_CACHE_HPP

// C++ standard library
#include <unordered_map>

// Local includes
#include "gui_primitives.hpp"
#include "../../gui/widgets/widget.hpp"

namespace IronFrost {
  class GUIPrimitivesCache {
    private:
      std::unordered_map<Widget*, GUIPrimitiveList> m_cache;

    public:
      GUIPrimitiveList& get(Widget* widget) {
        return m_cache[widget];
      }

      void invalidate(Widget* widget) {
        m_cache[widget].clear();
      }

      void clear() {
        m_cache.clear();
      }
  };
}

#endif
