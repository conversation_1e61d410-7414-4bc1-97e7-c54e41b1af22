#ifndef __IF__RECT_BUILDER_HPP
#define __IF__RECT_BUILDER_HPP

// Third-party libraries
#include <glm/glm.hpp>

// Local includes
#include "../gui_primitives.hpp"
#include "../gui_primitive_builder.hpp"
#include "../../gpu_handles.hpp"
#include "../../resource_manager.hpp"

namespace IronFrost {
  class QuadBuilder {
    private:
      IResourceManager& m_resourceManager;

      glm::mat4 m_transform{1.0f};
      glm::vec2 m_size{1.0f, 1.0f};
      glm::vec2 m_position{0.0f, 0.0f};
      float m_rotation{0.0f};

      Color m_color{1.0f, 1.0f, 1.0f, 1.0f};

      glm::vec4 m_uv{0.0f, 0.0f, 1.0f, 1.0f};
      TextureHandle m_texture{0};

      glm::vec4 m_border{0.0f, 0.0f, 0.0f, 0.0f}; // {top, right, bottom, left} - CSS-style
      Color m_borderColor{0.0f, 0.0f, 0.0f, 1.0f};
      bool m_borderEnabled{false};

      int m_layer{0};
      
      glm::mat4 computeTransform(const glm::vec2& position, const glm::vec2& size, float rotation) {
        glm::mat4 transform = glm::mat4(1.0f);
        transform = glm::translate(transform, glm::vec3(position, 0.0f));
        transform = glm::rotate(transform, glm::radians(rotation), glm::vec3(0.0f, 0.0f, 1.0f));
        transform = glm::scale(transform, glm::vec3(size, 1.0f));
        
        return transform;
      }

      void addBorderQuad(GUIPrimitiveBuilder& builder, const TextureHandle& texture, const glm::vec2& pos, const glm::vec2& size) {
        builder.add(GUIPrimitives::Quad{
          .transform = computeTransform(pos, size, m_rotation),
          .uv = glm::vec4(0.0f, 0.0f, 1.0f, 1.0f),
          .texture = texture,
          .color = m_borderColor,
          .layer = m_layer + 1
        });
      }

    public:
      QuadBuilder(IResourceManager& resourceManager) : m_resourceManager(resourceManager) {};

      QuadBuilder& setTransform(const glm::vec2& position, const glm::vec2& size, float rotation) {
        m_position = position + glm::vec2(size.x/2.0, size.y/2.0);
        m_size = size;
        m_rotation = rotation;

        m_transform = computeTransform(m_position, m_size, m_rotation);
        return *this;
      }

      QuadBuilder& setColor(const Color& color) {
        m_color = color;
        return *this;
      }

      QuadBuilder& setTexture(const TextureHandle& texture, const glm::vec4& uv) {
        m_texture = texture;
        m_uv = uv;
        return *this;
      }

      QuadBuilder& setBorder(const glm::vec4& border, const Color& color) {
        m_border = border;
        m_borderColor = color;

        if (border.x > 0.0f || border.y > 0.0f || border.z > 0.0f || border.w > 0.0f) {
          m_borderEnabled = true;
        } else {
          m_borderEnabled = false;
        }

        return *this;
      }
      
      QuadBuilder& setLayer(int layer) {
        m_layer = layer;
        return *this;
      }

      void build(GUIPrimitiveBuilder& builder) {
        builder.add(GUIPrimitives::Quad{
          .transform = m_transform,
          .uv = m_uv,
          .texture = m_texture,
          .color = m_color,
          .layer = m_layer
        });

        if (m_borderEnabled) {
          const TextureHandle& fallbackTexture = m_resourceManager.get<TextureHandle>(FallbackResources::FALLBACK_TEXTURE_NAME);

          // Border interpretation: m_border = {top, right, bottom, left} (CSS-style)
          const float borderTop = m_border.x;
          const float borderRight = m_border.y;
          const float borderBottom = m_border.z;
          const float borderLeft = m_border.w;

          const float halfWidth = m_size.x * 0.5f;
          const float halfHeight = m_size.y * 0.5f;

          // Create borders without overlaps for clean edges
          // Top border (full width including border extensions)
          if (borderTop > 0.0f) {
            const float topY = m_position.y + halfHeight + borderTop * 0.5f;
            const float topWidth = m_size.x + borderLeft + borderRight;
            addBorderQuad(builder, fallbackTexture, {m_position.x, topY}, {topWidth, borderTop});
          }

          // Bottom border (full width including border extensions)
          if (borderBottom > 0.0f) {
            const float bottomY = m_position.y - halfHeight - borderBottom * 0.5f;
            const float bottomWidth = m_size.x + borderLeft + borderRight;
            addBorderQuad(builder, fallbackTexture, {m_position.x, bottomY}, {bottomWidth, borderBottom});
          }

          // Left border (only content height, no overlap with top/bottom)
          if (borderLeft > 0.0f) {
            const float leftX = m_position.x - halfWidth - borderLeft * 0.5f;
            addBorderQuad(builder, fallbackTexture, {leftX, m_position.y}, {borderLeft, m_size.y});
          }

          // Right border (only content height, no overlap with top/bottom)
          if (borderRight > 0.0f) {
            const float rightX = m_position.x + halfWidth + borderRight * 0.5f;
            addBorderQuad(builder, fallbackTexture, {rightX, m_position.y}, {borderRight, m_size.y});
          }
        }
      }
  };
}

#endif
