#ifndef __IF__MATERIAL_HPP
#define __IF__MATERIAL_HPP

// C++ standard library
#include <variant>
#include <vector>

// Local includes
#include "gpu_handles.hpp"

namespace IronFrost {
  struct GUIMaterial {
    TextureHandle baseColor{0};
  };

  struct BlinnPhongMaterial {
    glm::vec3 ambient{1.0f, 1.0f, 1.0f};
    glm::vec3 diffuse{1.0f, 1.0f, 1.0f};
    glm::vec3 specular{1.0f, 1.0f, 1.0f};

    float shininess{32.0f};

    TextureHandle baseColor{0};
  };

  struct AtlasEntry {
    glm::vec2 uvMin{0.0f, 0.0f};
    glm::vec2 uvMax{1.0f, 1.0f};
  };

  struct PBRMaterial {
    TextureHandle pbrTextureArray{0};

    enum class TextureType {
      BASE_COLOR = 0,
      NORMAL,
      METALLIC,
      ROUGHNESS,
      AO,
      EMISSIVE,
      NUM_TEXTURES
    };
  };

  using Material = std::variant<GUIMaterial, BlinnPhongMaterial, PBRMaterial>;
}

#endif
