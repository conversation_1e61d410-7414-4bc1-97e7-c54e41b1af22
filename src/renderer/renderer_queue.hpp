#ifndef __IF__RENDERER_QUEUE_HPP
#define __IF__RENDERER_QUEUE_HPP

// C++ standard library
#include <tuple>
#include <unordered_map>
#include <vector>
#include <algorithm>

// Third-party libraries
#include <glm/glm.hpp>

// Local includes
#include "gpu_handles.hpp"
#include "instance_data.hpp"
#include "draw_command.hpp"
#include "renderables/renderables.hpp"

namespace IronFrost {
  class RendererQueue {
    private:
      using RenderableObjectWithInstanceData = std::tuple<RenderableObject, InstanceData>;

      std::vector<DrawCommand> m_drawCommands;

      static uint64_t makeSortKey(const RenderableObjectWithInstanceData& renderableObjectWithInstanceData) {
        const auto& [renderableObject, instanceData] = renderableObjectWithInstanceData;

        uint64_t layer = (uint32_t)std::clamp(instanceData.layer, 0, 0xFFFF);
        uint64_t shader = (uint32_t)renderableObject.shaderHandle.programID;

        return (layer << 32) | shader;
      }

    public:
      void submit(const RenderableObjectWithInstanceData& renderableObjectWithInstanceData) {
        const auto& [renderableObject, instanceData] = renderableObjectWithInstanceData;

        m_drawCommands.push_back(DrawCommand{
          .sortKey = makeSortKey(renderableObjectWithInstanceData),
          .shaderHandle = instanceData.shaderOverride.has_value() ? instanceData.shaderOverride.value() : renderableObject.shaderHandle,
          .renderableObject = renderableObject,
          .instanceData = instanceData
        });
      }

      void sort() {
        std::sort(m_drawCommands.begin(), m_drawCommands.end(), [](const DrawCommand& a, const DrawCommand& b) {
          return a.sortKey < b.sortKey;
        });
      }

      auto& items() {
        return m_drawCommands;
      }

      void clear() {
        m_drawCommands.clear(); 
      }

      auto size() {
        return m_drawCommands.size();
      }
  };
}

#endif
