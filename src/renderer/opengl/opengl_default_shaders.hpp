#ifndef __IF__OPENGL_DEFAULT_SHADERS_HPP
#define __IF__OPENGL_DEFAULT_SHADERS_HPP

// C++ standard library
#include <string>

namespace IronFrost {
  namespace DefaultShaders {
    constexpr const char* DEFAULT_VERTEX_SHADER = R"(
        #version 330 core
        layout (location = 0) in vec3 aPos;
        layout (location = 1) in vec2 aTexCoord;
        layout (location = 2) in vec3 aNormal;
        layout (location = 3) in vec3 aTangent;
        layout (location = 4) in vec3 aBitangent;

        out vec2 TexCoord;
        out vec3 Normal;
        out vec3 FragPos;
        out mat3 TBN;

        uniform mat4 model;
        uniform mat4 viewProjection;
        uniform mat3 normalMatrix;

        void main()
        {
            vec4 worldPos = model * vec4(aPos, 1.0);
            FragPos = vec3(worldPos);

            Normal = normalize(normalMatrix * aNormal);
            TBN = mat3(normalMatrix * aTangent, normalMatrix * aBitangent, Normal);
            TexCoord = aTexCoord;
            
            gl_Position = viewProjection * worldPos;
        } 
    )";

    constexpr const char* DEFAULT_FRAGMENT_SHADER = R"(
        #version 330 core
        out vec4 FragColor;

        in vec2 TexCoord;
        in vec3 Normal;
        in vec3 FragPos;
        in mat3 TBN;

        const int MATERIAL_BLINN_PHONG = 0;
        const int MATERIAL_PBR = 1;
        uniform int materialType;  // 0 = Blinn-Phong, 1 = PBR

        struct BlinnPhongMaterial {
            vec3 ambient;
            vec3 diffuse;
            vec3 specular;
            float shininess;
            sampler2D baseColor;
        };
        uniform BlinnPhongMaterial phongMaterial;

        uniform sampler2DArray pbrTextureArray;
        
        struct AmbientLight {
            vec3 color;
            float intensity;
        };
        uniform AmbientLight ambientLight;

        struct DirectionalLight {
            vec3 direction;
            vec3 color;
            float intensity;
        };

        #define MAX_DIRECTIONAL_LIGHTS 4
        uniform int numDirectionalLights;
        uniform DirectionalLight dirLights[MAX_DIRECTIONAL_LIGHTS];

        struct PointLight {
            vec3 position;
            vec3 color;
            float intensity;
            float constant;
            float linear;
            float quadratic;
        };

        #define MAX_POINT_LIGHTS 8
        uniform int numPointLights;
        uniform PointLight pointLights[MAX_POINT_LIGHTS];

        uniform vec3 cameraPosition;
        uniform vec2 uvTiling;

        vec3 calculateAmbientLight(vec3 albedo);
        vec3 calculateDirectionalLights(vec3 normal, vec3 viewDir);
        vec3 calculatePBRDirectionalLights(vec3 normal, vec3 viewDir, vec3 albedo, vec3 F0, float roughness, float metallic);
        vec3 calculatePointLights(vec3 normal, vec3 viewDir);
        vec3 calculatePBRPointLights(vec3 normal, vec3 viewDir, vec3 FragPos, vec3 albedo, vec3 F0, float roughness, float metallic);
        float calculateAttenuation(PointLight light, float distance);
        vec3 calculateBlinnPhong(vec3 normal, vec3 lightDir, vec3 viewDir, vec3 lightColor, float lightIntensity);

        vec3 fresnelSchlick(float cosTheta, vec3 F0);
        float DistributionGGX(vec3 N, vec3 H, float roughness);
        float GeometrySchlickGGX(float NdotV, float roughness);
        float GeometrySmith(vec3 N, vec3 V, vec3 L, float roughness);

        void main() {
            if (materialType != MATERIAL_BLINN_PHONG && materialType != MATERIAL_PBR) {
                FragColor = vec4(1.0, 0.0, 0.0, 1.0);
                return;
            }

            vec3 normal;
            vec4 textureColor;
            vec2 tiledUV = clamp(fract(TexCoord * uvTiling), 0.0, 1.0);

            if (materialType == MATERIAL_PBR) {
                vec3 tangentNormal = texture(pbrTextureArray, vec3(tiledUV, float(1))).rgb;
                tangentNormal = tangentNormal * 2.0 - 1.0; // Convert from [0,1] to [-1,1]
                tangentNormal = normalize(tangentNormal);
                normal = normalize(TBN * tangentNormal);

                textureColor = texture(pbrTextureArray, vec3(tiledUV, float(0)));
            } else {
                normal = normalize(Normal);
                textureColor = texture(phongMaterial.baseColor, tiledUV);
            }
                
            if (textureColor.a == 0.0) {
                FragColor = vec4(1.0, 0.0, 0.0, 1.0);
                return;
            }

            vec3 lighting;
            vec3 viewDir = normalize(cameraPosition - FragPos);
            if (materialType == MATERIAL_BLINN_PHONG) {
                vec3 ambientLight = calculateAmbientLight(phongMaterial.ambient);
                vec3 directionalLights = calculateDirectionalLights(normal, viewDir);
                vec3 pointLights = calculatePointLights(normal, viewDir);

                lighting = ambientLight + directionalLights + pointLights;

                FragColor = vec4(lighting * textureColor.rgb, 1.0);
            } else {
                vec3 albedo = textureColor.rgb;
                float roughness = texture(pbrTextureArray, vec3(tiledUV, float(3))).r;
                float metallic  = texture(pbrTextureArray, vec3(tiledUV, float(2))).r;

                vec3 F0 = vec3(0.04);
                F0 = mix(F0, albedo, metallic);

                vec3 ambientLight = calculateAmbientLight(albedo);
                vec3 directionalLights = calculatePBRDirectionalLights(normal, viewDir, albedo, F0, roughness, metallic);
                vec3 pointLights = calculatePBRPointLights(normal, viewDir, FragPos, albedo, F0, roughness, metallic);

                lighting = ambientLight + directionalLights + pointLights;

                FragColor = vec4(lighting, 1.0);
            }

            // float gamma = 2.2;
            // FragColor.rgb = pow(FragColor.rgb, vec3(1.0/gamma));
        }

        vec3 calculateAmbientLight(vec3 albedo) {
            return ambientLight.color * ambientLight.intensity * albedo;
        }

        vec3 calculateDirectionalLights(vec3 normal, vec3 viewDir) {
            vec3 totalDiffuseSpec = vec3(0.0);
            
            for (int i = 0; i < numDirectionalLights; i++) {
                vec3 lightDir = normalize(-dirLights[i].direction);
                totalDiffuseSpec += calculateBlinnPhong(
                    normal, 
                    lightDir, 
                    viewDir, 
                    dirLights[i].color, 
                    dirLights[i].intensity
                );
            }
            
            return totalDiffuseSpec;
        }

        vec3 calculatePBRDirectionalLights(vec3 normal, vec3 viewDir, vec3 albedo, vec3 F0, float roughness, float metallic) {
            vec3 totalDiffuseSpec = vec3(0.0);

            for (int i = 0; i < numDirectionalLights; i++) {
                vec3 L = normalize(-dirLights[i].direction);
                vec3 H = normalize(viewDir + L);

                float NDF = DistributionGGX(normal, H, roughness);
                float G = GeometrySmith(normal, viewDir, L, roughness);
                vec3 F = fresnelSchlick(max(dot(H, viewDir), 0.0), F0);

                float NdotV = max(dot(normal, viewDir), 0.0);
                float NdotL = max(dot(normal, L), 0.0);
                float denominator = 4.0 * NdotV * NdotL + 0.001;  
                vec3 specular = (NDF * G * F) / denominator;

                vec3 kS = F;
                vec3 kD = vec3(1.0) - kS;
                kD *= (1.0 - metallic);

                vec3 diffuse = kD * albedo / 3.14159265;

                totalDiffuseSpec += (diffuse + specular) * dirLights[i].color * dirLights[i].intensity * NdotL;
            }

            return totalDiffuseSpec;
        }

        vec3 calculatePointLights(vec3 normal, vec3 viewDir) {
            vec3 totalDiffuseSpec = vec3(0.0);
            
            for (int i = 0; i < numPointLights; i++) {
                vec3 lightDir = normalize(pointLights[i].position - FragPos);
                float distance = length(pointLights[i].position - FragPos);
                float attenuation = calculateAttenuation(pointLights[i], distance);
                
                vec3 contribution = calculateBlinnPhong(
                    normal,
                    lightDir,
                    viewDir,
                    pointLights[i].color,
                    pointLights[i].intensity
                );
                
                totalDiffuseSpec += contribution * attenuation;
            }
            
            return totalDiffuseSpec;
        }

        vec3 calculatePBRPointLights(vec3 normal, vec3 viewDir, vec3 FragPos, vec3 albedo, vec3 F0, float roughness, float metallic) {
            vec3 totalDiffuseSpec = vec3(0.0);

            for (int i = 0; i < numPointLights; i++) {
                vec3 L = normalize(pointLights[i].position - FragPos);
                float distance = length(pointLights[i].position - FragPos);
                float attenuation = calculateAttenuation(pointLights[i], distance);

                vec3 H = normalize(viewDir + L);
                float NDF = DistributionGGX(normal, H, roughness);
                float G = GeometrySmith(normal, viewDir, L, roughness);
                vec3 F = fresnelSchlick(max(dot(H, viewDir), 0.0), F0);

                float NdotV = max(dot(normal, viewDir), 0.0);
                float NdotL = max(dot(normal, L), 0.0);
                float denominator = 4.0 * NdotV * NdotL + 0.001;  
                vec3 specular = (NDF * G * F) / denominator;

                vec3 kS = F;
                vec3 kD = vec3(1.0) - kS;
                kD *= (1.0 - metallic);

                vec3 diffuse = kD * albedo / 3.14159265;

                totalDiffuseSpec += (diffuse + specular) * pointLights[i].color * pointLights[i].intensity * attenuation * NdotL;
            }

            return totalDiffuseSpec;
        }

        float calculateAttenuation(PointLight light, float distance) {
            return 1.0 / (
                light.constant + 
                light.linear * distance + 
                light.quadratic * (distance * distance) + 0.0001
            );
        }

        vec3 calculateBlinnPhong(vec3 normal, vec3 lightDir, vec3 viewDir, vec3 lightColor, float lightIntensity) {
            // Diffuse
            float diff = max(dot(normal, lightDir), 0.0);
            vec3 diffuse = diff * lightColor * lightIntensity * phongMaterial.diffuse;
            
            // Specular
            vec3 halfwayDir = normalize(lightDir + viewDir);
            float spec = pow(max(dot(normal, halfwayDir), 0.0), phongMaterial.shininess);
            vec3 specular = spec * lightColor * lightIntensity * phongMaterial.specular;
            
            return diffuse + specular;
        }

        vec3 fresnelSchlick(float cosTheta, vec3 F0) {
            float factor = (1.0 - cosTheta);
            float fresnelFactor = factor * factor * factor * factor * factor;
            return F0 + (1.0 - F0) * fresnelFactor;
        }

        float DistributionGGX(vec3 N, vec3 H, float roughness) {
            float a = roughness * roughness;
            float a2 = a * a;
            float NdotH = max(dot(N, H), 0.0);
            float NdotH2 = NdotH * NdotH;

            float denominator = (NdotH2 * (a2 - 1.0) + 1.0);
            denominator = 3.14159265 * denominator * denominator;

            return a2 / denominator;
        }

        float GeometrySchlickGGX(float NdotV, float roughness) {
            float r = (roughness + 1.0);
            float k = (r * r) / 8.0; // K is tuned for direct lighting

            return NdotV / (NdotV * (1.0 - k) + k);
        }

        float GeometrySmith(vec3 N, vec3 V, vec3 L, float roughness) {
            float NdotV = max(dot(N, V), 0.0);
            float NdotL = max(dot(N, L), 0.0);

            float ggx1 = GeometrySchlickGGX(NdotV, roughness);
            float ggx2 = GeometrySchlickGGX(NdotL, roughness);

            return ggx1 * ggx2;
        }
    )";

    constexpr const char* DEFAULT_POSTPROCESS_VERTEX_SHADER = R"(
        #version 330 core
        layout (location = 0) in vec2 aPos;
        layout (location = 1) in vec2 aTexCoords;

        out vec2 TexCoords;

        void main()
        {
            gl_Position = vec4(aPos.x, aPos.y, 0.0, 1.0); 
            TexCoords = aTexCoords;
        }  
    )";

    constexpr const char* DEFAULT_POSTPROCESS_FRAGMENT_SHADER = R"(
        #version 330 core
        out vec4 FragColor;

        in vec2 TexCoords;

        uniform sampler2D screenTexture;

        void main()
        { 
            FragColor = texture(screenTexture, TexCoords);
        }
    )";

    constexpr const char* DEFAULT_GUI_VERTEX_SHADER = R"(
        #version 330 core

        layout(location = 0) in vec2 aPos;
        layout(location = 1) in vec2 aTexCoord;

        out vec2 TexCoord;

        uniform mat4 model;
        uniform mat4 viewProjection;

        void main()
        {
            TexCoord = aTexCoord;
            gl_Position = viewProjection * model * vec4(aPos, 0.0, 1.0);
        }
    )";

    constexpr const char* DEFAULT_GUI_FRAGMENT_SHADER = R"(
        #version 330 core

        in vec2 TexCoord;
        out vec4 FragColor;

        uniform sampler2D ourTexture;
        uniform vec3 color;
        uniform float alpha;

        void main()
        {
            vec4 texColor = texture(ourTexture, TexCoord);
            FragColor = texColor * vec4(color, alpha);
        }
    )";

    constexpr const char* DEFAULT_TEXT_VERTEX_SHADER = R"(
        #version 330 core

        layout(location = 0) in vec2 aPos;
        layout(location = 1) in vec2 aTexCoord;

        out vec2 TexCoord;

        uniform mat4 model;
        uniform mat4 viewProjection;

        void main()
        {
            TexCoord = aTexCoord;
            gl_Position = viewProjection * model * vec4(aPos, 0.0, 1.0);
        }
    )";

    constexpr const char* DEFAULT_TEXT_FRAGMENT_SHADER = R"(
        #version 330 core

        in vec2 TexCoord;
        out vec4 FragColor;

        uniform sampler2D ourTexture;
        uniform vec3 color;
        uniform float alpha;

        void main()
        {
            vec4 texColor = texture(ourTexture, TexCoord);
            FragColor = vec4(color, texColor.r);
            FragColor *= alpha;
        }
    )";

    constexpr const char* DEFAULT_DEBUG_VERTEX_SHADER = R"(
        #version 330 core
        layout (location = 0) in vec3 aPos;
        layout (location = 1) in vec2 aTexCoord;
        layout (location = 2) in vec3 aNormal;
        layout (location = 3) in vec3 aTangent;
        layout (location = 4) in vec3 aBitangent;

        out vec2 TexCoord;
        out vec3 Normal;
        out vec3 FragPos;

        uniform mat4 model;
        uniform mat4 viewProjection;
        uniform mat3 normalMatrix;

        void main()
        {
            vec4 worldPos = model * vec4(aPos, 1.0);
            FragPos = vec3(worldPos);

            Normal = normalize(normalMatrix * aNormal);
            TexCoord = aTexCoord;
            
            gl_Position = viewProjection * worldPos;
        } 
    )";

    constexpr const char* DEFAULT_DEBUG_FRAGMENT_SHADER = R"(
        #version 330 core
        out vec4 FragColor;

        in vec2 TexCoord;
        in vec3 Normal;
        in vec3 FragPos;

        uniform vec3 cameraPos;
        uniform vec3 color;

        void main() {
            FragColor = vec4(color, 1.0);
        }
    )";
  }
}

#endif
