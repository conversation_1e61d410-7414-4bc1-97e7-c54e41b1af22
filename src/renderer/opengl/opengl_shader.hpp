#ifndef __IF__OPENGL_SHADER_HPP
#define __IF__OPENGL_SHADER_HPP

// C++ standard library
#include <exception>
#include <functional>
#include <string>

// Third-party libraries
#include <glad/glad.h>

namespace IronFrost {
  class OpenGLShader {
    private:
      std::string m_vertexSource;
      std::string m_fragmentSource;

      unsigned int m_vertexID{0};
      unsigned int m_fragmentID{0};

      unsigned int m_programID{0};

      unsigned int compileShader(const std::string& source, GLenum shaderType) {
        unsigned int shaderID = glCreateShader(shaderType);
        const char *sourceCStr = source.c_str();

        glShaderSource(shaderID, 1, &sourceCStr, nullptr);
        glCompileShader(shaderID);

        int success;
        glGetShaderiv(shaderID, GL_COMPILE_STATUS, &success);

        if (!success) {
          char infoLog[512];
          glGetShaderInfoLog(shaderID, 512, nullptr, infoLog);
          throw std::runtime_error(infoLog);
        }

        return shaderID;
      }

      unsigned int linkShaderProgram(unsigned int vertexShader, unsigned int fragmentShader) {
        unsigned int shaderProgram = glCreateProgram();

        glAttachShader(shaderProgram, vertexShader);
        glAttachShader(shaderProgram, fragmentShader);
        glLinkProgram(shaderProgram);

        int success;
        glGetProgramiv(shaderProgram, GL_LINK_STATUS, &success);

        if (!success) {
          char infoLog[512];
          glGetProgramInfoLog(shaderProgram, 512, NULL, infoLog);
          throw std::runtime_error(infoLog);
        }

        return shaderProgram;
      }

    public:
      OpenGLShader(const std::string& vertexSource, const std::string& fragmentSource) : 
        m_vertexSource(vertexSource), m_fragmentSource(fragmentSource) 
      {}

      unsigned int build() {
        m_vertexID = compileShader(m_vertexSource, GL_VERTEX_SHADER);
        m_fragmentID = compileShader(m_fragmentSource, GL_FRAGMENT_SHADER);
        m_programID = linkShaderProgram(m_vertexID, m_fragmentID);

        return m_programID;
      }

      void activeUniforms(std::function<void(const std::string&, int, unsigned int)> callback) {
        int uniformCount;
        glGetProgramiv(m_programID, GL_ACTIVE_UNIFORMS, &uniformCount);

        for (int i = 0; i < uniformCount; i++) {
          char name[256];

          GLsizei length;
          GLint size;
          GLenum uniformType;
          glGetActiveUniform(m_programID, i, sizeof(name), &length, &size, &uniformType, name);
          
          int uniformLocation = glGetUniformLocation(m_programID, name);

          callback(std::string(name), uniformLocation, uniformType);
        }
      }
  };
}

#endif
