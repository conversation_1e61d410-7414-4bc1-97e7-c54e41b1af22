#ifndef __IF__OPENGL_SCREEN_HPP
#define __IF__OPENGL_SCREEN_HPP

// Third-party libraries
#include <glad/glad.h>

// Local includes
#include "../gpu_handles.hpp"
#include "../renderer.hpp"

namespace IronFrost {
  class OpenGLScreen {
    private:
      unsigned int m_VAO{0};
      unsigned int m_VBO{0};

      int m_screenWidth;
      int m_screenHeight;

      void setupQuad() {
        float quadVertices[] = {
          // Positions   // TexCoords
          -1.0f,  1.0f,  0.0f, 1.0f,
          -1.0f, -1.0f,  0.0f, 0.0f,
          1.0f, -1.0f,  1.0f, 0.0f,

          -1.0f,  1.0f,  0.0f, 1.0f,
          1.0f, -1.0f,  1.0f, 0.0f,
          1.0f,  1.0f,  1.0f, 1.0f
        };

        glGenVertexArrays(1, &m_VAO);
        glGenBuffers(1, &m_VBO);

        glBindVertexArray(m_VAO);
        glBindBuffer(GL_ARRAY_BUFFER, m_VBO);
        glBufferData(GL_ARRAY_BUFFER, sizeof(quadVertices), &quadVertices, GL_STATIC_DRAW);

        glEnableVertexAttribArray(0);
        glVertexAttribPointer(0, 2, GL_FLOAT, GL_FALSE, 4 * sizeof(float), (void*)0);
        glEnableVertexAttribArray(1);
        glVertexAttribPointer(1, 2, GL_FLOAT, GL_FALSE, 4 * sizeof(float), (void*)(2 * sizeof(float)));

        glBindBuffer(GL_ARRAY_BUFFER, 0);
        glBindVertexArray(0);
      };

    public:
      OpenGLScreen(int screenWidth, int screenHeight) :
        m_screenWidth(screenWidth),
        m_screenHeight(screenHeight)
      {
        setupQuad();
        resize(screenWidth, screenHeight);
      }

      ~OpenGLScreen() {
        glDeleteVertexArrays(1, &m_VAO);
        glDeleteBuffers(1, &m_VBO);
      }

      void resize(int screenWidth, int screenHeight) {
        m_screenWidth = screenWidth;
        m_screenHeight = screenHeight;

        glViewport(0, 0, static_cast<GLsizei>(m_screenWidth), static_cast<GLsizei>(m_screenHeight));
      }

      void render(const FramebufferHandle& framebufferHandle)  {
        glBindVertexArray(m_VAO);
        glActiveTexture(GL_TEXTURE0);
        glBindTexture(GL_TEXTURE_2D, framebufferHandle.textureID);
        
        glDrawArrays(GL_TRIANGLES, 0, 6);
        
        glBindTexture(GL_TEXTURE_2D, 0);
        glBindVertexArray(0);
      }
  };
}

#endif
