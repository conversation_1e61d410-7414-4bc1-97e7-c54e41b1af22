#ifndef __IF__GPU_HANDLES_HPP
#define __IF__GPU_HANDLES_HPP

// C standard library
#include <cstddef>

// C++ standard library
#include <functional>
#include <string>
#include <tuple>
#include <unordered_map>
#include <vector>

// Third-party libraries
#include <glm/glm.hpp>

// Local includes
#include "../utils/collision_math.hpp"
#include "../utils/hash.hpp"

namespace IronFrost {
  struct MeshHandle {
    unsigned int VAO{0};
    unsigned int numIndices{0};
    unsigned int numVertices{0};

    bool operator==(const MeshHandle &other) const {
      return VAO == other.VAO;
    }
  };
  
  enum class TextureType {
    DIFFUSE,
    ATLAS,
    ARRAY
  };

  struct TextureHandle {
    unsigned int textureID{0};
    TextureType type{TextureType::DIFFUSE};

    bool operator==(const TextureHandle &other) const {
      return textureID == other.textureID;
    }
  };

  struct ModelHandle {
    struct Node {
      glm::mat4 transform{1.0f};

      std::vector<MeshHandle> meshes;
      std::vector<TextureHandle> textures;
      std::vector<Node> children;
    };

    Node rootNode;

    bool operator==(const ModelHandle &other) const {
      for (int i = 0; i < rootNode.meshes.size(); ++i) {
        if (rootNode.meshes[i] != other.rootNode.meshes[i]) {
          return false;
        }

        if (rootNode.textures[i] != other.rootNode.textures[i]) {
          return false;
        }
      }
      return true;
    }
  };

  struct GlyphHandle {
    unsigned int textureID{0};
    glm::vec2 size{0.0f, 0.0f};
    glm::vec2 bearing{0.0f, 0.0f};
    unsigned int advance{0};

    bool operator==(const GlyphHandle &other) const {
      return textureID == other.textureID && size == other.size && bearing == other.bearing && advance == other.advance;
    }
  };

  struct FontHandle {
    unsigned int lineHeight{0};
    std::vector<GlyphHandle> glyphs{128};

    bool operator==(const FontHandle &other) const {
      for (int i = 0; i < glyphs.size(); ++i) {
        if (glyphs[i] != other.glyphs[i]) {
          return false;
        }
      }

      return lineHeight == other.lineHeight;
    }
  };

  struct ShaderHandle {
    unsigned int programID{0};
    std::unordered_map<std::string, std::tuple<int, unsigned int>> activeUniforms;

    std::tuple<int, unsigned int> getUniformLocation(const std::string &name) const {
      auto it = activeUniforms.find(name);
      if (it != activeUniforms.end()) {
        return it->second;
      }

      return std::make_tuple(-1, 0);
    }

    bool operator==(const ShaderHandle& other) const {
      return programID == other.programID;
    }
  };

  struct FramebufferHandle {
    unsigned int framebufferID{0};
    unsigned int textureID{0};

    bool operator==(const FramebufferHandle &other) const {
      return framebufferID == other.framebufferID;
    }
  };
}

namespace std {
  template <>
  struct hash<IronFrost::MeshHandle> {
    std::size_t operator()(IronFrost::MeshHandle const &meshHandle) const noexcept {
      return std::hash<unsigned int>{}(meshHandle.VAO);
    }
  };

  template <>
  struct hash<IronFrost::TextureHandle> {
    std::size_t operator()(IronFrost::TextureHandle const &textureHandle) const noexcept {
      return std::hash<unsigned int>{}(textureHandle.textureID);
    }
  };

  template <>
  struct hash<IronFrost::ModelHandle> {
    std::size_t operator()(IronFrost::ModelHandle const &modelHandle) const noexcept {
      std::size_t hash = 0;

      for (const auto& mesh : modelHandle.rootNode.meshes) {
        hash_combine(hash, std::hash<IronFrost::MeshHandle>{}(mesh));
      }

      for (const auto& texture : modelHandle.rootNode.textures) {
        hash_combine(hash, std::hash<IronFrost::TextureHandle>{}(texture));
      }

      return hash;
    }
  };
  
  template <>
  struct hash<IronFrost::GlyphHandle> {
    std::size_t operator()(IronFrost::GlyphHandle const &glyphHandle) const noexcept {
      return std::hash<unsigned int>{}(glyphHandle.textureID);
    }
  };

  template <>
  struct hash<IronFrost::FontHandle> {
    std::size_t operator()(IronFrost::FontHandle const &fontHandle) const noexcept {
      std::size_t hash = 0;

      hash_combine(hash, fontHandle.lineHeight);

      for (const auto& glyph : fontHandle.glyphs) {
        hash_combine(hash, std::hash<IronFrost::GlyphHandle>{}(glyph));
      }

      return hash;
    }
  };

  template <>
  struct hash<IronFrost::ShaderHandle> {
    std::size_t operator()(IronFrost::ShaderHandle const &shaderHandle) const noexcept {
      return std::hash<unsigned int>{}(shaderHandle.programID);
    }
  };

  template <>
  struct hash<IronFrost::FramebufferHandle> {
    std::size_t operator()(IronFrost::FramebufferHandle const &framebufferHandle) const noexcept {
      return std::hash<unsigned int>{}(framebufferHandle.framebufferID);
    }
  };
}

#endif
