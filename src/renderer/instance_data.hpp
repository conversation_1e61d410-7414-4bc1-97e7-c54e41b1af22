#ifndef __IF__INSTANCE_DATA_HPP
#define __IF__INSTANCE_DATA_HPP

// C++ standard library
#include <optional>

// Local includes
#include "gpu_handles.hpp"
#include "pixel_rect.hpp"

namespace IronFrost {
  struct InstanceData {
    glm::mat4 transform{};
    glm::vec2 uvTiling{1.0f, 1.0f};
    glm::vec3 color{1.0f, 1.0f, 1.0f};

    int layer{0};

    std::optional<ShaderHandle> shaderOverride{std::nullopt};
    std::optional<PixelRect> scissorRect{std::nullopt};
  };
}

#endif
