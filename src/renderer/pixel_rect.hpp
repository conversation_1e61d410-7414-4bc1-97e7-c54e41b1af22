#ifndef __IF__PIXEL_RECT_HPP
#define __IF__PIXEL_RECT_HPP

// C++ standard library
#include <algorithm>

// Third-party libraries
#include <glm/glm.hpp>

namespace IronFrost {
  struct PixelRect {
    int x{0};
    int y{0};
    int width{0};
    int height{0};

    PixelRect() = default;
    PixelRect(int x, int y, int width, int height) : x(x), y(y), width(width), height(height) {}

    static PixelRect intersect(const PixelRect& a, const PixelRect& b) {
      const int x1 = std::max(a.x, b.x);
      const int y1 = std::max(a.y, b.y);
      const int x2 = std::min(a.x + a.width, b.x + b.width);
      const int y2 = std::min(a.y + a.height, b.y + b.height);

      return PixelRect{x1, y1, std::max(0, x2 - x1), std::max(0, y2 - y1)};
    }

    static PixelRect clampToViewport(const PixelRect& rect, const glm::ivec4& viewport) {
      const int x1 = std::clamp(rect.x, viewport.x, viewport.x + viewport.z);
      const int y1 = std::clamp(rect.y, viewport.y, viewport.y + viewport.w);
      const int x2 = std::clamp(rect.x + rect.width, viewport.x, viewport.x + viewport.z);
      const int y2 = std::clamp(rect.y + rect.height, viewport.y, viewport.y + viewport.w);

      return PixelRect{x1, y1, std::max(0, x2 - x1), std::max(0, y2 - y1)};
    }
  };
}

#endif
