#include "physfs_vfs.hpp"

// C++ standard library
#include <exception>
#include <iostream>
#include <memory>
#include <string>
#include <vector>

// Third-party libraries
#include <physfs.h>

namespace IronFrost
{
  PhysFS_VFS::~PhysFS_VFS() {
    PHYSFS_deinit();
  }

  std::unique_ptr<PhysFS_VFS> PhysFS_VFS::createPhysFS() {
    if (PHYSFS_init(nullptr) == 0) {
      std::string error = PHYSFS_getErrorByCode(PHYSFS_getLastErrorCode());
      throw std::runtime_error("Failed to initialize PhysFS: " + error);
    }

    return std::make_unique<PhysFS_VFS>();
  }

  bool PhysFS_VFS::mount(const std::string& path, const std::string& mountPoint, bool append) {
    return PHYSFS_mount(path.c_str(), mountPoint.empty() ? nullptr : mountPoint.c_str(), append ? 1 : 0) != 0;
  }

  bool PhysFS_VFS::unmount(const std::string& path) {
    return PHYSFS_unmount(path.c_str()) != 0;
  }

  bool PhysFS_VFS::exists(const std::string& filePath) const {
    return PHYSFS_exists(filePath.c_str()) != 0;
  }

  std::string PhysFS_VFS::readFile(const std::string& filePath) const {
    PHYSFS_File *file = PHYSFS_openRead(filePath.c_str());

    if (file == nullptr) {
      std::string error = PHYSFS_getErrorByCode(PHYSFS_getLastErrorCode());
      throw std::runtime_error("Failed to open file for reading: " + error);
    }

    PHYSFS_sint64 fileLength = PHYSFS_fileLength(file);
    std::string content(fileLength, '\0');

    PHYSFS_readBytes(file, content.data(), fileLength);
    PHYSFS_close(file);

    return content;
  }

  std::vector<unsigned char> PhysFS_VFS::readFileBinary(const std::string& filePath) const {
    PHYSFS_File *file = PHYSFS_openRead(filePath.c_str());

    if (file == nullptr) {
      std::string error = PHYSFS_getErrorByCode(PHYSFS_getLastErrorCode());
      throw std::runtime_error("Failed to open file for reading: " + error);
    }

    PHYSFS_sint64 fileLength = PHYSFS_fileLength(file);
    std::vector<unsigned char> content(fileLength);

    PHYSFS_readBytes(file, content.data(), fileLength);
    PHYSFS_close(file);

    return content;
  }

  bool PhysFS_VFS::writeFile(const std::string& filePath, const std::string& content) {
    PHYSFS_File *file = PHYSFS_openWrite(filePath.c_str());

    if (file == nullptr) {
      std::string error = PHYSFS_getErrorByCode(PHYSFS_getLastErrorCode());
      throw std::runtime_error("Failed to open file for writing: " + error);
    }

    PHYSFS_sint64 written = PHYSFS_writeBytes(file, content.data(), content.size());
    PHYSFS_close(file);

    return static_cast<std::size_t>(written) == content.size();
  }

  bool PhysFS_VFS::writeFileBinary(const std::string& filePath, const std::vector<unsigned char>& content) {
    PHYSFS_File *file = PHYSFS_openWrite(filePath.c_str());

    if (file == nullptr) {
      std::string error = PHYSFS_getErrorByCode(PHYSFS_getLastErrorCode());
      throw std::runtime_error("Failed to open file for writing: " + error);
    }

    PHYSFS_sint64 written = PHYSFS_writeBytes(file, content.data(), content.size());
    PHYSFS_close(file);

    return static_cast<std::size_t>(written) == content.size();
  }

  std::vector<std::string> PhysFS_VFS::listFiles(const std::string& directory) const {
    char **files = PHYSFS_enumerateFiles(directory.c_str());
    std::vector<std::string> fileList;

    for (char **file = files; *file != nullptr; ++file) {
      fileList.emplace_back(*file);
    }

    PHYSFS_freeList(static_cast<void*>(files));
    return fileList;
  }

  bool PhysFS_VFS::setWriteDirectory(const std::string& path) {
    return PHYSFS_setWriteDir(path.c_str()) != 0;
  }
}
