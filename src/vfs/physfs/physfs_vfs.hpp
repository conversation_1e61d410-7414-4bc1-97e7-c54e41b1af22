#ifndef __IF__PHYSFS_VFS_HPP
#define __IF__PHYSFS_VFS_HPP

// C++ standard library
#include <memory>
#include <string>
#include <vector>

// Local includes
#include "../vfs.hpp"

namespace IronFrost
{
  class PhysFS_VFS : public IVFS {
    public:
      ~PhysFS_VFS() override;

      static std::unique_ptr<PhysFS_VFS> createPhysFS();

      bool mount(const std::string& path, const std::string& mountPoint = std::string(), bool append = true) override;
      bool unmount(const std::string& path) override;

      bool exists(const std::string& filePath) const override;

      std::string readFile(const std::string& filePath) const override;
      std::vector<unsigned char> readFileBinary(const std::string& filePath) const override;

      bool writeFile(const std::string &filePath, const std::string& content) override;
      bool writeFileBinary(const std::string& filePath, const std::vector<unsigned char>& content) override;

      std::vector<std::string> listFiles(const std::string& directory) const override;
      bool setWriteDirectory(const std::string& path) override;
    };
}

#endif
