#ifndef __IF__VFS_HPP
#define __IF__VFS_HPP

// C++ standard library
#include <memory>
#include <string>
#include <vector>

namespace IronFrost {
  class IVFS {
    public:
      virtual ~IVFS() = default;

      static std::unique_ptr<IVFS> create();

      virtual bool mount(const std::string& _path, const std::string& _mountPoint = std::string(), bool _append = true) = 0;
      virtual bool unmount(const std::string& _path) = 0;

      virtual bool exists(const std::string& _filePath) const = 0;

      virtual std::string readFile(const std::string& _filePath) const = 0;
      virtual std::vector<unsigned char> readFileBinary(const std::string& _filePath) const = 0;

      virtual bool writeFile(const std::string& _filePath, const std::string& _content) = 0;
      virtual bool writeFileBinary(const std::string& _filePath, const std::vector<unsigned char>& _content) = 0;

      virtual std::vector<std::string> listFiles(const std::string& _directory) const = 0;
      virtual bool setWriteDirectory(const std::string& _path) = 0;
    };
}

#endif
