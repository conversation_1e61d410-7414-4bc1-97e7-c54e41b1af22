#ifndef __IF__ASSIMP_IO_SYSTEM_HPP
#define __IF__ASSIMP_IO_SYSTEM_HPP

// C++ standard library
#include <iostream>
#include <string>
#include <utility>
#include <vector>

// Third-party libraries
#include <assimp/IOSystem.hpp>

// Local includes
#include "../vfs.hpp"
#include "io_stream.hpp"


namespace IronFrost {
  class VFS_IOSystem : public Assimp::IOSystem {
    private:
      IVFS& m_vfs;
      std::string m_basePath{""};

    public:
      VFS_IOSystem(IVFS& _vfs, const std::string& _basePath = "") : 
        m_vfs(_vfs),
        m_basePath(_basePath)
      {}

      Assimp::IOStream* Open(const char* _filePath, const char* _mode) override {
        std::string fullPath = m_basePath + std::string(_filePath);
        std::vector<unsigned char> data = m_vfs.readFileBinary(fullPath);
        
        std::cout << "VFS_IOSystem::Open " << fullPath << " " << data.size() << std::endl;

        if(data.empty()) {
          return nullptr;
        }

        return new VFS_IOStream(std::move(data));
      }

      void Close(Assimp::IOStream* _stream) override {
        delete _stream;
      }

      bool Exists(const char* _filePath) const override {
        return m_vfs.exists(std::string(_filePath));
      }

      char getOsSeparator() const override { 
        return '/'; 
      }
  };
}

#endif
