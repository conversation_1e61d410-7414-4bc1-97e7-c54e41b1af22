#ifndef __IF__ASSIMP_IO_STREAM_HPP
#define __IF__ASSIMP_IO_STREAM_HPP

// C++ standard library
#include <algorithm>
#include <utility>
#include <vector>

// Third-party libraries
#include <assimp/IOStream.hpp>

namespace IronFrost {
  class VFS_IOStream : public Assimp::IOStream {
    private:
      std::vector<unsigned char> m_buffer;
      size_t m_position = 0;

    public:
      explicit VFS_IOStream(std::vector<unsigned char> _buffer) : 
        m_buffer(std::move(_buffer)) 
      {}

      size_t Read(void* _buffer, size_t _size, size_t _count) override {
        size_t bytesToRead = _size * _count;
        size_t bytesAvailable = m_buffer.size() - m_position;
        size_t bytesToCopy = std::min(bytesToRead, bytesAvailable);

        if (bytesToCopy > 0) {
          memcpy(_buffer, m_buffer.data() + m_position, bytesToCopy);
          m_position += bytesToCopy;
        }

        return bytesToCopy / _size;
      }

      size_t Write(const void* _buffer, size_t _size, size_t _count) override {
        return 0;
      }

      aiReturn Seek(size_t _offset, aiOrigin _origin) override {
        switch (_origin) {
          case aiOrigin_SET:
            m_position = _offset;
            break;
          case aiOrigin_CUR:
            m_position += _offset;
            break;
          case aiOrigin_END:
            m_position = m_buffer.size() - _offset;
            break;
          default:
            return aiReturn_FAILURE;
        }

        m_position = std::clamp(m_position, size_t(0), m_buffer.size());
        return aiReturn_SUCCESS;
      }

      size_t Tell() const override {
        return m_position;
      }

      size_t FileSize() const override {
        return m_buffer.size();
      }

      void Flush() override {}
  };
}

#endif
