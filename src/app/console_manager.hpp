#ifndef __IF__CONSOLE_MANAGER_HPP
#define __IF__CONSOLE_MANAGER_HPP

// Local includes
#include "../events/event_dispatcher.hpp"

namespace IronFrost {
  class Console;
  class GUI;

  class ConsoleManager {
    private:
      GUI& m_globalGUI;
      Console& m_console;

      // Event listener handles for cleanup
      EventListenerHandle m_consoleOpenHandle;
      EventListenerHandle m_consoleCloseHandle;
      EventListenerHandle m_consoleInputBufferHandle;
      EventListenerHandle m_consoleLogHandle;

      void createConsoleWidget();

    public:
      ConsoleManager(GUI& globalGUI, Console& console);
      ~ConsoleManager();

      // Disable copy/move for simplicity
      ConsoleManager(const ConsoleManager&) = delete;
      ConsoleManager& operator=(const ConsoleManager&) = delete;
      ConsoleManager(ConsoleManager&&) = delete;
      ConsoleManager& operator=(ConsoleManager&&) = delete;

      void initialize();
      void registerEventListeners();
  };
}

#endif
