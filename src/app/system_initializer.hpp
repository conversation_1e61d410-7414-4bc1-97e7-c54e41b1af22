#ifndef __IF__SYSTEM_INITIALIZER_HPP
#define __IF__SYSTEM_INITIALIZER_HPP

namespace IronFrost {
  class Application;

  class SystemInitializer {
    public:
      // Static utility class - no instances needed
      SystemInitializer() = delete;
      ~SystemInitializer() = delete;
      SystemInitializer(const SystemInitializer&) = delete;
      SystemInitializer& operator=(const SystemInitializer&) = delete;
      SystemInitializer(SystemInitializer&&) = delete;
      SystemInitializer& operator=(SystemInitializer&&) = delete;

      // Core system initialization methods
      static void registerServices();
      static void registerGlobalEventListeners(Application& application);
  };
}

#endif
