#ifndef __IF__DEBUG_CONFIG_HPP
#define __IF__DEBUG_CONFIG_HPP

// C++ standard library
#include <unordered_map>

// Local includes
#include "../../services/service.hpp"

namespace IronFrost {
  enum class DebugFeature {
    BoundingBoxes,
    NumOfFeatures
  };

  class DebugConfig : public IService {
    private:
      std::unordered_map<DebugFeature, bool> m_flags;

    public:
      DebugConfig() {
        for (int i = 0; i < static_cast<int>(DebugFeature::NumOfFeatures); ++i)
          m_flags[static_cast<DebugFeature>(i)] = false;
      }

      void enable(DebugFeature feature) {
        m_flags[feature] = true;
      }

      void disable(DebugFeature feature) {
        m_flags[feature] = false;
      }

      void toggle(DebugFeature feature) {
        m_flags[feature] = !m_flags[feature];
      }

      bool isEnabled(DebugFeature feature) const {
        auto it = m_flags.find(feature);
        return it != m_flags.end() && it->second;
      }
  };
}

#endif
