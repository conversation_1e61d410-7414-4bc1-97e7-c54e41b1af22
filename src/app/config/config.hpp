#ifndef __IF__CONFIG_HPP
#define __IF__CONFIG_HPP

// C++ standard library
#include <string>
#include <vector>

// Third-party libraries
#include <nlohmann/json.hpp>

using json = nlohmann::json;

namespace IronFrost {
  class AssetsManager;
  class IVFS;
  class IRenderer;
  class IAudioEngine;

  class Config {
    private:
      IVFS& m_vfs;
      json m_globalConfig;

    public:
      explicit Config(IVFS& vfs);

      void loadEssentialResources(AssetsManager& assetsManager);
      void loadGlobalAssets(AssetsManager& assetsManager);

      // Script configuration methods
      std::vector<std::string> getApplicationInitScripts() const;
      std::vector<std::string> getApplicationPerFrameScripts() const;

      // Script content methods (loads actual script content)
      std::vector<std::string> getApplicationInitScriptContents() const;
      std::vector<std::string> getApplicationPerFrameScriptContents() const;
  };
}

#endif
