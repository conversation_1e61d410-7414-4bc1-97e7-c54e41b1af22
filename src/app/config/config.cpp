#include "config.hpp"

// C++ standard library
#include <iostream>
#include <string>
#include <vector>

// Local includes
#include "../../assets/assets_manager.hpp"
#include "../../assets/assets_loader.hpp"
#include "../../assets/assets_manifest.hpp"
#include "../../assets/asset_events.hpp"
#include "../../assets/asset_data_types.hpp"
#include "../../audio/audio_engine.hpp"
#include "../../events/event_dispatcher.hpp"
#include "../../renderer/postprocess_effect.hpp"
#include "../../renderer/renderer.hpp"
#include "../../renderer/resource_manager.hpp"
#include "../../renderer/fallback_resources.hpp"
#include "../../services/service_locator.hpp"
#include "../../vfs/vfs.hpp"

namespace IronFrost {
  Config::Config(IVFS &vfs) : m_vfs(vfs) {
    m_vfs.mount("data/config", "config");
    m_globalConfig = json::parse(m_vfs.readFile("config/global.json"));

    auto mounts = m_globalConfig["vfs"]["mount"];

    for (json::iterator it = mounts.begin(); it != mounts.end(); ++it) {
      auto mount = it.value();
      m_vfs.mount(mount["path"], mount["mountPoint"]);
    }
  }

  void Config::loadEssentialResources(AssetsManager& assetsManager) {
    std::cout << "Loading essential resources..." << std::endl;

    EventDispatcher& eventDispatcher = ServiceLocator::getService<EventDispatcher>();
    
    // Load default quad
    std::unique_ptr<MeshData> quadMeshData = assetsManager.createPrimitive(PrimitiveType::QUAD);
    eventDispatcher.dispatch<LoadMeshEvent>(FallbackResources::DEFAULT_QUAD_NAME, *quadMeshData);

    // Load fallback mesh
    std::unique_ptr<MeshData> fallbackMeshData = assetsManager.createPrimitive(PrimitiveType::CUBE);
    eventDispatcher.dispatch<LoadMeshEvent>(FallbackResources::FALLBACK_MESH_NAME, *fallbackMeshData);
    eventDispatcher.dispatch<LoadMeshEvent>(FallbackResources::DEBUG_CUBE_NAME, *fallbackMeshData);

    // Load fallback texture
    std::unique_ptr<ImageData> fallbackImageData = assetsManager.loadImage("config/1x1-ffffffff.png");
    eventDispatcher.dispatch<LoadTextureEvent>(FallbackResources::FALLBACK_TEXTURE_NAME, *fallbackImageData);

    // Create fallback material
    auto fallbackMaterialData = std::make_unique<MaterialData>(BlinnPhongMaterialData{
      glm::vec3(1.0f), // ambient
      glm::vec3(1.0f), // diffuse
      glm::vec3(1.0f), // specular
      32.0f,           // shininess
      FallbackResources::FALLBACK_TEXTURE_NAME // textureName
    });
    const MaterialData& storedFallbackMaterial = assetsManager.storeMaterial(FallbackResources::FALLBACK_MATERIAL_NAME, std::move(fallbackMaterialData));
    eventDispatcher.dispatch<LoadMaterialEvent>(FallbackResources::FALLBACK_MATERIAL_NAME, storedFallbackMaterial);

    // Create fallback model
    ModelData fallbackModelData;
    fallbackModelData.rootNode.meshes.emplace_back(std::move(fallbackMeshData));
    fallbackModelData.rootNode.textures.emplace_back(std::move(fallbackImageData));
    eventDispatcher.dispatch<LoadModelEvent>(FallbackResources::FALLBACK_MODEL_NAME, fallbackModelData);
    
    std::cout << "Essential resources loaded" << std::endl;
  }

  void Config::loadGlobalAssets(AssetsManager& assetsManager) {
    std::cout << " --- Loading global assets" << '\n';

    AssetsManifest manifest(m_vfs);
    manifest.loadFromFile("global_assets.json");

    AssetsLoader assetsLoader(assetsManager);

    for (const auto& assetInfo : manifest) {
      std::cout << "Loading global asset: " << assetInfo.config["name"] << std::endl;
      assetsLoader.load(assetInfo);
    }

    std::cout << " --- Global assets loaded" << '\n';
  }

  std::vector<std::string> Config::getApplicationInitScripts() const {
    std::vector<std::string> scripts;

    if (m_globalConfig.contains("scripts") &&
        m_globalConfig["scripts"].contains("application") &&
        m_globalConfig["scripts"]["application"].contains("init")) {

      for (const auto& script : m_globalConfig["scripts"]["application"]["init"]) {
        scripts.push_back(script.get<std::string>());
      }
    }

    return scripts;
  }

  std::vector<std::string> Config::getApplicationPerFrameScripts() const {
    std::vector<std::string> scripts;

    if (m_globalConfig.contains("scripts") &&
        m_globalConfig["scripts"].contains("application") &&
        m_globalConfig["scripts"]["application"].contains("perFrame")) {

      for (const auto& script : m_globalConfig["scripts"]["application"]["perFrame"]) {
        scripts.push_back(script.get<std::string>());
      }
    }

    return scripts;
  }

  std::vector<std::string> Config::getApplicationInitScriptContents() const {
    std::vector<std::string> scriptContents;
    auto scriptPaths = getApplicationInitScripts();

    for (const auto& scriptPath : scriptPaths) {
      try {
        std::string scriptContent = m_vfs.readFile(scriptPath);
        scriptContents.push_back(scriptContent);
      } catch (const std::exception& e) {
        std::cout << "Error loading init script " << scriptPath << ": " << e.what() << '\n';
      }
    }

    return scriptContents;
  }

  std::vector<std::string> Config::getApplicationPerFrameScriptContents() const {
    std::vector<std::string> scriptContents;
    auto scriptPaths = getApplicationPerFrameScripts();

    for (const auto& scriptPath : scriptPaths) {
      try {
        std::string scriptContent = m_vfs.readFile(scriptPath);
        scriptContents.push_back(scriptContent);
      } catch (const std::exception& e) {
        std::cout << "Error loading per-frame script " << scriptPath << ": " << e.what() << '\n';
      }
    }

    return scriptContents;
  }
}
