# Contributing to IronFrost Game Engine

Thank you for your interest in contributing to IronFrost! This document provides guidelines and information for contributors.

## Table of Contents

- [Code of Conduct](#code-of-conduct)
- [Getting Started](#getting-started)
- [Development Setup](#development-setup)
- [Coding Standards](#coding-standards)
- [Contribution Process](#contribution-process)
- [Testing Guidelines](#testing-guidelines)
- [Documentation](#documentation)
- [Issue Reporting](#issue-reporting)

## Code of Conduct

We are committed to providing a welcoming and inclusive environment for all contributors. Please be respectful and professional in all interactions.

## Getting Started

### Prerequisites

Before contributing, ensure you have:
- C++20 compatible compiler (MSVC 2022, GCC 11+, or Clang 14+)
- CMake 3.29.0 or higher
- vcpkg for dependency management
- Git for version control

### Development Setup

1. **Fork and Clone**
   ```bash
   git clone https://github.com/yourusername/IronFrost.git
   cd IronFrost
   ```

2. **Set up vcpkg**
   ```bash
   # Install vcpkg if not already installed
   git clone https://github.com/Microsoft/vcpkg.git
   cd vcpkg
   ./bootstrap-vcpkg.sh  # Linux/macOS
   .\bootstrap-vcpkg.bat # Windows
   ./vcpkg integrate install
   ```

3. **Configure Environment**
   ```bash
   export VCPKG_ROOT=/path/to/vcpkg  # Linux/macOS
   set VCPKG_ROOT=C:\path\to\vcpkg   # Windows
   ```

4. **Build the Project**
   ```bash
   cmake --preset Debug
   cmake --build build/Debug
   ```

5. **Install Development Tools**
   - **clang-format**: For code formatting
   - **clang-tidy**: For static analysis
   - **Doxygen**: For documentation generation

## Coding Standards

### C++ Style Guidelines

#### Naming Conventions
- **Classes**: PascalCase (`AssetsManager`, `EventDispatcher`)
- **Functions/Methods**: camelCase (`loadShader`, `processEvents`)
- **Variables**: camelCase (`deltaTime`, `isLoaded`)
- **Constants**: UPPER_SNAKE_CASE (`MAX_TEXTURE_SIZE`)
- **Private Members**: m_ prefix (`m_window`, `m_renderer`)
- **Namespaces**: PascalCase (`IronFrost`)

#### Code Style
- **Indentation**: 2 spaces (no tabs)
- **Line Length**: Maximum 120 characters
- **Braces**: Opening brace on same line for functions, new line for classes
- **Headers**: Use `#pragma once` instead of include guards

#### Example:
```cpp
namespace IronFrost {
  class ExampleClass {
    private:
      int m_privateVariable;
      
    public:
      ExampleClass();
      ~ExampleClass();
      
      void doSomething(int parameter);
      bool isValid() const;
  };
  
  void ExampleClass::doSomething(int parameter) {
    if (parameter > 0) {
      m_privateVariable = parameter;
    }
  }
}
```

### File Organization
- **Headers**: `.hpp` extension
- **Source**: `.cpp` extension
- **Include order**: System headers, third-party headers, project headers
- **Forward declarations**: Use when possible to reduce compilation time

### Memory Management
- **Smart Pointers**: Prefer `std::unique_ptr` and `std::shared_ptr`
- **RAII**: Use Resource Acquisition Is Initialization pattern
- **Avoid**: Raw pointers for ownership, manual memory management

### Error Handling
- **Exceptions**: Use for exceptional circumstances
- **Return Values**: Use for expected error conditions
- **Logging**: Use the engine's logging system for debugging

## Contribution Process

### 1. Issue First
- Check existing issues before creating new ones
- For new features, create an issue to discuss the proposal
- For bugs, provide detailed reproduction steps

### 2. Branch Strategy
- Create feature branches from `main`
- Use descriptive branch names: `feature/asset-streaming`, `fix/memory-leak`
- Keep branches focused on single features or fixes

### 3. Commit Guidelines
- Write clear, descriptive commit messages
- Use present tense: "Add feature" not "Added feature"
- Reference issues: "Fix memory leak in AssetManager (#123)"
- Keep commits atomic and focused

### 4. Pull Request Process
1. **Before Submitting**:
   - Ensure code compiles without warnings
   - Run existing tests
   - Add tests for new functionality
   - Update documentation if needed
   - Format code with clang-format

2. **Pull Request Description**:
   - Clearly describe the changes
   - Reference related issues
   - Include screenshots for UI changes
   - List any breaking changes

3. **Review Process**:
   - Address reviewer feedback promptly
   - Keep discussions constructive
   - Update PR based on feedback

## Testing Guidelines

### Unit Tests
- Write tests for new functionality
- Use descriptive test names
- Test both success and failure cases
- Mock external dependencies

### Integration Tests
- Test component interactions
- Verify end-to-end functionality
- Test with realistic data

### Performance Tests
- Benchmark critical paths
- Monitor memory usage
- Test with large datasets

### Running Tests
```bash
# Build and run tests
cmake --build build/Debug --target test
ctest --test-dir build/Debug
```

## Documentation

### Code Documentation
- Document all public APIs with Doxygen comments
- Include parameter descriptions and return values
- Provide usage examples for complex functions

### Example:
```cpp
/**
 * @brief Loads a shader from the specified file path
 * @param name Unique identifier for the shader
 * @param path File path to the shader source
 * @return Reference to the loaded shader data
 * @throws std::runtime_error if shader compilation fails
 * 
 * @code
 * auto& shader = assets.loadShader("basic", "shaders/basic.glsl");
 * @endcode
 */
const ShaderData& loadShader(StringID name, const std::string& path);
```

### README Updates
- Update README.md for new features
- Include usage examples
- Update build instructions if needed

### Generate Documentation
```bash
# Generate API documentation
cmake --build build/Debug --target docs
```

## Issue Reporting

### Bug Reports
Include:
- Operating system and version
- Compiler and version
- Steps to reproduce
- Expected vs actual behavior
- Relevant log output
- Minimal code example if applicable

### Feature Requests
Include:
- Clear description of the feature
- Use cases and benefits
- Proposed API or interface
- Implementation considerations

### Performance Issues
Include:
- Profiling data
- System specifications
- Reproduction steps
- Performance expectations

## Development Tips

### Building Efficiently
- Use parallel builds: `cmake --build build/Debug --parallel`
- Use ccache for faster rebuilds
- Build only changed targets when possible

### Debugging
- Use the engine's logging system
- Enable debug builds for development
- Use appropriate debugging tools (gdb, Visual Studio debugger)

### Code Quality
- Run static analysis regularly
- Use sanitizers during development
- Profile performance-critical code

## Getting Help

- **Documentation**: Check the generated API docs
- **Issues**: Search existing issues for solutions
- **Discussions**: Use GitHub Discussions for questions
- **Code Review**: Ask for feedback on complex changes

## Recognition

Contributors will be acknowledged in:
- CONTRIBUTORS.md file
- Release notes for significant contributions
- Project documentation

Thank you for contributing to IronFrost Game Engine!
