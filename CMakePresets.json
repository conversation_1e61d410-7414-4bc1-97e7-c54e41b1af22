{"version": 6, "configurePresets": [{"name": "Debug", "displayName": "Debug", "binaryDir": "build/Debug", "cacheVariables": {"CMAKE_TOOLCHAIN_FILE": "$env{VCPKG_ROOT}/scripts/buildsystems/vcpkg.cmake", "CMAKE_BUILD_TYPE": "Debug"}}, {"name": "Release", "displayName": "Release", "binaryDir": "build/Release", "cacheVariables": {"CMAKE_TOOLCHAIN_FILE": "$env{VCPKG_ROOT}/scripts/buildsystems/vcpkg.cmake", "CMAKE_BUILD_TYPE": "Release"}}], "buildPresets": [{"name": "Debug", "displayName": "Debug", "configurePreset": "Debug", "configuration": "Debug"}, {"name": "Release", "displayName": "Release", "configurePreset": "Release", "configuration": "Release"}]}