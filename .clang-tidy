# clang-tidy configuration for IronFrost Game Engine

Checks: >
  bugprone-*,
  cppcoreguidelines-*,
  modernize-*,
  performance-*,
  readability-*,
  -clang-analyzer-*,
  -bugprone-easily-swappable-parameters,
  -bugprone-reserved-identifier,
  -cppcoreguidelines-avoid-magic-numbers,
  -cppcoreguidelines-pro-bounds-pointer-arithmetic,
  -cppcoreguidelines-pro-bounds-array-to-pointer-decay,
  -cppcoreguidelines-pro-type-member-init,
  -cppcoreguidelines-avoid-c-arrays,
  -cppcoreguidelines-non-private-member-variables-in-classes,
  -cppcoreguidelines-init-variables,
  -cppcoreguidelines-pro-type-union-access,
  -modernize-avoid-c-arrays,
  -modernize-use-trailing-return-type,
  -modernize-use-nodiscard,
  -readability-magic-numbers,
  -readability-named-parameter,
  -readability-identifier-length,
  -readability-convert-member-functions-to-static

WarningsAsErrors: ''

HeaderFilterRegex: '^src/.*\.(hpp|h)$'

FormatStyle: file

CheckOptions:
  # Naming conventions
  - key: readability-identifier-naming.ClassCase
    value: CamelCase
  - key: readability-identifier-naming.StructCase
    value: CamelCase
  - key: readability-identifier-naming.FunctionCase
    value: camelBack
  - key: readability-identifier-naming.VariableCase
    value: camelBack
  - key: readability-identifier-naming.ClassMemberCase
    value: camelBack
  - key: readability-identifier-naming.ClassMemberPrefix
    value: m_
  - key: readability-identifier-naming.PrivateMemberPrefix
    value: m_
  - key: readability-identifier-naming.ParameterCase
    value: camelBack

  # Function size limits (relaxed)
  - key: readability-function-size.LineThreshold
    value: 150
  - key: readability-function-size.StatementThreshold
    value: 1000
  - key: readability-function-size.ParameterThreshold
    value: 15
