-- Mouse Camera Control Example
-- This script demonstrates how to use the new relative mouse position functionality
-- for camera controls in Lua

-- Camera state
local camera = {
    yaw = 0.0,
    pitch = 0.0,
    sensitivity = 0.1,
    max_pitch = 89.0
}

-- Mouse state tracking
local mouse_captured = false

function init()
    print("Mouse Camera Example initialized")
    print("Press 'C' to capture/release mouse for camera control")
    print("Move mouse to look around when captured")
    
    -- Set up key callback for mouse capture toggle
    local keyboard = window:getKeyboard()
    keyboard:setCallback(KeyType.C, function()
        toggle_mouse_capture()
    end)
end

function toggle_mouse_capture()
    mouse_captured = not mouse_captured
    
    if mouse_captured then
        print("Mouse captured - camera control enabled")
        -- Reset relative position to avoid sudden jumps
        local mouse = window:getMouse()
        mouse:resetRelativePosition()
    else
        print("Mouse released - camera control disabled")
    end
end

function update(deltaTime)
    if not mouse_captured then
        return
    end
    
    local mouse = window:getMouse()
    
    -- Get relative mouse movement
    local rel_x = mouse:getRelativeX()
    local rel_y = mouse:getRelativeY()
    
    -- Only update camera if there's actual movement
    if rel_x ~= 0.0 or rel_y ~= 0.0 then
        update_camera(rel_x, rel_y)
        
        -- Reset relative position after processing
        mouse:resetRelativePosition()
    end
end

function update_camera(delta_x, delta_y)
    -- Update yaw (horizontal rotation)
    camera.yaw = camera.yaw + (delta_x * camera.sensitivity)
    
    -- Update pitch (vertical rotation) with clamping
    camera.pitch = camera.pitch - (delta_y * camera.sensitivity)  -- Invert Y for natural feel
    
    -- Clamp pitch to prevent over-rotation
    if camera.pitch > camera.max_pitch then
        camera.pitch = camera.max_pitch
    elseif camera.pitch < -camera.max_pitch then
        camera.pitch = -camera.max_pitch
    end
    
    -- Wrap yaw to keep it in 0-360 range
    if camera.yaw >= 360.0 then
        camera.yaw = camera.yaw - 360.0
    elseif camera.yaw < 0.0 then
        camera.yaw = camera.yaw + 360.0
    end
    
    -- Print camera state for debugging
    print(string.format("Camera - Yaw: %.2f°, Pitch: %.2f°", camera.yaw, camera.pitch))
end

function render()
    -- In a real application, you would use the camera.yaw and camera.pitch
    -- to set up your view matrix for rendering
    
    -- Example of how you might use the camera values:
    -- local view_matrix = create_view_matrix(camera.yaw, camera.pitch, camera_position)
    -- renderer:setViewMatrix(view_matrix)
end

-- Utility function to demonstrate mouse position access
function debug_mouse_info()
    local mouse = window:getMouse()
    
    print("=== Mouse Debug Info ===")
    print(string.format("Absolute Position: (%.2f, %.2f)", mouse:getX(), mouse:getY()))
    print(string.format("Relative Position: (%.2f, %.2f)", mouse:getRelativeX(), mouse:getRelativeY()))
    print(string.format("Left Button: %s", mouse:isButtonPressed(MouseButtonType.LEFT) and "Pressed" or "Released"))
    print(string.format("Right Button: %s", mouse:isButtonPressed(MouseButtonType.RIGHT) and "Pressed" or "Released"))
    print(string.format("Middle Button: %s", mouse:isButtonPressed(MouseButtonType.MIDDLE) and "Pressed" or "Released"))
    print("========================")
end

-- Example of advanced mouse handling
function handle_mouse_buttons()
    local mouse = window:getMouse()
    
    -- Right click to print debug info
    if mouse:isButtonPressed(MouseButtonType.RIGHT) then
        debug_mouse_info()
    end
    
    -- Middle click to reset camera
    if mouse:isButtonPressed(MouseButtonType.MIDDLE) then
        camera.yaw = 0.0
        camera.pitch = 0.0
        print("Camera reset to default position")
    end
end

-- Main update function that would be called by the engine
function main_update(deltaTime)
    update(deltaTime)
    handle_mouse_buttons()
end

-- Initialize the example
init()
